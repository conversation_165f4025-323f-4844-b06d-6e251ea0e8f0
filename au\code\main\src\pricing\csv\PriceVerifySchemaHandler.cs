using lumen.csv;
using System.Collections.Generic;
using System.Text.RegularExpressions;

namespace lep.pricing.csv
{
	public class PriceVerifySchemaHandler : BaseHandler
    {
        protected string fileType;
        protected string[] headers;
        protected Regex[] regexs;
        private bool requireAllColumns = true;
        private int rowNumber = 0;
        private bool seenFirstRow = false;

        public PriceVerifySchemaHandler()
            : base()
        {
            fileType = "Spec Type File ";
            headers = new string[]
            {
                "Job Type", "Size", "Colour", "Cello", "Stock", "Page", "Required", "Qty", "Price", /* "MYOB",*/ "PrintType"
            };
            regexs = new Regex[]
            {
                new Regex(@"^(\w+)||(\d+)$"),
                new Regex(@"^(\w+)||(\d+)$"),
                new Regex(@"^(\d+)||\-$"),
                new Regex(@"^(\w+)||(\d+)$"),
                new Regex(@"^(\w+)||(\d+)$"),
                new Regex(@"^(\d+)||\-$"),
                new Regex(@"^(\w+)||\-$"),
                new Regex(@"^(\d+)||\-$"),
                new Regex(@"^(\d+)||\-$"),
                //new Regex(@"^(\w+)||\-$"),
                new Regex(@"^[DOWN]{1}$"),
            };
        }

        public bool RequireAllColumns
        {
            set { requireAllColumns = value; }
        }

        public override void StartDocument()
        {
            rowNumber = 0;
            seenFirstRow = false;
        }

        public override void EndDocument()
        {
        }

        public override void RowData(string[] values)
        {
            if (!seenFirstRow)
            {
                seenFirstRow = true;

                if (requireAllColumns && headers.Length != values.Length)
                {
                    throw new CsvParseException(
                        string.Format(fileType + "Input header does not have the correct number of columns: {0}",
                            headers.Length));
                }

                var columns = new List<string>(headers);

                for (var i = 0; i < values.Length; i++)
                {
                    if (!columns.Contains(values[i].ToLower()))
                    {
                        throw new CsvParseException(string.Format(fileType + "Column does not exist: {0}", values[i]));
                    }
                }
            }
            else
            {
                rowNumber++;
                for (var i = 0; i < values.Length; i++)
                {
                    if (!regexs[i].Match(values[i]).Success && "" != values[i])
                    {
                        throw new CsvParseException(string.Format(fileType + "Input row {0}:{1} is invalid value.",
                            rowNumber, headers[i]));
                    }
                }
                if (requireAllColumns && headers.Length != values.Length)
                {
                    throw new CsvParseException(
                        string.Format(fileType + "Input row {0} does not have the correct number of columns: {1}",
                            rowNumber, headers.Length.ToString()));
                }
            }

            base.RowData(values);
        }
    }
}
