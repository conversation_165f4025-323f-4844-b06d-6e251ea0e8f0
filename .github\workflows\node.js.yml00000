# This workflow will do a clean installation of node dependencies, cache/restore them, build the source code and run tests across different versions of node
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-nodejs-with-github-actions

name: Node.js CI

on:
  push:
    branches: [ "develop" ]
  pull_request:
    branches: [ "develop" ]

jobs:
  build:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: au/LepCore

        # See supported Node.js release schedule at https://nodejs.org/en/about/releases/
    steps:
      - uses: actions/checkout@v3
      - name: Use Node.js 8.17.0
        uses: actions/setup-node@v3
        with:
          node-version: 8.17.0
          cache: 'npm'
          cache-dependency-path: au/LepCore/package-lock.json
      - run: git config --global url.https://github.com/.insteadOf git://github.com/
      - run: npm ci
      - run: gulp release
