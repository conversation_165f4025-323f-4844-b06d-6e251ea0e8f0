using System.Drawing;
using System.Drawing.Printing;

namespace lep.despatch.impl.label
{
    public class ImmediateFreightMeLabel : BasePigeonHoleLabel
    {
        public ImmediateFreightMeLabel()
        {
            WatermarkStamp = "  ONE JOB\nFREIGHT NOW";
            WatermarkStampColor = Color.Green;
        }

        protected override void PrintExtraThingsAfterGrid(Graphics g)
        {
            DrawPackingDetailsDispatchBoxes(g);
        }

        protected override void OnPrintPage(PrintPageEventArgs e)
        {
            base.OnPrintPage(e);
        }
    }
}