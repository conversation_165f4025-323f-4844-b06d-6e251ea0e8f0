﻿using lep.job;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace lep.printPortal
{
	/// <summary>
	/// PrintPortalSettings of a Customers Print portal
	/// </summary>
	public class PrintPortalSettings
	{
		public virtual int? Version { get; set; }

		/// <summary>
		/// Whether or not AAC is run on this print portal
		/// </summary>
		public virtual bool IsAacEnabled { get; set; }

		/// <summary>
		/// Url of a css file to customise look and feel of a print portal site
		/// </summary>
		public virtual string CustomCssURL { get; set; }

		/// <summary>
		/// PayPal Client Id where the curstomer receives paypal payment
		/// </summary>
		public virtual String PayPalClientId { get; set; }

		/// <summary>
		/// Stripe details where the user receives money
		/// </summary>
		public virtual string StripePublishableKey { get; set; }

		public virtual string StripeRestrictedChargeKey { get; set; }

		/// <summary>
		/// Templates that not offerred in this print portal
		/// </summary>
		public virtual List<int> DeniedTemplates { get; set; } = new List<int>();

		/// <summary>
		/// Stocks and Sizes availale in Allowed Templates
		/// </summary>
		public virtual List<AllowedProduct> WhiteLabelAllowedProducts { get; set; } = new List<AllowedProduct>();


		/// <summary>
		/// Category markup or price range markup
		/// </summary>
		public virtual PricingModel? PricingModel { get; set; }

		public virtual List<CategoryMarkup> CategoryMarkups { get; set; } = new List<CategoryMarkup>();
		public virtual decimal? WhiteLabelGlobalMarkup { get; set; }
		public virtual List<PriceRangeMarkup> PriceRangeMarkups { get; set; } = new List<PriceRangeMarkup>();

		public virtual FavouriteCategoryMarkups FavouriteCategoryMarkups { get; set; } = new FavouriteCategoryMarkups();
		public virtual FavouritePriceRangeMarkups FavouritePriceRangeMarkups { get; set; } = new FavouritePriceRangeMarkups();
	}

	public class AllowedProduct 
	{
		[JsonProperty("T")]
		public int JobOptionId { get; set; }

		[JsonProperty("S")]
		public int StockId { get; set; }

		[JsonProperty("P")]
		public int PaperSizeId { get; set; }
	}

	public class ListOfAllowedProducts : List<AllowedProduct> 
	{
		public override bool Equals(object obj)
		{
			if (obj is ListOfAllowedProducts other)
			{
				return this.SequenceEqual(other);
			}
			return false;
		
		}
	}

	public static class PrintPortalSettingsExts
	{
		public static decimal? MarkupForJob(this PrintPortalSettings _, IJob job, StringBuilder log)
		{
			var Any = "Any";
			bool matchesSomeMarkup(CategoryMarkup m, IJob j)
			{
				try
				{
					if (m.Template.Id != j.Template.Id)
						return false;

					//if (!(m.PrintType == j.PrintType.ToString() || m.PrintType == j.PrintType.ToDescription() || m.PrintType == Any || m.PrintType == null))
					//	return false;

					if (!(m.FinishedSize == j.FinishedSize.PaperSize.Name || m.FinishedSize == Any || m.FinishedSize == null))
						return false;

					var cello = (j.FinalFrontCelloglaze.ToString() + "/" + j.FinalBackCelloglaze.ToString());
					if (!(m.Cello == cello || m.Cello == null || m.Cello == Any))
						return false;

					if (!(j.Quantity >= m.QtyMin && j.Quantity <= m.QtyMax))
						return false;

					return true;

					//return (m.Template.Id == j.Template.Id &&
					//(m.PrintType == j.PrintType.ToString() || m.PrintType == j.PrintType.ToDescription() || m.PrintType == Any || m.PrintType == null) &&
					//(m.FinishedSize == j.FinishedSize.PaperSize.Name || m.FinishedSize == Any || m.FinishedSize == null) &&
					//(m.Cello == (j.FrontCelloglaze.ToString() + "/" + j.BackCelloglaze.ToString()) || m.Cello == null || m.Cello == Any) &&
					//(j.Quantity >= m.QtyMin && j.Quantity <= m.QtyMax));
				}
				catch (Exception ex)
				{
					return false;
				}
			}

			bool matchesSomePriceRange(PriceRangeMarkup m, IJob j)
			{
				try
				{
					var p = Double.Parse(j.Price);
					return (p >= m.PriceFrom && p <= m.PriceTo);
				}
				catch (Exception ex)
				{
					return false;
				}
			}

			decimal? generalMarkup()
			{
				if (_.WhiteLabelGlobalMarkup != null && _.WhiteLabelGlobalMarkup != 0)
				{
					log.AppendLine($"Found general markup. {_.WhiteLabelGlobalMarkup}");
					return _.WhiteLabelGlobalMarkup;
				}
				return null;
			}

			if (_.PricingModel == null || _.PricingModel == PricingModel.Unspecified)
			{
				log.AppendLine(" has no pricing Pricing model");
				return generalMarkup();
			}
			else if (_.PricingModel == PricingModel.CategoryMarkup)
			{
				log.AppendLine("Pricing model CategoryMarkup. ");
				var categoryMarkups = _.CategoryMarkups?.Where(__ => matchesSomeMarkup(__, job)).ToList();

				var sorter = new CategoryMarkupWithMoreSpecifityComesFirst();
				categoryMarkups.Sort(sorter);

				var categoryMarkup = categoryMarkups.FirstOrDefault();

				if (categoryMarkup == null)
				{
					log.AppendLine("None found. ");
				}
				else
				{
					log.AppendLine($"Mark up = {categoryMarkup.Markup}. ");
				}

				return categoryMarkup?.Markup ?? generalMarkup();
			}
			else if (_.PricingModel == PricingModel.PriceMarkup)
			{
				log.AppendLine("Pricing model PriceMarkup. ");

				var priceRangeMarkup = _.PriceRangeMarkups?.Where(__ => matchesSomePriceRange(__, job)).FirstOrDefault();

				if (priceRangeMarkup == null)
				{
					log.AppendLine("None found. ");
				}
				else
				{
					log.AppendLine($"Mark up = {priceRangeMarkup.Markup}. ");
				}

				return priceRangeMarkup?.Markup ?? generalMarkup();
			}

			return null;
		}
	}
}
