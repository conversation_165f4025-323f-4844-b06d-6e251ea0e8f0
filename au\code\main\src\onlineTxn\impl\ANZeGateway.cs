﻿// using Serilog;
// using System;
// using System.Collections.Generic;
// using System.Net;
// using System.Reflection;
// using System.Text;
// using System.Web;

// namespace lep.onlineTxn.impl
// {
// 	public class ANZeGateway
// 	{
// 		#region Constants

// 		private const string STR_vpcURL = @"https://migs.mastercard.com.au/vpcdps";

// 		#endregion Constants

// 		#region Readonly

// 		// private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

// 		#endregion Readonly

// 		#region Properties

// 		public String FailureDetailedReason
// 		{
// 			get { return getResponseDescription(Response["vpc_Message"].ToString()); }
// 		}

// 		public String FailureReason
// 		{
// 			get { return getResponseDescription(Response["vpc_TxnResponseCode"].ToString()); }
// 		}

// 		public IDictionary<string, string> Response { get; } = new Dictionary<string, string>();

// 		#endregion Properties

// 		#region Public Methods

// 		public IOnlineTxn AttemptPayment(IDictionary<string, string> paramsToANZ)
// 		{
// 			var postData = String.Empty;
// 			try
// 			{
// 				foreach (var kvp in paramsToANZ)
// 				{
// 					postData += HttpUtility.UrlEncode(kvp.Key) + "=" + HttpUtility.UrlEncode(kvp.Value) + "&";
// 				}

// 				var ANZeGatewayClient = new WebClient();
// 				ANZeGatewayClient.Headers.Add("Content-Type", "application/x-www-form-urlencoded");
// 				var response = ANZeGatewayClient.UploadData(STR_vpcURL, "POST", Encoding.ASCII.GetBytes(postData));
// 				var responseData = Encoding.ASCII.GetString(response, 0, response.Length);

// 				splitResponse(responseData);
// 			}
// 			catch (Exception ex)
// 			{
// 				Log.Error(ex.Message, ex);
// 			}
// 			return GetTransaction();
// 		}

// 		public IOnlineTxn GetTransaction()
// 		{
// 			Func<string, string> get = s => Response.ContainsKey(s) ? Response[s] : String.Empty;

// 			IOnlineTxn tx = new OnlineTxn();
// 			tx.PaymentDate = DateTime.Now;
// 			tx.VpcAuthorizeId = get("vpc_AuthorizeId");
// 			tx.VpcAcqResponseCode = get("vpc_AcqCSCRespCode");
// 			tx.VpcAmount = get("vpc_Amount");
// 			tx.VpcAuthorizeId = get("vpc_AuthorizeId");
// 			tx.VpcBatchNo = get("vpc_BatchNo");
// 			tx.VpcCscResultCode = get("vpc_CSCResultCode");
// 			tx.VpcCard = get("vpc_Card");
// 			tx.VpcCommand = get("vpc_Command");
// 			tx.VpcMerchTxnRef = get("vpc_MerchTxnRef");
// 			tx.VpcMerchant = get("vpc_Merchant");
// 			tx.VpcMessage = get("vpc_Message");
// 			tx.VpcOrderInfo = get("vpc_OrderInfo");
// 			tx.VpcReceiptNo = get("vpc_ReceiptNo");
// 			tx.VpcTransactionNo = get("vpc_TransactionNo");
// 			tx.VpcTxnResponseCode = get("vpc_TxnResponseCode");
// 			tx.VpcVersion = get("vpc_Version");
// 			tx.VpcAvsResultCode = get("vpc_AVSResultCode");
// 			tx.IsSuccessful = Response["vpc_TxnResponseCode"].ToString() == "0";
// 			return tx;
// 		}

// 		#endregion Public Methods

// 		#region Private Methods

// 		private string getAVSDescription(string vAVSResultCode)
// 		{
// 			var result = "Unknown";

// 			if (vAVSResultCode.Length <= 0)
// 			{
// 				return result;
// 			}

// 			if (vAVSResultCode.Equals("Unsupported"))
// 			{
// 				result = "AVS not supported or there was no AVS data provided";
// 				return result;
// 			}

// 			switch (vAVSResultCode)
// 			{
// 				case "X":
// 					result = "Exact match - address and 9 digit ZIP/postal code";
// 					break;

// 				case "Y":
// 					result = "Exact match - address and 5 digit ZIP/postal code";
// 					break;

// 				case "S":
// 					result = "Service not supported or address not verified (international transaction)";
// 					break;

// 				case "G":
// 					result = "Issuer does not participate in AVS (international transaction)";
// 					break;

// 				case "A":
// 					result = "Address match only";
// 					break;

// 				case "W":
// 					result = "9 digit ZIP/postal code matched, Address not Matched";
// 					break;

// 				case "Z":
// 					result = "5 digit ZIP/postal code matched, Address not Matched";
// 					break;

// 				case "R":
// 					result = "Issuer system is unavailable";
// 					break;

// 				case "U":
// 					result = "Address unavailable or not verified";
// 					break;

// 				case "E":
// 					result = "Address and ZIP/postal code not provided";
// 					break;

// 				case "N":
// 					result = "Address and ZIP/postal code not matched";
// 					break;

// 				case "0":
// 					result = "AVS not requested";
// 					break;

// 				default:
// 					result = "Unable to be determined";
// 					break;
// 			}
// 			return result;
// 		}

// 		private string getCSCDescription(string vCSCResultCode)
// 		{
// 			var result = "Unknown";
// 			if (vCSCResultCode.Length <= 0)
// 				return result;

// 			if (vCSCResultCode.Equals("Unsupported"))
// 			{
// 				result = "CSC not supported or there was no CSC data provided";
// 				return result;
// 			}

// 			switch (vCSCResultCode)
// 			{
// 				case "M":
// 					result = "Exact code match";
// 					break;

// 				case "S":
// 					result = "Merchant has indicated that CSC is not present on the card (MOTO situation)";
// 					break;

// 				case "P":
// 					result = "Code not processed";
// 					break;

// 				case "U":
// 					result = "Card issuer is not registered and/or certified";
// 					break;

// 				case "N":
// 					result = "Code invalid or not matched";
// 					break;

// 				default:
// 					result = "Unable to be determined";
// 					break;
// 			}
// 			return result;
// 		}

// 		private string getResponseDescription(string vResponseCode)
// 		{
// 			var result = "Unknown";

// 			if (vResponseCode.Length <= 0)
// 				return result;

// 			switch (vResponseCode)
// 			{
// 				case "0":
// 					result = "Transaction Successful";
// 					break;

// 				case "1":
// 					result = "Transaction Declined";
// 					break;

// 				case "2":
// 					result = "Bank Declined Transaction";
// 					break;

// 				case "3":
// 					result = "No Reply from Bank";
// 					break;

// 				case "4":
// 					result = "Expired Card";
// 					break;

// 				case "5":
// 					result = "Insufficient Funds";
// 					break;

// 				case "6":
// 					result = "Error Communicating with Bank";
// 					break;

// 				case "7":
// 					result = "Payment Server detected an error";
// 					break;

// 				case "8":
// 					result = "Transaction Type Not Supported";
// 					break;

// 				case "9":
// 					result = "Bank declined transaction (Do not contact Bank)";
// 					break;

// 				case "A":
// 					result = "Transaction Aborted";
// 					break;

// 				case "B":
// 					result = "Transaction Declined - Contact the Bank";
// 					break;

// 				case "C":
// 					result = "Transaction Cancelled";
// 					break;

// 				case "D":
// 					result = "Deferred transaction has been received and is awaiting processing";
// 					break;

// 				case "F":
// 					result = "3-D Secure Authentication failed";
// 					break;

// 				case "I":
// 					result = "Card Security Code verification failed";
// 					break;

// 				case "L":
// 					result = "Shopping Transaction Locked (Please try the transaction again later)";
// 					break;

// 				case "N":
// 					result = "Cardholder is not enrolled in Authentication scheme";
// 					break;

// 				case "P":
// 					result = "Transaction has been received by the Payment Adaptor and is being processed";
// 					break;

// 				case "R":
// 					result = "Transaction was not processed - Reached limit of retry attempts allowed";
// 					break;

// 				case "S":
// 					result = "Duplicate SessionID";
// 					break;

// 				case "T":
// 					result = "Address Verification Failed";
// 					break;

// 				case "U":
// 					result = "Card Security Code Failed";
// 					break;

// 				case "V":
// 					result = "Address Verification and Card Security Code Failed";
// 					break;

// 				default:
// 					result = "Unable to be determined";
// 					break;
// 			}
// 			return result;
// 		}

// 		private void splitResponse(string rawData)
// 		{
// 			try
// 			{
// 				// Check if there was a response containing parameters
// 				if (rawData.IndexOf("=") > 0)
// 				{
// 					// Extract the key/value pairs for each parameter
// 					foreach (var pair in rawData.Split('&'))
// 					{
// 						var equalsIndex = pair.IndexOf("=");
// 						if (equalsIndex > 1 && pair.Length > equalsIndex)
// 						{
// 							var paramKey = HttpUtility.UrlDecode(pair.Substring(0, equalsIndex));
// 							var paramValue = HttpUtility.UrlDecode(pair.Substring(equalsIndex + 1));
// 							Response.Add(paramKey, paramValue);
// 						}
// 					}
// 				}
// 				else
// 				{
// 					// There were no parameters so create an error
// 					Response.Add("vpc_Message",
// 						"The data contained in the response was not a valid receipt.<br/>\nThe data was: <pre>" +
// 						rawData + "</pre><br/>\n");
// 				}
// 			}
// 			catch (Exception ex)
// 			{
// 				// There was an exception so create an error
// 				Log.Error(ex.Message, ex);
// 				Response.Add("vpc_Message",
// 					"\nThe was an exception parsing the response data.<br/>\nThe data was: <pre>" + rawData +
// 					"</pre><br/>\n<br/>\nException: " + ex.ToString() + "<br/>\n");
// 			}
// 		}

// 		#endregion Private Methods
// 	}
// }
