//using lep.freight.impl;

//namespace lep.freight.csv
//{
//    using lep.job;
//    using NHibernate;
//    using System.Collections.Generic;
//    using System.Data;
//    using System.Linq;

//    public class CartonOptionReader : lep.www.BulkCopyImport
//    {
//        private IDictionary<string, int> papersizeLst;
//        private IDictionary<string, int> stockLst;
//        private IDictionary<string, int> jobTypeLst;
//        private IList<string> cartonLst;

//        public CartonOptionReader(ISession session) : base("CartonOption", typeof(CartonOption), session)
//        {
//        }

//        protected override DataTable CreateDataTable()
//        {
//            DataTable table = new DataTable();

//            DataColumn IdCol = new DataColumn("Id", typeof(int));
//            IdCol.AllowDBNull = false;
//            IdCol.AutoIncrement = true;
//            table.Columns.Add(IdCol);

//            DataColumn jobCol = new DataColumn("JobTemplateId", typeof(int));
//            jobCol.AllowDBNull = false;
//            table.Columns.Add(jobCol);

//            DataColumn stockCol = new DataColumn("StockId", typeof(int));
//            stockCol.AllowDBNull = false;
//            table.Columns.Add(stockCol);

//            DataColumn finishCol = new DataColumn("FinishId", typeof(int));
//            finishCol.AllowDBNull = false;
//            table.Columns.Add(finishCol);

//            DataColumn foldCol = new DataColumn("FoldId", typeof(int));
//            foldCol.AllowDBNull = false;
//            table.Columns.Add(foldCol);

//            DataColumn cartonTypeCol = new DataColumn("CartonCode", typeof(string));
//            cartonTypeCol.MaxLength = 50;
//            cartonTypeCol.AllowDBNull = false;
//            table.Columns.Add(cartonTypeCol);

//            DataColumn magnetCol = new DataColumn("Magnet", typeof(char));
//            magnetCol.MaxLength = 1;
//            magnetCol.AllowDBNull = false;
//            table.Columns.Add(magnetCol);

//            DataColumn capacityCol = new DataColumn("Capacity", typeof(int));
//            capacityCol.AllowDBNull = false;
//            table.Columns.Add(capacityCol);

//            return table;
//        }

//        public override void StartDocument()
//        {
//            base.StartDocument();

//            //get all papersize/stock
//            papersizeLst = JobApplication.ListPaperSize().ToDictionary(s => s.Name, s => s.Id);
//            stockLst = JobApplication.ListStock().ToDictionary(s => s.Name, s => s.Id);
//            jobTypeLst = JobApplication.ListJobTemplates().ToDictionary(s => s.Name, s => s.Id);
//            cartonLst = CartonFinder.Instance.ListCarton(null).Select(c => c.Code).ToList();
//        }

//        protected override bool VerifyHeader(string[] values)
//        {
//            string[] headers = new string[] { "JobType", "Stock", "Trim Size", "Folded Size", "Carton Code", "Magnet", "Quantity" };

//            if (headers.Length != values.Length)
//            {
//                AddError("Header must be JobType,Stock,Trim Size,Folded Size,Carton Code,Magnet, Quantity");
//                return false;
//            }
//            else
//            {
//                for (int i = 0; i < headers.Length; i++)
//                {
//                    if (headers[i].ToLower() != values[i].ToLower())
//                    {
//                        AddError("Header must be JobType,Stock,Trim Size,Folded Size,Carton Code,Magnet, Quantity");
//                        return false;
//                    }
//                }
//            }

//            return true;
//        }

//        protected override bool ProcessRow(DataRow dr, string[] values)
//        {
//            if (values.Length < 7)
//            {
//                AddError("Number of columns not match");
//                return false;
//            }

//            if (jobTypeLst.ContainsKey(values[0]))
//            {
//                dr[1] = jobTypeLst[values[0]];
//            }
//            else
//            {
//                AddError("Invalid job type");
//                return false;
//            }

//            if (stockLst.ContainsKey(values[1]))
//            {
//                dr[2] = stockLst[values[1]];
//            }
//            else
//            {
//                AddError("Invalid Stock");
//                return false;
//            }

//            if (papersizeLst.ContainsKey(values[2]))
//            {
//                dr[3] = papersizeLst[values[2]];
//            }
//            else
//            {
//                AddError("Invalid Trim Size");
//                return false;
//            }

//            if (papersizeLst.ContainsKey(values[3]))
//            {
//                dr[4] = papersizeLst[values[3]];
//            }
//            else
//            {
//                AddError("Invalid Folded Size");
//                return false;
//            }

//            if (cartonLst.Contains(values[4]))
//            {
//                dr[5] = values[4];
//            }
//            else
//            {
//                AddError("Invalid Carton Size");
//                return false;
//            }

//            dr[6] = values[5] == "Y" ? true : false;

//            int tmp;
//            if (int.TryParse(values[6], out tmp) && tmp >= 0)
//            {
//                dr[7] = tmp;
//            }
//            else
//            {
//                AddError("Invalid Quantity");
//                return false;
//            }

//            return true;
//        }

//        public IJobApplication JobApplication { get; set; }
//    }
//}
