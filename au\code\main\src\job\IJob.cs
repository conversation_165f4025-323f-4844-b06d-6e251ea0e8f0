using lep.freight;
using lep.job.impl;
using lep.order;

//using lep.quote;
using lep.run;
using lep.user;

using System;
using System.Collections.Generic;
using System.Security;

namespace lep.job
{


	/// Common to all job types.
	///
	/// </summary>
	public interface IJob
	{

		//list of splits
		JobSplits Splits { get; set; }
		bool HasSplitDelivery { get; }



		#region member variables

		int Id { set; get; }
		string JobNr { get; }
		string Barcode { get; }

		// requested details
		// common to all subtypes
		string Name { get; set; }

		IJobTemplate Template { get; set; }
		IOrder Order { get; set; }

		//IQuote Quote { get; set; } // can be null
		decimal ThicknessOfSingleJob { get; set; }
		int Quantity { get; set; }

		string DisplayQuantity { get; }

		string HealthCss { get; set; }

		int CustomSlot { get; set; }

		ArtworkStatusOption ArtworkStatus { get; set; }
		JobProofStatus ProofStatus { get; set; }
		IList<IArtwork> Artworks { get; set; }
		string SpecialInstructions { get; set; }
		string ProductionInstructions { get; set; }
		int ReOrderSourceJobId { get; set; }
		bool SendSamples { get; set; }

		// customised in jobtypes
		IStock Stock { get; set; }
		IStock StockOverride { get; set; }

		IStock FinalStock { get; }

		JobPrintOptions FrontPrinting { get; set; }
		JobPrintOptions BackPrinting { get; set; }
		JobCelloglazeOptions FrontCelloglaze { get; set; }
		JobCelloglazeOptions BackCelloglaze { get; set; }

		JobCelloglazeOptions? FrontCelloglazeOverride { get; set; }
		JobCelloglazeOptions? BackCelloglazeOverride { get; set; }

		JobCelloglazeOptions FinalFrontCelloglaze { get;  }
		JobCelloglazeOptions FinalBackCelloglaze { get; }



		string FoilColour { get; set; }
		string Envelope { get; set; }

		string EnvelopeType { get; set; }

		ISize FinishedSize { get; set; }
		ISize FoldedSize { get; set; }
		//bool Magnet { get; set; }
		int NumberOfMagnets { get; set; }
		RoundOption RoundOption { get; set; }
		RoundDetailOption RoundDetailOption { get; set; }
		string CustomDieCut { get; set; }
		int? NumberOfHoles { get; set; }
		HoleDrilling HoleDrilling { get; set; }
		RotationOption Rotation { get; set; }
		JobBoundEdgeOptions BoundEdge { get; set; }
		bool TRround { get; set; }
		bool TLround { get; set; }
		bool BRround { get; set; }
		bool BLround { get; set; }

		IFreight Freight { get; set; }

		// magazine only
		int Pages { get; set; }

		bool SelfCovered { get; set; }
		IStock StockForCover { get; set; }

		IStock StockForCoverOverride { get; set; }

		IStock FinalStockForCover { get; }

		IBindingOption BindingOption { get; set; }

		string ArtSuppliedVia { get; set; }

		string PriceBase { get; set; }
		decimal PriceMargin { get; set; }
		string PriceMarginValue { get; set; }
		string ProductPriceCode { get; set; }
		string Price { get; set; }

		decimal? CustomerRequestedPrice { get; set; }
		bool IsWhiteLabel { get; set; }
		string PriceWL { get; set; }

		bool IsQuotePrice { get; set; }

		bool IsQuoteExpired { get; }
		bool IsAutoPriceExpired { get; }

		DateTime? PriceDate { get; set; }
		//string MYOB { get; set; }

		// job management
		JobStatusOptions Status { set; get; }

		JobStatusOptions NextStatus { set; get; }
		DateTime StatusDate { set; get; }
		JobApprovalOptions SupplyArtworkApproval { set; get; }
		JobApprovalOptions ReadyArtworkApproval { set; get; }
		bool HasRejectedBefore { set; get; }
		bool QuoteNeedApprove { get; set; }

		//bool Enable { get; set; }
		PadDirection PadDirection { get; set; }

		DateTime? ReceivedDate { get; set; }
		IProofs Proofs { get; set; }
		bool Urgent { get; set; }
		bool TrackProgress { get; set; }

		// lep production
		IPrepress Prepress { get; set; }

		IStaff PrepressCheckedBy { get; set; }
		IStaff PrintedBy { get; set; }
		DateTime? PrintedDate { get; set; }
		int NumPressSheets { get; set; }

		// lep PrePress & Finishing Instructions
		string LepSpecialInstructions { get; set; }

		string Folding { get; set; }
		bool Scoring { get; set; }
		string ScoringInstructions { get; set; }
		bool Perforating { get; set; }
		string PerforatingInstructions { get; set; }
		CutOptions DieCutType { get; set; }
		string DieCutting { get; set; }
		string RequestedPackaging { get; set; }
		string DispatchRequirements { get; set; }

		bool NCRNumbered { get; set; }

		string NCRStartingNumber { get; set; }

		NCRInfo NCRInfo { get; set; }
		string GetActualPackaging();

		IStaff FinishedBy { get; set; }
		DateTime? FinishedDate { get; set; }

		IList<IPressDetail> PressDetails { set; get; }

		string Preview { get; set; }

		DateTime? DispatchDate { get; set; }

		DateTime? PrintByDate { get; set; }

		IList<IComment> Comments { set; get; }

		//IList<IStatusChangeEvent> StatusChangeEvents { set; get; }
		IList<IRun> Runs { get; set; }

		IUser CreatedBy { get; set; }
		DateTime DateCreated { get; set; }
		DateTime DateModified { get; set; }

		// Printing
		bool Printed { get; set; }

		bool MailedPrePayment { get; set; }
		bool MailedGonePlate { get; set; }
		bool MailedGoneFinish { get; set; }
		bool MailedComplete { get; set; }
		bool MailedCompletePayment { get; set; }
		DateTime? RequiredByDate { get; set; }

		IList<string> GetRequiredPosition();

		bool IsAllowPreflight();

		bool InvolvesOutwork { get; set; }

		bool HasFacilityChange { get; set; }

		void SetPrice(IUser user, decimal engineprice, decimal quoteprice, bool isApproved);

		bool IsOptionEditable(IUser currentUser);

		bool IsArtworkEditable();

		/// <summary>
		/// Add a new comment to this job.
		/// </summary>
		void AddComment(IUser author, string commentText, bool staffOnly = false);

		/// <summary>
		/// Add a new comment to this job relating to a change of status
		///  create new Comment for this job relating to the status
		/// </summary>

		void AddStatusComment(IUser author);

		//CR26
		bool IsBusinessCard();

		bool IsPartOfMultiJobOrder();

		bool IsFurtherProcessingRequired();

		bool IsFurtherProcessingRequiredForAnyJobInOrder();

		bool IsFurtherProcessingRequiredForOtherJobsInOrder();

		bool IsCustomerGood();

		bool IsTheLastJobInOrderBeingProcessed(Facility facility);

		bool JobsOrderIsInSingleRun();

		int ScanCount { get; set; }

		//cr21
		string ReprintReason { get; set; }

		string ReprintReasonPredefined { get; set; }
		bool IsReprint { get; set; }
		bool IsRestart { get; }
		bool IsReprintJobDispatch { get; set; }
		string ReprintResult { get; set; }

		int ReprintFromPreviousJobNo { get; set; }
		int ReprintFromPreviousRunNo { get; set; }

		string NCRNo { get; set; }
		string ReprintCost { get; set; }

		IUser ReprintBy { get; set; }

		PrintType PrintType { get; set; }
		PrintType? ForcedPrintType { get; set; }

		bool IsCustomFacility { get; set; }
		Facility? Facility { get; set; }

		bool? AACNotPerformed { get; }
		bool AACNotPerformedOld { get; set; }
		#endregion member variables



		#region  Quote Fields
		string QuoteEstimator { get; set; }
		decimal? QuoteCOGS { get; set; }
		decimal? QuoteOutworkCost { get; set; }
		string QuoteComments { get; set; }
		string QuotePrimary { get; set; }

		string QuoteOutcome { get; set; }
		string QuoteFollowUpNotes { get; set; }

		string QuoteLostReason { get; set; }
		string QuoteLostComments { get; set; }

		#endregion

		#region workflow methods

		//call before save job
		void JobSaveState(IUser user, bool awaitingArtworkApproval);

		/// <summary>
		/// Job is submitted when it's Order is submitted.
		/// Customer can no longer make changes.
		/// PRE:
		///  Status is Open or PreflightDone
		///  CurrentUser is a Customer User
		/// POST:
		///  if Status is Open -> Submitted
		///  if Status is PreflightDone then no change
		///  LepApproval = NotNeeded
		///  IF (RejectStatus==Current) RejectStatus=Previously
		///  System Comment added
		/// </summary>
		void Submit(IUser user);

		//void Withdraw (IUser user);
		bool CanWithdraw(IUser user);

		//void Reactivate (IUser user);
		//bool CanReactivate (IUser user);
		void AcceptSupplyArtwork(IUser customer);

		/// <summary>
		/// Reject this order
		/// PRE:
		///  CurrentUser has Preflight role or higher
		///  Status is Submitted
		/// POST:
		///  Status = Open
		///  LepApproval = Rejected
		///  RejectStatus = Currently
		///  New Comment added
		/// </summary>
		void Reject(IStaff staff, string commentText);

		IArtwork GetArtwork(string position);

		IArtwork AddArtwork(string position);
		string TopLevelPackages();

		//check if artwork valid for submit
		bool IsArtworkValidForSubmit();

		string JobFolderName { get; }
		bool IsArtworkValidForPreflight();

		/// <summary>
		///  Approves the artwork for this job.
		/// PRE
		///  Status is ApprovalRequired
		///  ArtworkApproval = NeedsApproval
		/// POST:
		///  Status = PreflightDone
		///  ArtworkApproval set to Approved
		///  if currently rejected set to previously rejected
		///  System Comment added
		/// </summary>
		void ApproveArtwork(IUser reviewer);

		/// <summary>
		///  Rejects this artwork for this job.
		/// PRE
		///  Status is ApprovalRequired
		///  ArtworkApproval = NeedsApproval
		/// POST:
		///  ArtworkApproval set to Rejected
		///  RejectedStatus set to Currently
		///  Comment added
		/// </summary>
		/// <param name="reviewer"></param>
		/// <param name="commentText"></param>
		void RejectArtwork(IUser reviewer, string commentText);

		/// <summary>
		///  Approves the quote for this job.
		/// PRE
		///  Status is ApprovalRequired
		///  ArtworkApproval = NeedsApproval
		///  QuoteApproval = NeedsApproval
		/// POST:
		///  if (ArtworkApproval = Approved) then
		///      Status = PreflightDone
		///  else
		///      Status = Submitted
		///  QuoteApproval = Approved
		///  if currently rejected set to previously rejected
		///  System Comment added
		/// </summary>
		void ApproveQuote(IUser reviewer);

		/// <summary>
		/// This job's status is set to InRun and the Run recorded in the Runs list.
		/// PRE:
		///  Status is PreflightDone or higher (can be added manually to another run)
		///  CurrentUser has PrePress role or higher, or is SYSTEM
		///  if (customer==prepay || order==paidfor)
		/// POST:
		///  Run added to Runs list
		///  Status = InRun
		///  System Comment added
		/// </summary>
		void AddToRun(IStaff staff, IRun run, bool addComment = true);

		/// <summary>
		/// The specified Run is removed from the list of runs.
		/// The status may be reverted to PreflightDone.
		/// PRE:
		///  Status is InRun
		///  CurrentUser has PrePress role or higher, or is SYSTEM
		/// POST:
		///  Run removed from Runs list
		///  If Runs list is empty then Status reverts to PreflightDone
		///  System Comment added
		/// </summary>
		void RemoveFromRun(IStaff staff, IRun run, bool addComment = true);

		/// <summary>
		/// Change Job Status
		/// PRE:
		///
		/// POST:
		///  Job.Status is changed
		///  DateModified set to CURRENT_TIMESTAMP
		///  System Comment added
		/// </summary>
		void SetStatus(JobStatusOptions s, IUser user);

		IJob CopyJob();

		bool HasReject { get; }
		bool NeedApproval { get; }

		bool IsCMYK { get; }
		RunCelloglazeOptions Celloglaze { get; }

		int CalculateJobSize();

		int DisplayJobSize();

		IPressDetail CreatePressDetail();

		#endregion workflow methods

		string Render(bool forStff);

		string StatusS { get; }
		string StatusC { get; }
		string StatusCss { get; }

		string JobInfo { get; }

		BrochureDistPackInfo BrochureDistPackInfo { get; set; }
		WiroMagazineInfo WiroInfo { get; set; }

		int CelloSides();

		JobProgress GetProgress();

		bool IsMagazine();
		bool IsWiroMagazine();
		bool IsMagazineSeparate();
		bool IsPresentationFolder();

		bool IsBrochure();

		bool IsDigital();
		bool IsDigitalAndRunGanged();

		bool IsWideFormat();

		bool IsNCRBook();

		bool IsEnvelope();

		bool IsSDD();

		bool IsNDD();

		/// <summary>
		/// Determines if a job is an outwork job based on its template
		/// </summary>
		bool IsOutworkJob();

		decimal GetSpineWidth();

		string GetDigitalJobMailHouseInstuctions();

		bool IsOpenish();
	}


	public class NCRInfo
	{
		public Dictionary<string, NCRSheetInfo> Sheets { get; set; }
		public string Cover { get; set; }
		public string BindingTape { get; set; }
		//public string FrontInkColor { get; set; }
		//public string BackInkColor { get; set; }
	}


	public class NCRSheetInfo
	{
		public string PaperColor { get; set; }
		public bool Perforated { get; set; }
		public bool PrintedOnBack { get; set; }
	}

}
