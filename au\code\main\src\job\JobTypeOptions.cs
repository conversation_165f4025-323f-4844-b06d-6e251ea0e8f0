using System.Linq;

namespace lep.job
{
	// enum of records in the JobOption table
	// TODO this must be synchronised with the ID's of
	// the records in the JobOptiontable
	public enum JobTypeOptions
    {
        [Description("Business Card")] BusinessCard = 1,

        [Description("Double Business Card")] DoubleBusinessCard = 2,

        [Description("DL")] DL = 3,

        [Description("DL Special")] DLSpecial = 4,

        [Description("Letterhead")] Letterhead = 5,

        [Description("With Comps Slip")] Compliments = 6,

        [Description("Brochures & Flyers")] Brochure = 7,

        [Description("Magazine/Booklet")] Magazine = 8,

        [Description("Presentation Folder")] PresentationFolder = 9,

        [Description("Poster")] Poster = 10,

        [Description("Postcard")] Postcard = 11,

        [Description("Custom")] Custom = 12,

        [Description("Brochure Special")] BrochureSpecial = 13,

        [Description("Magazine/Booklet Separate Cover")] MagazineSeparate = 14,

        [Description("Golf Score Cards")] GolfScoreCards = 15,

        [Description("Notepads")] Notepads = 16,

        [Description("Brochure (next day dispatch)")] BrochureNDD = 18,

        [Description("Letterhead (next day dispatch)")] LetterheadNDD = 19,

        [Description("With Comps Slips (next day dispatch)")] ComplimentsNDD = 20,

        [Description("Presentation Folder (next day dispatch)")] PresentationFolderNDD = 21,

        [Description("Magazine/Booklet (next day dispatch)")] MagazineNDD = 22,

        [Description("Brochure (same day dispatch)")] BrochureSDD = 23,

        [Description("Letterhead (same day dispatch)")] LetterheadSDD = 24,

        [Description("With Comps Slips (same day dispatch)")] ComplimentsSDD = 25,

        [Description("Business Card (next day dispatch)")] BusinessCardNdd = 26,

        [Description("Business Card (same day dispatch)")] BusinessCardSdd = 27,

        [Description("Stationery")]
        Stationery = 28,

        [Description("Stationery (same day dispatch)")]
        StationerySDD = 29,

        [Description("Magazine Cover")]
        MagazineCover = 30,

        //----------------------- BANNERS/PULLUPS ------------

        //	(37, 'Pull Up Banner (Standard Stand)', 'Y', 'N') ,
        //  (38, 'Pull Up Banner (Premium Stand)', 'Y', 'N') ,

        [Description("Backlit Posters")]
        BacklitPosters = 32,

        [Description("Mesh Banner with eyelets")]
        MeshBanner = 33,

        [Description("Vinyl Outdoor")]
        VinylOutdoor = 34,

        [Description("Poster Matt Art")]
        PosterMattArt = 35,

        [Description("Poster Canvas")]
        PosterCanvas = 36,

        [Description("Pull Up Banner (Standard Stand)")]
        PullUpBannerStandardStand = 37,

        [Description("Pull Up Banner (Premium Stand)")]
        PullUpBannerPremiumStand = 38,

        //---------------------- ADHESIVES SIGNS/STICKERS  ----
        [Description("Vinyl Sticker")]
        VinylSticker = 41,

        [Description("Removable Wall Decals")]
        RemovableWallDecals = 42,

        [Description("Vinyl Sticker Outdoor")]
        VinylStickerOutdoor = 43,

        [Description("Rigid Signs")]
        RigidSigns = 44,

        //----------------------- NCR BOOKS ------------------
        [Description("Duplicate NCR Books")]
        DuplicateNCRBooks = 51,

        [Description("Triplicate NCR Books ")]
        TriplicateNCRBooks = 52,

        [Description("Quadruplicate NCR Books")]
        QuadruplicateNCRBooks = 53,

		//-----------------------Envelopes ------------------

		[Description("Envelope Black")]
		EnvelopeBlack = 60,

		[Description("Envelope 1PMS")]
        Envelope1Pms = 61,

        [Description("Envelope 2PMS")]
        Envelope2Pms = 62,

        [Description("Envelope CMYK")]
        EnvelopeCmyk = 63,


		/*
			Calendars/Greeting Cards
		A4 Calendar Self Cover
		A4 Calendar Separate Cover
 		*/

		[Description("A4 Calendar Self Cover")]
		A4CalendarSelfCover = 71,

		[Description("A4 Calendar Separate Cover")]
		A4CalendarSeparateCover = 72,

		[Description("Tent Calendars")]
		TentCalendars = 73,


		[Description("DL Calendars")]
		DLCalendars = 74,

		[Description("Greeting Cards")]
		GreetingCards = 75,

        //Wiro Magazines
        [Description("Wiro Magazines")]
        WiroMagazines = 76,

		[Description("Fridge Magnet")] FridgeMagnet = 6020,



    }

    public static class CustomJobType
    {
		public static int Original(int jobOptionId)
		{

			switch (jobOptionId)
			{
				case (int)JobTypeOptions.A4CalendarSelfCover:
					jobOptionId = (int)JobTypeOptions.Magazine;
					break;

				case (int)JobTypeOptions.A4CalendarSeparateCover:
					jobOptionId = (int)JobTypeOptions.MagazineSeparate;
					break;

				case (int)JobTypeOptions.TentCalendars:
				case (int)JobTypeOptions.DLCalendars:
				case (int)JobTypeOptions.GreetingCards:
						jobOptionId = (int)JobTypeOptions.Brochure;
					break;
			}

			return jobOptionId;
		}

		public static bool Is(this JobTypeOptions current, params JobTypeOptions[] list)
        {
            return list.Any(t => current == t);
        }
    }
}
