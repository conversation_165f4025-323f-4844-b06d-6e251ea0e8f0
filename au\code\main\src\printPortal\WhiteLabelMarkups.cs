using lep.job.impl;
using System.Collections.Generic;
using System.Linq;

namespace lep.printPortal
{
	public class CategoryMarkup
	{
		public JobTemplate Template { get; set; }

		public string Category { get; set; } // delete after data has been ported after release

		//public string PrintType { get; set; }

		public string FinishedSize { get; set; }

		public string Fold { get; set; }

		public string Cello { get; set; }

		public int? QtyMin { get; set; }
		public int? QtyMax { get; set; }

		public decimal Markup { get; set; }
	}

	public class CategoryMarkupWithMoreSpecifityComesFirst : IComparer<CategoryMarkup>
	{
		public int Compare(CategoryMarkup x, CategoryMarkup y)
		{
			int r = x.Template.Name.CompareTo(y.Template.Name);

			//if (r == 0)
			//{
			//	r = x.PrintType == null || x.PrintType == "Any" ? 1 : x.PrintType.CompareTo(y.PrintType);
			//}

			if (r == 0)
			{
				r = x.FinishedSize == null || x.FinishedSize == "Any" ? 1 : x.FinishedSize.CompareTo(y.FinishedSize);
			}

			if (r == 0)
			{
				r = x.Fold == null || x.Fold == "Any" ? 1 : x.Fold.CompareTo(y.Fold);
			}

			if (r == 0)
			{
				r = x.Cello == null || x.Cello == "Any" ? 1 : x.Cello.CompareTo(y.Cello);
			}

			return r;
		}
	}

	public class PriceRangeMarkup
	{
		public int PriceFrom { get; set; }
		public int PriceTo { get; set; }
		public decimal Markup { get; set; }
	}

	// marker classes for nhibernate mapping xml, do not delete even if no reference in code
	// see user.hbm.xml
	public class ListOfCategoryMarkups : List<CategoryMarkup>
	{
		public override bool Equals(object obj)
		{
			if (obj is ListOfCategoryMarkups other)
			{
				return this.SequenceEqual(other);
			}
			return false;
		}
	}

	public class ListOfPriceRangeMarkups : List<PriceRangeMarkup> { }

	public class FavouriteCategoryMarkups : Dictionary<string, List<CategoryMarkup>> { }

	public class FavouritePriceRangeMarkups : Dictionary<string, List<PriceRangeMarkup>> { }
}
