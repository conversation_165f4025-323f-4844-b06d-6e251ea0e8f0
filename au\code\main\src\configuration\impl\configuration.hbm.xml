<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2"
				   namespace="lep.configuration.impl"
				   assembly="lep"
				   auto-import="true"
				   default-cascade="all">

	<class name="ConfigurationValue" table="configuration" discriminator-value="null">
		<cache usage="read-write" />
		<id name="Code" type="lep.GenericEnum`1[lep.configuration.Configuration], lep" unsaved-value="null">
			<generator class="assigned" />
		</id>
		<property name="Value" column="DefaultValue" length="200" not-null="true" />
	</class>
</hibernate-mapping>