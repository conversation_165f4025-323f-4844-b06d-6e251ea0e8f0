using System;
using lep.user;

namespace lep.quote
{
    /// <summary>
    /// Proscribes business logic for a Quote within the system.
    ///
    /// Statuses: Requested, Quoted, Rejected
    ///
    /// Operations:
    /// Create a New Quote Request is done via QuoteApplication.newQuote();
    /// Search/Retrieve Customer Quotes - Customer User or Staff
    /// Respond to Quote Request - Staff
    ///
    /// </summary>
    public interface IQuote
    {
        int Id { get; set; }
        String QuoteNr { get; }
        ICustomerUser Customer { get; set; }
        QuoteStatusOptions Status { get; set; }
        String Description { get; set; }
        String Quotation { get; set; }
        DateTime ValidUntil { get; set; }
        DateTime DateCreated { get; set; }
        DateTime DateModified { get; set; }

        /// <summary>
        /// Respond to a Quote using the default number of valid days.
        /// PRE:
        ///  quotation not null nor empty
        ///  Status==Requested
        /// POST:
        ///  Quote is updated with quotation
        ///  ValidUntil calculated using default number
        ///  Status set to Quoted
        ///  DateModified set
        /// </summary>
        /// <param name="user">the user performing the operation</param>
        /// <param name="quotation">LEP's quotation response</param>
        void RespondToQuote(IStaff user, string quotation);

		/// <summary>
		/// Respond to Quote and supply the number of valid days.
		/// PRE:
		///  quotation not null nor empty
		///  Status==Requested
		/// POST:
		///  Quote is updated with Quotation
		///  ValidUntil calculated using validDays
		///  Status set to Quoted
		///  DateModified set
		/// </summary>
		/// <param name="user">the user performing the operation</param>
		/// <param name="quotation">LEP's quotation response</param>
		/// <param name="validUntil">Number of days added to NOW to determine ValidUntil</param>
		void RespondToQuote (IStaff user, string quotation, DateTime validUntil);

        /// <summary>
        /// PRE:
        ///  quotation not null nor empty
        ///  Status==Requested
        /// POST:
        ///  Quote is updated with quotation
        ///  Status set to Rejected
        ///  DateModified set
        /// </summary>
        /// <param name="user">the user performing the operation</param>
        /// <param name="quotation">LEP's quotation response</param>
        void RejectQuote(IStaff user, string quotation);
    }
}
