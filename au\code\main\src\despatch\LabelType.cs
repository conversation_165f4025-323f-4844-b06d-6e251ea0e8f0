namespace lep.despatch
{
    public enum LabelType
    {
        Undetermined,

        [Description("Business Card Reference Layout")] BusinessCardReferenceLayout,

        [Description("")] FurtherProcessing,

        [Description("Carton Label")] Carton,

        [Description("Freight Label")] Freight,

        [Description("Filing Pegionhole Label")] Filing,

        [Description("Filing Pegionhole Payme Label")] FilingPayMe,

        [Description("Ready to Freight now")] FreightMe,

        [Description("Immediate Freight Slip")] ImmediateFreightMe,

        [Description("Freight Label Pick List")] FreightPickList,

        [Description("Pick up only, no freight needed")] Pickup,

        [Description("DPC Processing Label")] DPCProcessing,

        [Description("Wide Format Processing Label")] WideFormatProcessing,

        [Description("FurtherProcessing Labels in strict print order")]
        FurtherProcessingList,
    }
}