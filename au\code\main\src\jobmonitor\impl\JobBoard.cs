using lep.job;

using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace lep.jobmonitor.impl
{
	[Serializable]
	public class JobBoard
	{
		#region Constructors

		// private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		public JobBoard()
		{
		}

		#endregion Constructors

		public event EventHandler VersionUpdated;

		#region Properties

		public List<JobDetailsDTO> Entries { get; set; } = new List<JobDetailsDTO>();
		public long Version { get; set; } = 0;
		public object SyncRoot { get; set; } = new object();

		private String RenderBoardsToAppear(JobDetailsDTO jd)
		{
			if (jd.BoardsToAppear.Any())
			{
				var boards = jd.BoardsToAppear.Select(aa => aa.ToString()).Aggregate((m, n) => m + "," + n);
				return boards;
			}
			else
			{
				return "Where does this go?";
			}
		}

		public object[][] GetEntiesAA(Facility facility, JobBoardTypes board)
		{
			object[][] result = null;

			if (Entries == null)
				return result;

			lock (SyncRoot)
			{
				try
				{
					result = Entries
					.Where(e => e.Facility == facility)
					.Where(e => board == JobBoardTypes.All || e.BoardsToAppear.Contains(board))
					.Select(j => new object[]
					{
                    /* 00 */	j.Health,
                    /* 01 */	RenderBoardsToAppear(j),
                    /* 02 */	j.Id,
                    /* 03 */	j.OrderId,
                    /* 04 */	j.RunId,              //(j.RunId != -1 ? j.RunId.ToString():""),
                    /* 05 */	j.CustomerName,
                    /* 06 */	j.JobName,
					/* 07 */	j.PrintByDate,
					/* 08 */	j.DespatchByDate,
					/* 09 */	j.HD,                // we send seconds to clientside and calculate the hh:mm in javascript
					/* 10 */	j.CurrentStatus,
					/* 11 */	j.StatusDate,
					/* 12 */	j.NextStatus,
					/* 13 */	j.Template,
					/* 14 */	j.Stock,
					/* 15 */	j.Cello,
					/* 16 */	j.TrimSize,
					/* 17 */	j.Folding,
					/* 18 */	j.Quantity,
					/* 19 */	j.PressDetailPaperSize,
					/* 20 */	j.PressSheets,
					/* 21 */	Math.Round(j.Age.TotalHours, 2),
					/* 22 */	j.RunIsBC ? string.Format("_R{0:D6}", j.RunId) : "Jobs",
					/* 23 */	j.Health != 3 ? "E" : "",
					/* 24 */	j.RunIsBC ? string.Format("{0}_R{1:D6}_J{2:D6}", j.HealthOfRun, j.RunId, j.Id) : "Z_Jobs",
					/* 25 */	j.Id == 0 ? "r" : (j.RunIsBC ? "jbc" : "j"),
					/* 26 */	j.RunId,
					/* 27 */	j.PressDetailPaperSize,
					/* 28 */	j.OrderDispatchEst // we use this to show ? in HD column,
					})
					.ToArray();
				}
				catch (Exception ex)
				{
					Log.Error(ex.Message);
				}
			}
			return result;
		}

		#endregion Properties

		#region Public Methods

		//public bool HasJob (int id)
		//{
		//	lock (SyncRoot) {
		//		return Entries.Any(x => x.Id == id);
		//	}

		//}

		//public void Add (JobDetailsDTO j)
		//{
		//	lock (SyncRoot) {
		//		Entries.Add(j);
		//	}
		//}

		//public void Remove (int jdId)
		//{
		//	lock (SyncRoot) {
		//		Entries.RemoveAll(x => x.Id == jdId);
		//	}
		//}

		//public void RemoveRun (int runId)
		//{
		//	lock (SyncRoot) {
		//		Entries.RemoveAll(x => x.Id == 0 && x.RunId == runId);
		//	}
		//}

		//public void ColoriseRuns ()
		//{
		//	return;
		//	lock (SyncRoot) {
		//		// faster way to colorize runs
		//		// instead of finding min health for each candidate job details we start with a list of runs that needs health adorning.
		//		var runIDs =
		//			Entries.Where(e => e.RunId != -1 && e.RunIsBC).Select(e => e.RunId).Distinct().ToList<int>();
		//		var runHealths = new Dictionary<int, int>();

		//		foreach (var runID in runIDs) {
		//			var heath = Entries.Where(e1 => e1.RunId == runID).Min(e2 => e2.Health);
		//			runHealths.Add(runID, heath);
		//		}

		//		foreach (var runID in runIDs) {
		//			var heath = runHealths[runID];
		//			Entries.Where(e => e.RunId == runID).ForEach((x) => { x.HealthOfRun = heath; });
		//		}
		//	}
		//}

		public void UpdateVersion()
		{
			lock (SyncRoot)
			{
				Version = DateTime.Now.Ticks;
			}
			VersionUpdated?.Invoke(this, null);
		}

		#endregion Public Methods
	}
}
