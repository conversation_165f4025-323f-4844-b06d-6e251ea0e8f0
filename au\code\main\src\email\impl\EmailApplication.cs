using lep.configuration;
using lep.content;
using lep.courier;
using lep.extensionmethods;
using lep.job;
using lep.onlineTxn;
using lep.order;
using lep.security;
using lep.user;

using Serilog;
using NHibernate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Mail;
using System.Reflection;
using System.Text;
using System.Threading;
using static lep.content.ContentType;
using static lep.user.NotificationType;
using static System.String;
 
namespace lep.email.impl
{
	public class EmailApplication : BaseApplication, IEmailApplication
	{
		// private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		public EmailApplication(ISession sf, ISecurityApplication _securityApp,
		IConfigurationApplication configApp, IContentApplication contentApp) : base(sf, _securityApp)
		{
			_configApp = configApp;
			_contentApp = contentApp;
		}

		public bool SendForgottenPassword(IUser user, string newPassword)
		{
			var name = "";
			if (user is IStaff)
			{
				name = user.FirstName + " " + user.LastName;
			}
			if (user is ICustomerUser)
			{
				name = ((ICustomerUser)user).Name;
			}

			var body = _contentApp.GetContent((int)PasswordReset).Body;
			body = body.Replace("[[username]]", user.Username)
				.Replace("[[password]]", newPassword)
				.Replace("[[name]]", name);

			body += _contentApp.GetContent((int)FooterNotification).Body;

			body = SiteMacroReplace(user is ICustomerUser ? ((ICustomerUser)user).SiteLocation : SiteLocation.AU, body);

			var email = NewHtmlMessage(user is ICustomerUser ? ((ICustomerUser)user).SiteLocation : SiteLocation.AU);
			ErrorMessageAddressing(email);
			email.Subject = "LEP Online Password";
			email.To = user.Email;
			email.Body = body;

			Send(email);
			//SendPendingMessages();
			return true;
		}

		private string GetEmailsToSendTo(IOrder order, ContentType notificationType)
		{
			//if (LepGlobal.Instance.TestBox) {
			//	return "<EMAIL>";
			//}

			var emails = new List<string>();
			// Existing logic: remains same
			if (AllowEmail(notificationType, order.Customer.NotificationType) && !IsNullOrEmpty(order.Customer.Email))
			{
				emails.Add(order.Customer.Email);
			}

			// new bit: Also send
			// added 17/12/2019

			if (AllowEmail(notificationType, order.Customer.NotificationType) &&
				!IsNullOrEmpty(order.Contact.Email))
			{
				emails.Add(order.Contact.Email);
			}

			var result = String.Empty;
			if (emails.Any())
			{
				result = emails.Distinct().Aggregate((m, n) => m + ";" + n);
			}

			return result;
		}

		public void SendNotification(IOrder order, ContentType contentType)
		{
			try
			{
				var emailsToSendto = GetEmailsToSendTo(order, contentType);
				if (emailsToSendto.Length <= 1) return;
				String body;
				var message = NewHtmlMessage(order.Customer.SiteLocation);
				message.To = emailsToSendto;
				message.Subject = NotificationSubject(contentType);
				body = _contentApp.GetContent((int)contentType).Body;
				body = OrderMacroReplace(order, body);
				body = JobsMacroReplace(order, body);
				body = DispatchEstimateMacroReplace(order, body);
				body = ConNoteMacroReplace2(order, body);
				body += _contentApp.GetContent((int)FooterNotification).Body;
				body = SiteMacroReplace(order.Customer.SiteLocation, body);
				message.Body = body;

				if (IsNullOrEmpty(message.Subject) || IsNullOrEmpty(message.Body))
				{
					Log.Error("Empty Email: Order {order.Id}  {notificationType}");
					return;
				}

				Send(message);
			}
			catch (Exception ex)
			{
				Log.Error(ex.Message, ex);
			}
		}

		public void SendRejectionOfVariations(IOrder order)
		{
			ContentType contentType = ContentType.RejectionOfVariationRequest;
			try
			{
				var emailsToSendto = GetEmailsToSendTo(order, contentType);
				if (emailsToSendto.Length <= 1) return;
				String body;
				String subject;
				var message = NewHtmlMessage(order.Customer.SiteLocation);
				message.To = emailsToSendto;
				if (contentType == JobReject || contentType == UnableToMeetPrice || contentType == RejectionOfVariationRequest)
				{
					if (!LepGlobal.Instance.TestBox)
						message.BCC = _configApp.GetValue(Configuration.EmailJobRejectBCCAddress);
				}

				subject = NotificationSubject(contentType);
				
				message.Subject = subject;

				body = _contentApp.GetContent((int)contentType).Body;
				body = OrderMacroReplace(order, body);

				var jobIdsA = order.Jobs
					.Where(_ => _.Status == JobStatusOptions.RejectedVariation)
					.Select(_ => _.Id.ToString() + " : " + _.JobInfo.ToString()).ToArray();
				var jobsIds = string.Join("<br/> ", jobIdsA);

				body = body.Replace("[[jobnumber]]", jobsIds);
				body += _contentApp.GetContent((int)FooterNotification).Body;
				body = SiteMacroReplace(order.Customer.SiteLocation, body);
				message.Body = body;

				if (IsNullOrEmpty(message.Subject) || IsNullOrEmpty(message.Body))
				{
					Log.Error("Empty Email: Job {job.Id}  {notificationType}");
					return;
				}

				Send(message);
			}
			catch (Exception ex)
			{
				Log.Error(ex.Message, ex);
			}
		}


		public void SendNotification(IJob job, ContentType contentType)
		{
			try
			{
				var emailsToSendto = GetEmailsToSendTo(job.Order, contentType);
				if (emailsToSendto.Length <= 1) return;
				String body;
				String subject;
				var message = NewHtmlMessage(job.Order.Customer.SiteLocation);
				message.To = emailsToSendto;
				if (contentType == JobReject || contentType == UnableToMeetPrice || contentType == RejectionOfVariationRequest)
				{
					if (!LepGlobal.Instance.TestBox)
						message.BCC = _configApp.GetValue(Configuration.EmailJobRejectBCCAddress);
				}

				subject = NotificationSubject(contentType);
				subject = JobSubjectMacroReplace(job, subject);
				message.Subject = subject;

				body = _contentApp.GetContent((int)contentType).Body;
				body = OrderMacroReplace(job.Order, body);
				body = JobMacroReplace(job, body);
				body += _contentApp.GetContent((int)FooterNotification).Body;
				body = SiteMacroReplace(job.Order.Customer.SiteLocation, body);
				message.Body = body;

				if (IsNullOrEmpty(message.Subject) || IsNullOrEmpty(message.Body))
				{
					Log.Error("Empty Email: Job {job.Id}  {notificationType}");
					return;
				}

				Send(message);
			}
			catch (Exception ex)
			{
				Log.Error(ex.Message, ex);
			}
		}

		public void SendCustomerDetailChangeNotification(ICustomerUser customer)
		{
			String body;
			var message = NewHtmlMessage(customer.SiteLocation);
			message.To = _configApp.GetValue(Configuration.EmailAdminAddress);
			message.Subject = NotificationSubject(CustomerDetailChange);
			body = _contentApp.GetContent((int)CustomerDetailChange).Body;
			body = CustomerMacroReplace(customer, body);
			body += _contentApp.GetContent((int)FooterNotification).Body;
			body = SiteMacroReplace(customer.SiteLocation, body);
			message.Body = body;
			Send(message);
		}

		public void SendCustomerNotification(ICustomerUser customer, ContentType notificationType)
		{
			if (!IsNullOrEmpty(customer.Email))
			{
				String body;
				var message = NewHtmlMessage(customer.SiteLocation);
				message.To = customer.Email;
				message.Subject = NotificationSubject(notificationType);
				body = _contentApp.GetContent((int)notificationType).Body;
				body = CustomerMacroReplace(customer, body);
				body += _contentApp.GetContent((int)FooterNotification).Body;
				body = SiteMacroReplace(customer.SiteLocation, body);
				message.Body = body;
				Send(message);
			}
		}

		public void SendRaisePurchaseOrderForJobToAcounts(IJob job)
		{
			String body;
			var message = NewHtmlMessage(SiteLocation.AU);
			message.To = _configApp.GetValue(Configuration.EmailCustomerCreditLimitExceeded);
			message.Subject = $"Purchase Order required for Job {job.JobNr}";
			body = job.ProductionInstructions;
			message.Body = body;
			Send(message);
		}

 

		public void SendPayMeOrder(IOrder order)
		{
			String body = "";
			var message = NewHtmlMessage(SiteLocation.AU);
			message.To = _configApp.GetValue(Configuration.EmailCustomerCreditLimitExceeded);
			message.Subject = $"PayMe Order#{order.Id}";
			message.Body = body;
			Send(message);
		}

		public void SendOnlinePaymentNotification(IOnlineTxn tx)
		{
			if (!IsNullOrEmpty(tx.Order.Customer.Email))
			{
				String body;
				var message = NewHtmlMessage(tx.Order.Customer.SiteLocation);
				message.To = tx.Order.Customer.Email;

				if (tx.IsSuccessful)
				{
					message.Subject = NotificationSubject(OnlinePaymentSuccessful);
				}
				else
				{
					message.Subject = NotificationSubject(OnlinePaymentFail);
				}
				body = GetOnlinePaymentNotificationBody(tx);
				body += _contentApp.GetContent((int)FooterNotification).Body;
				body = SiteMacroReplace(tx.Order.Customer.SiteLocation, body);
				message.Body = body;
				Send(message);
			}
		}

		public string GetOnlinePaymentNotificationBody(IOnlineTxn tx)
		{
			string body;
			if (tx.IsSuccessful)
			{
				body = _contentApp.GetContent((int)OnlinePaymentSuccessful).Body;
			}
			else
			{
				body = _contentApp.GetContent((int)OnlinePaymentFail).Body;
			}
			body = CustomerMacroReplace(tx.Order.Customer, body);
			body = OrderMacroReplace(tx.Order, body);
			body = OnlinePaymentMacroReplace(tx, body);

			return body;
		}

		public IMailMessage NewHtmlMessage(SiteLocation loc)
		{
			IMailMessage message = new EmailMessage
			{
				//message.SubjectEncoding = Encoding.ASCII;
				BodyEncoding = Encoding.UTF8,
				From = GetEmailFromAddress(loc),
				//message.ReplyTo = configurationApp.GetValue(Configuration.EmailReplyAddress);
				IsBodyHtml = true,
				Priority = MailPriority.Normal
			};

			return message;
		}

		public IMailMessage NewTextMessage(SiteLocation loc)
		{
			IMailMessage message = new EmailMessage();

			//message.SubjectEncoding = Encoding.ASCII;
			message.BodyEncoding = Encoding.GetEncoding("iso-8859-1");

			message.From = GetEmailFromAddress(loc);
			message.ReplyTo = GetEmailReplyAddress(loc);
			message.IsBodyHtml = false;
			message.Priority = MailPriority.Normal;

			return message;
		}

		public void ErrorMessageAddressing(IMailMessage message)
		{
			// To Address wasnt being set anywhere
			message.To = _configApp.GetValue(Configuration.EmailErrorAddress);
			message.From = _configApp.GetValue(Configuration.EmailErrorAddress);
			message.ReplyTo = _configApp.GetValue(Configuration.EmailErrorAddress);
			message.Priority = MailPriority.High;
		}

		//public void SendCourierMail (IOrder order)
		//{
		//	var sb = new StringBuilder();
		//	var body = "";

		//	if (IsNullOrEmpty(order.Customer.Email)) {
		//	}

		//	var message = NewHtmlMessage(order.Customer.SiteLocation);

		//	message.To = order.Customer.Email;
		//	message.Subject = NotificationSubject(CourierDispatch);

		//	message.Subject = "Order dispatched via courier";

		//	body = ContentApplication.GetContent((int)CourierDispatch).Body;
		//	body = OrderMacroReplace(order, body);
		//	body = JobsMacroReplace(order, body);
		//	body = ConNoteMacroReplace(order, body);

		//	body += ContentApplication.GetContent((int)FooterNotification).Body;
		//	body = SiteMacroReplace(order.Customer.SiteLocation, body);
		//	message.Body = body;
		//	Send(message);

		//}

		public void Send(IMailMessage message)
		{
			Log.Information("Sending mail : " + message.To + "  " + message.Subject + "\n\n" + message.Body + "\n\n");
			message.From = LepGlobal.Instance.SmtpUsername;

			var m = message.ToMailMessage();
			if (m == null)
			{
				Log.Error("null mail : " + message.To + "  " + message.Subject);
				return;
			}
			if (IsNullOrEmpty(m.Subject))
			{
				Log.Error("null mail subject : " + message.To);
				return;
			}

			if (!LepGlobal.Instance.SendMail)
			{
				return;
			}

			ThreadPool.QueueUserWorkItem(t =>
			{
				try
				{
					SmtpClient client = new SmtpClient(LepGlobal.Instance.SmtpServer, LepGlobal.Instance.SmtpPort);
					client.EnableSsl = true;
					client.Credentials = new System.Net.NetworkCredential("apikey", LepGlobal.Instance.SmtpPassword);
					client.Send(m);
				}
				catch (Exception ex)
				{
					Log.Error(ex.Message);
				}
			});
		}



		public void Send(MailMessage message)
		{
			Log.Information("Sending mail : " + message.To + "  " + message.Subject + "\n\n" + message.Body + "\n\n");

			if (!LepGlobal.Instance.SendMail)
			{
				return;
			}

			ThreadPool.QueueUserWorkItem(t =>
			{
				try
				{
					SmtpClient client = new SmtpClient(LepGlobal.Instance.SmtpServer, LepGlobal.Instance.SmtpPort);
					client.EnableSsl = true;
					client.Credentials = new System.Net.NetworkCredential("apikey", LepGlobal.Instance.SmtpPassword);
					client.Send(message);
				}
				catch (Exception ex)
				{
					Log.Error(ex.Message);
				}
			});
		}



		public void SendPendingMessages()
		{
		}

		private bool AllowEmail(ContentType contentType, NotificationType notifyType)
		{
			switch (notifyType)
			{
				case All:
					return true;

				case Brief:
					return contentType.Is(
						JobQuoteRequestSubmit
						, JobQuoteRequestAttention
						, JobArtWorkApproval
						, JobReject
						, OrderPrePayment
						, OrderSubmit
						, JobPaymentAwaitingCourier
						, JobAwaitingCourier
						, OnlinePaymentSuccessful
						, OnlinePaymentFail
						, WebOrderRaised
						, CourierDispatch
						, WebOrderRaisedWithPaypalPayment
						, WebOrderRaisedWithStripePayment
						, WebOrderRaisedWithAccountPayment
						, OrderReadyForPickup
						, UnableToMeetPrice
						, RejectionOfVariationRequest
						);

				case None:
					return contentType.Is(
						Reprint,
						JobQuoteRequestSubmit,
						JobQuoteRequestAttention,
						JobArtWorkApproval,
						JobReject,
						OrderPrePayment,
						WebOrderRaised, 
						WebOrderRaisedWithPaypalPayment,
						WebOrderRaisedWithStripePayment,
						WebOrderRaisedWithAccountPayment,
						OrderReadyForPickup);
			}

			return false;
		}

		private string NotificationSubject(ContentType notificationType)
		{
			switch (notificationType)
			{
				case JobArtWorkApproval:
				case JobAwaitingCourier:
				case JobPaymentAwaitingCourier:
				case JobGonePlate:
				case JobGoneFinish:
				case JobQuoteRequestAttention:
					return "Job Status Update";

				case JobQuoteRequestSubmit:
					return "Name Your Price request submitted";

				case OrderPrePayment:
				case OrderSubmit:
				case CustomerOnHold:
					return "Order Status Update";

				case JobReject:
					return "Artwork Rejected - Action Required";

				case CustomerDetailChange:
					return "Customer detail change notification";

				case OnlinePaymentFail:
					return "Payment Attempt unsuccessful";

				case OnlinePaymentSuccessful:
					return "Thank you for making payment";

				case Reprint:
					return "Reprint Issued";

				case Restart:
					return "Job Restarted";

				case CourierDispatch:
					return "Order dispatched via courier";

				case WebOrderRaised:
					return "New web order";

				case WebOrderRaisedWithPaypalPayment:
					return "New web order with Paypal payment";

				case WebOrderRaisedWithStripePayment:
					return "New web order with Stripe payment";

				case WebOrderRaisedWithAccountPayment:
					return "New web order with Account payment";

				case OrderReadyForPickup:
					return "Order Completed Ready for Pick Up!";

				case UnableToMeetPrice:
					return "Unable to meet price";
					
				case RejectionOfVariationRequest:
					return "Rejection of variation request";
			}
			Log.Error($"{notificationType} has no subject");

			return "";
		}

		private string JobsMacroReplace(IOrder order, string body)
		{
			var numbersb = new StringBuilder();
			var detailsb = new StringBuilder();
			foreach (var j in order.Jobs)
			{
				numbersb.Append($"\n<br/>   {j.JobNr} {j.Name}");
				detailsb.Append($"\n<br/>   {j.Template.Name} {j.JobNr}: {j.Name} x {j.Quantity}");
			}
			return body.Replace("[[jobnumber]]", numbersb.ToString()).Replace("[[jobdetail]]", detailsb.ToString());
		}

		private string DispatchEstimateMacroReplace(IOrder order, string body)
		{
			if (order.DispatchEst != null)
			{
				return body.Replace("[[dispatchestimate]]",
					"The Estimated Dispatch Date for your order is " + order.DispatchEst.Value.ToString("dd/MM/yyyy"));
			}
			else
			{
				return body.Replace("[[dispatchestimate]]", "");
			}
		}

		private string SiteMacroReplace(SiteLocation loc, string body)
		{
			if (loc == SiteLocation.AU)
			{
				return body.Replace("[[lep site name]]", LEP_Site)
					.Replace("[[lep site url]]", LEP_Site_URL)
					.Replace("[[lep email]]", GetEmailFromAddress(loc))
					.Replace("[[lep phone]]", LEP_Phone)
					.Replace("[[lep phone]]", LEP_Fax);
			}
			else
			{
				return body.Replace("[[lep site name]]", LEP_NZ_Site)
					.Replace("[[lep site url]]", LEP_NZ_Site_URL)
					.Replace("[[lep email]]", GetEmailFromAddress(loc))
					.Replace("[[lep phone]]", LEP_NZ_Phone)
					.Replace("[[lep phone]]", LEP_NZ_Fax);
			}
		}

		public string ConNoteMacroReplace(IOrder order, string body)
		{
			if (!body.Contains("[[couriername]]"))
				return body;

			if (!order.ConNotes.Any())
				return body;

			var connotes = order.ConNotes.OrderBy(cn => cn.ConNote);

			var couriername = connotes.First().CarrierName;
			var courierservice = connotes.First().CarrierService;

			var courierImage = Empty;
			var courierUrl = Empty;

			var couriers = new List<string>();

			switch (couriername)
			{
				case CourierType.AusPost:
				case "AUSTRALIA POST":
					courierImage = LepGlobal.Instance.CourierImage_AustPOST;
					courierUrl = LepGlobal.Instance.CourierURL_AustPOST;
					break;

				case CourierType.FastWay:
				case "FastWay":
					courierImage = LepGlobal.Instance.CourierImage_FastWay;
					courierUrl = LepGlobal.Instance.CourierURL_FastWay;
					break;

				case CourierType.StarTrack:
					courierImage = LepGlobal.Instance.CourierImage_StarTrack;
					courierUrl = LepGlobal.Instance.CourierURL_StarTrack;
					break;

				case CourierType.TNT:
					courierImage = LepGlobal.Instance.CourierImage_TNT;
					courierUrl = LepGlobal.Instance.CourierURL_TNT;
					break;

				case CourierType.CourierVicPlease2:
					courierImage = LepGlobal.Instance.CourierImage_CouriersPlease;
					courierUrl = LepGlobal.Instance.CourierURL_CouriersPlease;
					break;

				default:
					break;
			}

			//couriername = couriername + ",  " + courierservice;
			body = body.Replace("[[couriername]]", couriername);

			var logolink = Empty;
			if (!IsNullOrEmpty(courierImage))
			{
				logolink = $"<a href=\"{courierUrl}\"><br /><img src=\"{courierImage}\"><br /></a>";
			}

			body = body.Replace("[[courierlogolink]]", logolink);

			var numbersb = new StringBuilder();
			numbersb.AppendLine("<ol>");

			if(couriername == CourierType.FastWay)
			{
				foreach (var cn in connotes)
				{
					foreach(var tn in cn.TrackingLabels)
					{
						numbersb.Append($"\n<li><a href=\"{courierUrl}\" target=\"_blank\">{tn}</a></li>");
					}
				}
			}
			else
			{
				foreach (var cn in connotes)
				{
					numbersb.Append($"\n<li><a href=\"{courierUrl}\" target=\"_blank\">{cn.ConNote}</a></li>");
				}
			}

			numbersb.AppendLine("</ol>");
			return body.Replace("[[connotelist]]", numbersb.ToString());
		}
		public string ConNoteMacroReplace2(IOrder order, string body)
		{
	 

			if (!order.ConNotes.Any())
				return body;

			var grouped = order.ConNotes.OrderBy(cn => cn.ConNote).GroupBy(_ => _.CarrierName);
			var numbersb = new StringBuilder();

			foreach ( var g in grouped)
			{

				var couriername = g.Key;
				var courierImage = Empty;
				var courierUrl = Empty;

				var couriers = new List<string>();

				switch (couriername)
				{
					case CourierType.AusPost:
					case "AUSTRALIA POST":
						courierImage = LepGlobal.Instance.CourierImage_AustPOST;
						courierUrl = LepGlobal.Instance.CourierURL_AustPOST;
						break;

					case CourierType.FastWay:
					case "FastWay":
						courierImage = LepGlobal.Instance.CourierImage_FastWay;
						courierUrl = LepGlobal.Instance.CourierURL_FastWay;
						break;

					case CourierType.StarTrack:
					case CourierType.StarTrack2:
						courierImage = LepGlobal.Instance.CourierImage_StarTrack;
						courierUrl = LepGlobal.Instance.CourierURL_StarTrack;
						break;

					case CourierType.TNT:
						courierImage = LepGlobal.Instance.CourierImage_TNT;
						courierUrl = LepGlobal.Instance.CourierURL_TNT;
						break;

					case CourierType.CourierVicPlease2:
						courierImage = LepGlobal.Instance.CourierImage_CouriersPlease;
						courierUrl = LepGlobal.Instance.CourierURL_CouriersPlease;
						break;

					case CourierType.TOLLNQX:
						courierImage = LepGlobal.Instance.CourierImage_TOLLNQX;
						courierUrl = LepGlobal.Instance.CourierURL_TOLLNQX;
						break;

					case CourierType.Aramex:
						courierImage = LepGlobal.Instance.CourierImage_ARAMEX;
						courierUrl = LepGlobal.Instance.CourierURL_ARAMEX;
						break;


					default:
						break;
				}


				//couriername = couriername + ",  " + courierservice;
				numbersb.Append("<br/>");
				numbersb.Append(couriername);

				var logolink = Empty;
				if (!IsNullOrEmpty(courierImage))
				{
					logolink = $"<a href=\"{courierUrl}\"><br /><img src=\"{courierImage}\"><br /></a>";
					numbersb.Append(logolink);
				}

				numbersb.Append("<ol>");
				if (couriername == CourierType.FastWay
					|| couriername == CourierType.Aramex)
				{
					foreach (var cn in g)
					{
						if (cn.TrackingLabels == null) continue;
						foreach (var tn in cn.TrackingLabels)
						{
							numbersb.Append($"<li><a href=\"{courierUrl}\" target=\"_blank\">{tn}</a></li>");
						}
					}
				}
				else
				{
					foreach (var cn in g)
					{
						numbersb.Append($"<li><a href=\"{courierUrl}\" target=\"_blank\">{cn.ConNote}</a></li>");
					}
				}

				numbersb.Append("</ol>");
			}

			return body.Replace("[[couierinfo]]", numbersb.ToString());
		}
		private string JobMacroReplace(IJob job, string body)
		{
			var rejectComment = Empty;
			for (var i = job.Comments.Count - 1; i >= 0; i--)
			{
				if (job.Comments[i].CommentText.StartsWith("Job Rejected"))
				{
					rejectComment = job.Comments[i].CommentText;
					break;
				}
			}
			return body.Replace("[[jobnumber]]", $"{job.JobNr} {job.Name}")
				.Replace("[[lastcomment]]", rejectComment)
				.Replace("[[quantity]]", job.Quantity.ToString())
				.Replace("[[jobtype]]", job.Template.Name)
				.Replace("[[jobname]]", job.Name);
		}

		private string JobSubjectMacroReplace(IJob job, string subject)
		{
			return subject.Replace("[[jobnumber]]", job.JobNr.ToString())
				.Replace("[[quantity]]", job.Quantity.ToString())
				.Replace("[[jobtype]]", job.Template.Name)
				.Replace("[[jobname]]", job.Name);
		}

		private string OrderMacroReplace(IOrder order, string body)
		{
			return body.Replace("[[ordernumber]]", order.OrderNr);
		}

		private string CustomerMacroReplace(ICustomerUser customer, string body)
		{
			var sb = new StringBuilder();
			sb.AppendLine($"Customer Number : #{customer.CustomerNr}");
			return body.Replace("[[customer-detail]]", sb.ToString()).Replace("[[BusinessName]]", customer.Name);
		}

		private string OnlinePaymentMacroReplace(IOnlineTxn tx, string body)
		{
			var sb = new StringBuilder();

			if (tx.IsSuccessful)
			{
				sb.AppendLine(Format("<br/>Payment Successful!"));
				sb.AppendLine($"<br/>Payment Date : {tx.PaymentDate}");
				sb.AppendLine($"<br/>Amount       : {Decimal.Parse(tx.VpcAmount) / 100:C}");
				sb.AppendLine($"<br/>Authorise ID : {tx.VpcAuthorizeId}");
				sb.AppendLine($"<br/>Batch #      : {tx.VpcBatchNo}");
				sb.AppendLine($"<br/>Receipt #    : {tx.VpcReceiptNo}");
			}
			else
			{
				sb.AppendLine(Format("<br/>Payment Failure!"));
				sb.AppendLine($"<br/>Attempt Date : {tx.PaymentDate}");
				sb.AppendLine($"<br/>Amount       : {Decimal.Parse(tx.VpcAmount) / 100:C}");
				sb.AppendLine($"<br/>Reason       : {tx.VpcMessage}");
			}

			return body.Replace("[[payment]]", sb.ToString());
		}

		private string GetEmailFromAddress(SiteLocation loc)
		{
			return
				_configApp.GetValue(loc == SiteLocation.AU
					? Configuration.EmailFromAddress
					: Configuration.NZEmailFromAddress);
		}

		private string GetEmailReplyAddress(SiteLocation loc)
		{
			return
				_configApp.GetValue(loc == SiteLocation.AU
					? Configuration.EmailReplyAddress
					: Configuration.NZEmailReplyAddress);
		}

		#region properties

		public IConfigurationApplication _configApp { get; set; }
		public IContentApplication _contentApp { get; set; }

		public string LEP_Site { set; private get; }
		public string LEP_Site_URL { set; private get; }
		public string LEP_Phone { set; private get; }
		public string LEP_Fax { set; private get; }

		public string LEP_NZ_Site { set; private get; }
		public string LEP_NZ_Site_URL { set; private get; }
		public string LEP_NZ_Phone { set; private get; }
		public string LEP_NZ_Fax { set; private get; }

		public bool IsExternal { set; private get; }

		#endregion properties
	}
}
