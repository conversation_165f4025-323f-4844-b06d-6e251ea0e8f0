namespace lep.courier.csv
{
	using System;
	using System.IO;
	using System.Collections;
	using System.Collections.Generic;
	using System.Text;
	using System.Data;
	using System.Data.SqlClient;
	using System.Linq;

	using NHibernate;
	using lumen.csv;
	using lep.courier.impl;
using lep.job;

	public class FastwayPostcodeReader: lep.www.BulkCopyImport
	{

		private IList<string> typeLst;
		private IList<string> postcodes;

		public FastwayPostcodeReader()
			: base( "FastWay_Postcode", typeof(FastwayPostcode)  )
		{
		}

		protected override DataTable CreateDataTable()
		{

			DataTable table = new DataTable();

			DataColumn postcodeCol = new DataColumn( "Postcode",typeof( string ) );
			postcodeCol.AllowDBNull = false;
			postcodeCol.MaxLength = 4;
			table.Columns.Add( postcodeCol );

			DataColumn labelCol = new DataColumn( "LabelType",typeof( string ) );
			labelCol.MaxLength = 20;
			labelCol.AllowDBNull = false;
			table.Columns.Add( labelCol );

            DataColumn originCol = new DataColumn("Origin", typeof(string));
			labelCol.MaxLength = 10;
			labelCol.AllowDBNull = false;
            table.Columns.Add(originCol);

			table.PrimaryKey = new DataColumn[] { postcodeCol, originCol };
			return table;
		}


		public override void StartDocument()
		{
			base.StartDocument();

			typeLst = Enum.GetNames( typeof( FastwayLabelType ) ).ToList();
			postcodes = new List<string>();
		}


		protected override bool VerifyHeader( string[] values )
		{
            string[] headers = new string[] { "Postcode", "Origin", "LabelType" };

			for (int i = 0; i < headers.Length; i++) {
				if (i >= values.Length || headers[i].ToLower() != values[i].ToLower()) {
                    AddError("Header row must be: Postcode, Origin, LabelType");
					return false;
				}
			}
			return true;
		}

		protected override bool ProcessRow( DataRow dr,string[] values )
		{
			if (values.Length < 3) {
				AddError( "Not enough columns" );
				return false;
			}

			if (!typeLst.Contains( values[2] )) {
				return false;
			}

            if (String.IsNullOrEmpty(values[0]) || String.IsNullOrEmpty(values[1]))
            {
                AddError( "Postcode and Origin required" );
				return false;
            } else {
                if (!postcodes.Contains(values[0] + "-" + values[1]))
                {
					dr[0] = values[0].ToString();
                    postcodes.Add(values[0] + "-" + values[1]);
					dr[1] = values[2].ToString();
                    if (values[1].ToUpper() == "SSC") {
                        dr[2] = Facility.FG.ToString();
                    }
                    else if (values[1].ToUpper() == "MEL")
                    {
                        dr[2] = Facility.PM.ToString();
                    }
                    else
                    {
                        AddError("Invalid Origin");
                        return false;
                    }
				} else {
					return false;
				}
			}
			return true;
		}
	}
}
