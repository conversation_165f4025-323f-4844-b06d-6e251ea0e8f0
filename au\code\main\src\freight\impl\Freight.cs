using Newtonsoft.Json;

namespace lep.freight.impl
{
    public class Freight : IFreight
    {
        public Freight()
        {
        }

        //public virtual IList<JobCarton> Cartons { get; set; } = new List<JobCarton>();

        public virtual ListOfPackages Packages { get; set; } = new ListOfPackages();

        public virtual bool IsCustom { get; set; }

        public override bool Equals(object obj)
        {
            if (obj is Freight other)
            {
                return IsCustom == other.IsCustom &&
                       (
                           (Packages == null && other.Packages == null) ||
                           (Packages.Equals(other.Packages))
                       );
            }
            return false;
        }

        public override int GetHashCode()
        {
            return base.GetHashCode();
        }

        public override string ToString()
        {
            return JsonConvert.SerializeObject(this, Formatting.Indented);
        }
    }
}