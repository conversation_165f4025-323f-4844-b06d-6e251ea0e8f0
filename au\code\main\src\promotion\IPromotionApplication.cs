﻿using lep.order;
using NHibernate;
using System.Collections.Generic;
using System.Text;

namespace lep.promotion
{
	public interface IPromotionApplication
    {
        // Basic CRUD
        IPromotion NewPromotion();

        IPromotion GetPromotion(string promoCode);

        IPromotion GetPromotion(int promotionId);

        ICriteria GetPromotionsCriteria(string freeText);

        void Save(IPromotion p);

        // Returns Count of a given promotion code
        int PromoCodeCount(string p);

        // create a duplicate of a promotion along with its child promoted products
        IPromotion DuplicatePromotion(IPromotion promotion);

        // apply a promocode to an order
        bool ApplyPromotionToOrder(string promotionCode, IOrder order, StringBuilder stepsFollowed,
            bool ignoreOrderStatus);

        List<CustomerOffer> ValidPromotionsForCustomer(string username);
		List<CustomerOffer> ValidPromotionsForCustomer(int customerId);


		bool IsPromotionValid(IOrder order, StringBuilder stepsFollowed, out decimal benifitAmount);

        bool CalculatePromotion(IOrder order, StringBuilder stepsFollowed);

        void UpdateUsedPromotion(IOrder order);

        // for CRM
        List<PromotionInfo> GetPromotionInfo();
    }
}
