using lep.security;

using Serilog;
using NHibernate;
using System;
using System.Reflection;

namespace lep.content.impl
{
	public class ContentApplication : BaseApplication, IContentApplication
	{
 
		public ContentApplication(ISession sf, ISecurityApplication _securityApp) : base(sf, _securityApp)
		{
		}

 
		public IContent GetContent(int Id)
		{
			// todo
			//AssertPermission( "content.read" );
			return Get<IContent>(Id);
		}

		public void Save(IContent content)
		{
			// todo
			//AssertPermission( "content.update" );
			Log.Debug("[simon] saving (in ContentApplication)");
			base.Save<IContent>(content);
		}

		public void Delete(IContent content)
		{
			// todo
			//AssertPermission( "content.delete" );
			//base.Delete<IContent>( content );
			throw new Exception("The method or operation is not implemented.");
		}
	}
}
