using System;

namespace lep.job.impl
{
	public class Size : ISize
    {
        public Size()
        {
        }

        public Size(int w, int h)
        {
            Width = w;
            Height = h;
        }

        #region ISize Members

        public virtual int Width { get; set; }
        public virtual int Height { get; set; }
        public virtual IPaperSize PaperSize { get; set; }

        public bool CanFit(int width, int height, bool exact = true)
        {
            if (exact)
            {
                return (Width == width && Height == height) || (Width == height && Height == width);
            }
            else
            {
                return (Width >= width && Height >= height) || (Width >= height && Height >= width);
            }
        }

        #endregion ISize Members

        public override bool Equals(object obj)
        {
            if (obj == null)
                return false;
            Size other = obj as Size;
            if (other == null) return false;
            if (Width == other.Width && Height == other.Height && PaperSize.Id == other.PaperSize.Id)
            {
                return true;
            }

            return false;
        }

		public override int GetHashCode()
		{
			return HashCode.Combine(Width, Height, PaperSize);
		}
	}
}
