using System;

namespace lep.job
{
	public interface IStock //: IIdAndName
    {
        int Id { set; get; }
        string Name { set; get; }
		string SType { set; get; }
		bool IsCover { get; set; }
		DateTime DateCreated { get; set; }
        DateTime DateModified { get; set; }
        int GSM { set; get; }
        bool FG_Production { get; set; }
        bool PM_Production { get; set; }
        decimal Thickness { get; set; }
    }
}
