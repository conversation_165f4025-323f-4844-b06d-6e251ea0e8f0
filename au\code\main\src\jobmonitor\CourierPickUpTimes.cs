using lep.courier;
using lep.job;
using System;

namespace lep.jobmonitor
{
	public static class Utils3
	{
		private static TimeSpan
			_12pm = TimeSpan.Parse("12:00"),
			_2pm = TimeSpan.Parse("14:00"),
			_3pm = TimeSpan.Parse("15:00"),
			_4pm = TimeSpan.Parse("16:00"),
			_4_30pm = TimeSpan.Parse("16:30"),
			_2_30pm = TimeSpan.Parse("14:30");

		public static TimeSpan PickupTime(Facility f, CourierType c)
		{
			switch (f)
			{
				case Facility.FG:
					if (c.IsNone) return _3pm;
					else if (c.IsStarTrack) return _2pm;
					else if (c.IsTNT) return _2pm;
					else if (c.IsFastWay) return _2pm;
					else if (c.IsAusPost) return _2pm;
					else if (c.<PERSON>arlow) return _2pm;
					else if (c.IsTollNqx) return _2pm;
					else if (c.IsPickup) return _3pm;
					break;

				case Facility.PM:
					if (c.<PERSON>Aramex) return _3pm;
					else if (c.<PERSON>us<PERSON>ost) return _2_30pm;
					else if (c.<PERSON>ierPleaseV<PERSON>) return _2_30pm;
					else if (c.IsStarTrack) return _4pm;
					else if (c.IsTNT) return _4pm;
					else if (c.IsFastWay) return _4pm;
					else if (c.IsNone) return _3pm;
					else if (c.IsTollNqx) return _12pm;
					else if (c.IsPickup) return _3pm;
					break;
			}
			return _4pm; // default if none is matched
		}
	}
}
