﻿using lep.order;
using System;

namespace lep.onlineTxn
{
	public interface IOnlineTxn
    {
        int OlpId { get; set; } // online payment Id
        IOrder Order { get; set; }
        DateTime PaymentDate { get; set; }
        string VpcAcqResponseCode { get; set; }
        string VpcAmount { get; set; }
        string VpcAuthorizeId { get; set; }
        string VpcAvsResultCode { get; set; }
        string VpcBatchNo { get; set; }
        string VpcCard { get; set; }
        string VpcCommand { get; set; }
        string VpcCscResultCode { get; set; }
        string VpcMerchant { get; set; }
        string VpcMerchTxnRef { get; set; }
        string VpcMessage { get; set; }
        string VpcOrderInfo { get; set; }
        string VpcReceiptNo { get; set; }
        string VpcTransactionNo { get; set; }
        string VpcTxnResponseCode { get; set; }
        string VpcVersion { get; set; }
        bool IsSuccessful { get; set; }
    }
}