﻿using lep.job;
using System;

namespace lep.jobmonitor
{
	/// <summary>
	/// Sc
	/// </summary>
	public interface ISchedule
    {
        int Id { get; set; }
        IJobTemplate Template { get; set; }
        bool Folding { get; set; }

        int StatusInt { get; set; }
        JobStatusOptions Status { get; set; }
        ProductionTiming ScheduleType { get; set; }
        decimal Amber { get; set; }
        decimal Red { get; set; }
        Facility Facility { get; set; }

        DateTime DateCreated { get; set; }
        DateTime DateModified { get; set; }
    }
}