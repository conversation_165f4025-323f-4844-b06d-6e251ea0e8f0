using lep.configuration;
using lep.job;
using RoundedRectangles;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Printing;
using System.IO;
using System.Linq;
using System.Text;
using System.Drawing.Drawing2D;
using System.Drawing.Text;

namespace lep.despatch.impl.label
{
	public class BaseFurtherProcessingLabel : PrintDocument, IDespatchLabel, IDisposable
	{
		protected List<IDisposable> disposables = new List<IDisposable>();
		protected int hTitle = 13; // 13mm title height
		protected int pageNumber = 0;
		protected const int defaultFontSize = 4;  // ~4mm font size
		protected const int titlefontsize = 5;   // ~5mm font size
		protected const int specialInstructionsFontSize = 3; // ~3mm font size
		protected Font defaultFont = new Font(FontConsolas, defaultFontSize, FontStyle.Bold, GraphicsUnit.Millimeter);
		protected Font titleFont = new Font(FontConsolas, titlefontsize, FontStyle.Bold, GraphicsUnit.Millimeter);
		protected Font barCodeFont = new Font(Font3of9, 10, GraphicsUnit.Millimeter);
		protected Font specialInstructionsFont = new Font(FontConsolas, specialInstructionsFontSize, FontStyle.Bold, GraphicsUnit.Millimeter);
		protected Font smallFont = new Font(FontConsolas, 2, FontStyle.Regular, GraphicsUnit.Millimeter);
		protected Font quantityBoldFont = new Font(FontConsolas, 4, FontStyle.Bold, GraphicsUnit.Millimeter);
		protected string fmt = "{0,-8}: {1}";

		protected StringFormat titleAlignment = new StringFormat()
		{
			LineAlignment = StringAlignment.Near,
			Alignment = StringAlignment.Center,
			Trimming = StringTrimming.None
		};

		protected StringFormat middleFormat = new StringFormat
		{
			Trimming = StringTrimming.EllipsisCharacter,
			FormatFlags = StringFormatFlags.NoWrap | StringFormatFlags.DisplayFormatControl,
			Alignment = StringAlignment.Near
		};

		protected StringFormat rightFormat = new StringFormat
		{
			Trimming = StringTrimming.EllipsisCharacter,
			FormatFlags = StringFormatFlags.NoWrap | StringFormatFlags.DisplayFormatControl,
			Alignment = StringAlignment.Near
		};

		protected StringFormat frontTopFormat = new StringFormat()
		{
			LineAlignment = StringAlignment.Near,
			Alignment = StringAlignment.Center,
			Trimming = StringTrimming.None
		};

		protected StringFormat frontBottomFormat = new StringFormat()
		{
			LineAlignment = StringAlignment.Far,
			Alignment = StringAlignment.Center,
			Trimming = StringTrimming.None
		};

		protected StringFormat InstructionsFormat = new StringFormat
		{
			Trimming = StringTrimming.None,
			FormatFlags = StringFormatFlags.FitBlackBox | StringFormatFlags.DisplayFormatControl,
			Alignment = StringAlignment.Near
		};

		protected StringFormat quantityAlignment = new StringFormat()
		{
			LineAlignment = StringAlignment.Center,
			Alignment = StringAlignment.Center,
			Trimming = StringTrimming.None
		};

		protected const string Font3of9 = "Free 3 of 9";
		protected const string FontConsolas = "Verdana";
		// Paper size in mm - Graphics unit set to millimeters
		protected const float PaperHeightMM = 177f;
		protected const float PaperWidthMM = 103f;

		// Keep pixel values for compatibility with PrinterSettings
		protected const int PaperHeight = 697;
		protected const int PaperWidth = 406;

		// Margins in mm
		protected const float MarginWidthMM = 2f;
		protected const float MarginHeightMM = 2f;

		// For pixel-based operations (compatibility)
		protected int MarginWidth => (int)(MarginWidthMM * 100f / 25.4f);
		protected int MarginHeight => (int)(MarginHeightMM * 100f / 25.4f);

		protected int width => PaperWidth - MarginWidth * 2;
		protected int left => MarginWidth;
		protected int top => MarginHeight;

		protected StringBuilder basicInformation = new StringBuilder();
		protected StringBuilder processingText1 = new StringBuilder();
		protected string strInstructions = "";
		protected StringBuilder processingText = new StringBuilder();
		protected StringBuilder processingText2 = new StringBuilder();

		// Coordinates in mm - much cleaner now!
		// Inner sticker area: (87, 27) to (165, 82) mm
		protected RectangleF rBasicInfoTxt => new RectangleF(27f,87f,  24f, 25f);
		protected RectangleF rThumb => new RectangleF(87f, 52f, 24f, 25f);
		protected RectangleF rRoundedCorner => new RectangleF(67f, 57f, 20f, 20f);
		protected RectangleF rTitle => new RectangleF(2f, 2f, 99f, 13f);

		// EDD positioning (4mm, 90mm, 24mm wide)
		protected RectangleF rEDD => new RectangleF(4f, 90f, 24f, 8f);
		protected IJob job;
		protected Pen penBlack3 = new Pen(Color.Black, 1);
		protected Pen penRed3 = new Pen(Color.Red, 1);

		public virtual IJob Job
		{
			get { return job; }

			set {
				job = value;
				FormatPrintContent();
			}
		}

		public string PrinterAndTray { get; set; }
		public string PrintFileName { get; set; }
		public IConfigurationApplication ConfigurationApplication { get; set; }

		public BaseFurtherProcessingLabel()
		{
			disposables.Add(defaultFont);
			disposables.Add(titleFont);
			disposables.Add(barCodeFont);
			disposables.Add(specialInstructionsFont);
			disposables.Add(smallFont);
			disposables.Add(quantityBoldFont);

			disposables.Add(penBlack3);
			disposables.Add(penRed3);
		}

		void IDespatchLabel.Print()
		{
			Print();
		}

		void IDisposable.Dispose()
		{
			base.Dispose();
			foreach (var d in disposables)
			{
				d.Dispose();
			}
		}

		public void SetupPrintProperties()
		{
			var ps = new PrinterSettings();
			PrintUtils.ConfigurePrinterSettings(PrinterAndTray, ref ps);

			PrinterSettings = ps;
			DefaultPageSettings.PaperSize = new PaperSize("103 x 177 mm", 406, 697);

			ps.PrintToFile = ps.PrinterName == "Microsoft XPS Document Writer";
			ps.DefaultPageSettings.Margins = new Margins(MarginWidth, MarginWidth, MarginHeight, MarginHeight);

			if (ps.PrintToFile)
			{
				ps.PrintFileName = PrintFileName;
			}

			PrinterSettings = ps;
		}

		public virtual void FormatPrintContent()
		{
		}

		protected override void OnPrintPage(PrintPageEventArgs e)
		{
			base.OnPrintPage(e);
			DrawGrid(g);
		}

		protected void SetupGraphics(Graphics g)
		{
			g.SmoothingMode = SmoothingMode.None;
			g.TextRenderingHint = TextRenderingHint.SingleBitPerPixel;
			g.PixelOffsetMode = PixelOffsetMode.None;
			g.CompositingQuality = CompositingQuality.HighQuality;
			g.InterpolationMode = InterpolationMode.HighQualityBicubic;
		}

		// UNCOMMENT THIS TO SHOW 5MM GRID FOR FINE-TUNING
		protected void DrawGrid(Graphics g)
		{
			using (var redPen = new Pen(Color.Red, 0.1f)) // 0.1mm thick line
			{
				// Draw vertical lines every 5mm
				for (float x = 0; x <= PaperWidthMM; x += 5f)
				{
					g.DrawLine(redPen, x, 0, x, PaperHeightMM);
				}

				// Draw horizontal lines every 5mm
				for (float y = 0; y <= PaperHeightMM; y += 5f)
				{
					g.DrawLine(redPen, 0, y, PaperWidthMM, y);
				}

				// Draw red dots at intersections
				for (float x = 0; x <= PaperWidthMM; x += 5f)
				{
					for (float y = 0; y <= PaperHeightMM; y += 5f)
					{
						g.FillEllipse(Brushes.Red, x - 0.5f, y - 0.5f, 1f, 1f); // 1mm dots
					}
				}
			}
		}

		protected void FormatBasicInformation()
		{
			basicInformation.Clear();
			basicInformation.AppendFormat(fmt, "Job Name", Job.Name).AppendLine();
			basicInformation.AppendFormat(fmt, "Order #", Job.Order.OrderNr).AppendLine();
			basicInformation.AppendFormat(fmt, "Job # ", Job.JobNr).AppendLine();

			if (Job.ReOrderSourceJobId != 0)
			{
				basicInformation.AppendFormat(fmt, "Orig Job #", Job.ReOrderSourceJobId.ToString()).AppendLine();
			}

			if (!job.HasSplitDelivery)
			{
				basicInformation.AppendFormat(fmt, "Courier", Job.Order.Courier).AppendLine();
			}
			else
			{
				basicInformation.AppendFormat(fmt, "Courier", "SPLIT Delivery").AppendLine();
			}

			if (Job.Order.Jobs.Count() > 1)
			{
				basicInformation.Append("*** Multi Job Order ***");
			}
		}

		protected void FormatCommonSpecialInstructions()
		{
			strInstructions = "";
			if (Job.Order.CustomerLogoRequired)
			{
				strInstructions = "*** Logo label required ***" + '\n';
			}
			if (Job.SendSamples == true)
			{
				strInstructions += "*** Send samples ***" + '\n';
			}

			if (Job.SpecialInstructions != null && Job.SpecialInstructions.Length > 0)
			{
				strInstructions += Job.SpecialInstructions + "\n";
			}

			if (Job.ProductionInstructions != null && Job.ProductionInstructions.Length > 0)
			{
				strInstructions += Job.ProductionInstructions + "\n";
			}

			strInstructions += "\n" + job.TopLevelPackages();

			if (job.HasSplitDelivery)
			{
				strInstructions += "\n" + job.Order.SplitsSummary();
			}
		}

		protected void DrawBarcode(Graphics g)
		{
			// Position barcode to the right of basic job info (112mm, 40mm)
			g.TranslateTransform(112f, 40f);
			g.RotateTransform(-90.0f);
			g.DrawString("*" + Job.Barcode + "*", barCodeFont, Brushes.Black, new PointF(0, 0));
			g.ResetTransform();
		}

		protected void DrawThumbnail(Graphics g)
		{
			g.FillRectangle(new SolidBrush(Color.FromArgb(64, Color.Gray)), rThumb);

			var thumbs2 = LepGlobal.Instance.GetThumbs(Job).Where(fn => !fn.Name.Contains("_bk"));
			if (thumbs2.Any())
			{
				using (Stream fs = thumbs2.First().OpenRead())
				{
					using (var image = Image.FromStream(fs, true))
					{
						g.DrawImage(image, rThumb);
						fs.Close();
					}
				}
			}
		}

		protected void DrawEDD(Graphics g)
		{
			if (Job.Order.DispatchEst != null)
			{
				var edd = "EDD: " + Job.Order.DispatchEst.Value.ToString("dd/MMM/yy");
				var sz = g.MeasureString(edd, defaultFont);
				// Position at 4mm, 90mm with 24mm width constraint
				g.DrawRectangle(penBlack3, rEDD.X, rEDD.Y, Math.Min(sz.Width + 0.5f, rEDD.Width), sz.Height + 0.5f);
				g.DrawString(edd, defaultFont, Brushes.Black, rEDD, middleFormat);
			}
		}

		protected void DrawRoundedCorndersBox(Graphics g)
		{
			// draw sticker
			var rRoundedCorner = new Rectangle(274, 224, 78, 78);

			g.DrawString(job.Quantity.ToString(), quantityBoldFont, Brushes.Black, rRoundedCorner, quantityAlignment);
			g.DrawString("Front top", smallFont, Brushes.Black, rRoundedCorner, frontTopFormat);
			g.DrawString("Front bottom", smallFont, Brushes.Black, rRoundedCorner, frontBottomFormat);
			var radious = 12;
			// figure corners
			var rc = RectangleCorners.None;
			if (Job.TLround)
				rc = rc | RectangleCorners.TopLeft;
			if (Job.TRround)
				rc = rc | RectangleCorners.TopRight;
			if (Job.BLround)
				rc = rc | RectangleCorners.BottomLeft;
			if (Job.BRround)
				rc = rc | RectangleCorners.BottomRight;

			GraphicsPath path = null;
			if (Job.RoundOption == RoundOption.None)
			{
				g.DrawRectangle(penBlack3, rThumb);
				g.DrawRectangle(penBlack3, rRoundedCorner);
			}
			else
			{
				// Convert RectangleF to Rectangle for RoundedRectangle.Create
				var thumbRect = Rectangle.Round(rThumb);
				var cornerRect = Rectangle.Round(rRoundedCorner);

				path = RoundedRectangle.Create(thumbRect, 1, rc);
				g.DrawPath(Pens.Black, path);
				var path2 = RoundedRectangle.Create(cornerRect, radious, rc);
				disposables.Add(path2);
				g.DrawPath(Pens.Black, path2);
			}

			if (path != null)
				g.SetClip(path);
			if (path != null)
			{
				g.ResetClip();
				disposables.Add(path);
			}
		}

		protected bool specialInstructionsOnNextPage = false;

		protected void DrawSpecialInstructions(PrintPageEventArgs e, Graphics g)
		{
			SizeF measure = g.MeasureString(strInstructions, specialInstructionsFont);
			var hInstructions = (int)measure.Height;
			if (pageNumber == 1)
			{
				var rInstructions = new Rectangle(left, 610, width, 80);
				if (rInstructions.Height > measure.Height)
				{
					specialInstructionsOnNextPage = false;
					g.DrawString(strInstructions, specialInstructionsFont, Brushes.Black, rInstructions, InstructionsFormat);
				}
				else
				{
					e.HasMorePages = true;
					specialInstructionsOnNextPage = true;
					g.DrawString("Instructions on next page...", specialInstructionsFont, Brushes.Red, rInstructions, InstructionsFormat);
				}
			}
			else if (pageNumber == 2 && specialInstructionsOnNextPage)
			{
				strInstructions = $"Job #: {Job.Id}\n\n" + strInstructions;
				var rInstructions = new Rectangle(left, top, width + MarginWidth, 650);
				g.DrawString(strInstructions, specialInstructionsFont, Brushes.Black, rInstructions, InstructionsFormat);
				pageNumber = 0;
				specialInstructionsOnNextPage = false;
			}
		}
	}
}
