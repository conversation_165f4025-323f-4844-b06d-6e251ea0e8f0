using Newtonsoft.Json;
using System.Collections.Generic;
using System.Diagnostics;

namespace lep.job.impl
{
	[DebuggerDisplay("JobOptionSpecSize {Id}  {Template.Name}, {PaperSize.Name} has {StockOptions.Count} StockOptions")]
    public class JobOptionSpecSize : IJobOptionSpecSize
    {
        public JobOptionSpecSize()
        {
        }

        #region IJobOptionSpecSize Members

        public virtual int Id { get; set; }

        [JsonIgnore]
        public virtual IJobTemplate JobTemplate { get; set; }

        public virtual IPaperSize PaperSize { get; set; }

        public virtual IList<IJobOptionSpecStock> StockOptions { get; set; } = new List<IJobOptionSpecStock>();

        #endregion IJobOptionSpecSize Members
    }
}