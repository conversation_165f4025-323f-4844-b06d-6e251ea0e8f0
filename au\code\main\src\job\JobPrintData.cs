using System;

namespace lep.job
{
	public class JobPrintData
    {
        public Boolean ProofsRequired { get; set; }
        public int NumProofsSentA1 { get; set; }
        public int NumProofsSentA2 { get; set; }
        public int NumProofsSentA3 { get; set; }

        public int OneSidedSheets { get; set; }
        public bool OneSidedA1 { get; set; }
        public bool OneSidedA2 { get; set; }

        public int WorkAndTurnSheets { get; set; }
        public bool WorkAndTurnA1 { get; set; }
        public bool WorkAndTurnA2 { get; set; }

        public int WorkAndTumbleSheets { get; set; }
        public bool WorkAndTumbleA1 { get; set; }
        public bool WorkAndTumbleA2 { get; set; }

        public int SheetWorkSheets { get; set; }
        public bool SheetWorkA1 { get; set; }
        public bool SheetWorkA2 { get; set; }

        public int CoverSheets { get; set; }
        public bool CoverA1 { get; set; }
        public bool CoverA2 { get; set; }

        public Boolean Scoring { get; set; }
        public string ScoringInstructions { get; set; }
        public Boolean Perforating { get; set; }
        public string PerforatingInstructions { get; set; }
        public string DieCutting { get; set; }
        public string RequestedPackaging { get; set; }
    }
}