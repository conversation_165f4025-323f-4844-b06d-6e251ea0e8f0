using lep.security;

using Serilog;
using System;
using System.Collections.Generic;
using System.Reflection;
using System.Threading;

namespace lep.cron.impl
{
	public class CronApplication : ICronApplication, IInitializingObject
	{
		//// private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);
		private static object MONITOR = new object();

		public CronApplication()
		{
		}

		#region my data

		private DateTime lastTickInstant;

		#endregion my data

		#region services

		protected ISecurityApplication securityApp;

		#endregion services

		/*
        public void CronServiceThread()
        {
            while (true) {
                var session = SessionFactory.OpenSession();
                CurrentSessionContext.Bind( session );
                try {
                    long start_t = System.DateTime.Now.Ticks;

                    securityApp.DoPrivileged( delegate {
                        foreach( ThreadStart job in jobs ) {
                            try {
                                job();
                            } catch (Exception ex) {
                                Log.Error( ex.Message, ex );
                            }
                        }

                        long delta_t = (System.DateTime.Now.Ticks - start_t) / 10000;
                        if (log.IsInfoEnabled) {
                            Log.Information string.Format( "cron tick. processing took {0}ms", delta_t ) );
                        }
                    });
                } catch (Exception ex) {
                    Log.Error( ex.Message, ex );
                } finally {
                    session.Dispose();
                    CurrentSessionContext.Unbind( SessionFactory );
                }

                Thread.Sleep( TickInterval );
            }
        }
        */

		public TimeSpan MinimumTickInterval
		{
			set { minimumTickInterval = value; }
		}

		public IList<ThreadStart> Jobs
		{
			set { jobs = value; }
		}

		public ISecurityApplication SecurityApplication
		{
			set { securityApp = value; }
		}

		public void Trigger()
		{
			var locked = false;
			try
			{
				locked = Monitor.TryEnter(MONITOR);
				if (locked)
				{
					if (0 < DateTime.Now.CompareTo(lastTickInstant.Add(minimumTickInterval)))
					{
						lastTickInstant = DateTime.Now;

						var start_t = DateTime.Now.Ticks;

						securityApp.DoPrivileged(delegate
						{
							foreach (var job in jobs)
							{
								try
								{
									job();
									Log.Debug(string.Format("cron {0}.{1}. {2}ms", job.Target.GetType(), job.Method.Name,
										(DateTime.Now.Ticks - start_t) / 10000));
								}
								catch (Exception ex)
								{
									Log.Error(ex.Message, ex);
								}
							}

							var delta_t = (DateTime.Now.Ticks - start_t) / 10000;
							Log.Debug(string.Format("cron tick. processing took {0}ms", delta_t));
						});
					}
				}
				else
				{
					Log.Debug("cron tick. cron was locked");
				}
			}
			finally
			{
				if (locked)
				{
					Monitor.Pulse(MONITOR);
					Monitor.Exit(MONITOR);
				}
			}
		}

		public void AfterPropertiesSet()
		{
			if (jobs == null)
			{
				jobs = new List<ThreadStart>();
			}

			/*
            if (0 != Jobs.Count) {
                thread = new Thread( new ThreadStart( CronServiceThread ) );
                thread.IsBackground = true;
                thread.Name = "Cron";
                thread.Start();
            }
            */
		}

		#region configuration

		private TimeSpan minimumTickInterval = new TimeSpan(0, 1, 0);
		private IList<ThreadStart> jobs;

		#endregion configuration
	}
}
