//using System;
//using System.IO;
//using System.Linq;
//using System.Reflection;
//using System.Text;
//using Serilog;
//using lep.configuration;
//using lep.email;
//using lep.extensionmethods;
//using lep.job;
//using lep.security;
//using NHibernate;
//using NHibernate.Criterion;
//using Config = lep.configuration.Configuration;

//namespace lep.backupdelete.impl
//{
//    public class BackupDeleteApplication : BaseApplication, IBackupDeleteApplication
//    {

//        public BackupDeleteApplication (ISession sf, ISecurityApplication _securityApp)
//            : base(sf, _securityApp)
//        {
//        }


//        #region Private Methods

//        private void DumpLogInfo()
//        {
//            BackupLog.InformationString.Format(@"
//				AllowedMaxDailyUsage	{0} MB
//                currentAvgDailyUsage	{1} MB
//                Free 					{2} MB
//                Remaining				{3} days
//                Warn in					{4} days
//                Cleanup when remains	{5} days",
//                _allowedMaxDailyUsage,
//                _currentAvgDailyUsage,
//                GetFreeSpace(),
//                _spaceRemainingInDays,
//                _lowspaceWarningThreshold,
//                _cleanupThreshold));
//        }

//        #endregion

//        #region Constants

//        private const string DeleteCommand = "rmdir -r {0}";

//        public const int Interval = 90;

//        public const int Megabytes = 1024*1024;

//        private const string Pattern = "????????";

//        #endregion

//        #region Readonly

//        private static readonly ILog BackupLog =
//            LogManager.GetLogger(String.Format("{0}.Backup", MethodBase.GetCurrentMethod().DeclaringType.ToString()));

//        // private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

//        #endregion

//        #region Fields

//        private long _allowedMaxDailyUsage;

//        private string _backupPath;

//        private long _cleanupThreshold;

//        private long _currentAvgDailyUsage;

//        private DirectoryInfo _diOrders;

//        private DirectoryInfo _diRuns;

//        private string _emailTo = "";

//        private long _lowspaceWarningThreshold;

//        private string _resourcePath;

//        private long _spaceRemainingInDays;

//        #endregion

//        #region Properties

//        public IConfigurationApplication ConfigurationApplication { get; set; }

//        public IEmailApplication EmailApplication { get; set; }

//        public IJobApplication JobApplication { get; set; }

//        #endregion

//        #region Public Methods

//        public void CalculateUsage()
//        {
//            try
//            {
//                BackupLog.InformationMethodBase.GetCurrentMethod().Name.ToString());
//                var history = Session.CreateCriteria(typeof(IUsage))
//                    .Add(Restrictions.Lt("OnDate", DateTime.Today))
//                    .SetMaxResults(Interval - 1)
//                    .AddOrder(Order.Desc("Id"))
//                    .List<IUsage>();

//                long usedToday = 0;
//                var remainingToday = GetFreeSpace();

//                if (history.Count == 0)
//                {
//                    // running for the very first time
//                    usedToday = GetUsageForGivenDate(DateTime.Today);
//                }
//                else if (history[0].Used == 0)
//                {
//                    usedToday = GetUsageForGivenDate(DateTime.Today);
//                }
//                else
//                {
//                    usedToday = history[0].Remaining - remainingToday;
//                }

//                IUsage todaysUsage = new Usage {OnDate = DateTime.Today, Remaining = remainingToday, Used = usedToday};
//                Session.Save(todaysUsage);
//                history.Add(todaysUsage);

//                _currentAvgDailyUsage = (long) history.Average(h => (double) h.Used);

//                //avoid divide by 0 error
//                _spaceRemainingInDays = _currentAvgDailyUsage > 0
//                    ? remainingToday/_currentAvgDailyUsage
//                    : _lowspaceWarningThreshold + 1;
//            }
//            catch (Exception ex)
//            {
//                BackupLog.Error("Error Calculating Average ", ex);
//            }
//        }

//        /// <summary>
//        /// no point in summing up the folder size if its gonna get deleted anyways!
//        /// getting disk free space after delete would be much faster
//        ///
//        /// BUTTHEN the deletetion has to be implemented in code not in a batch file  DeleteOldFiles3()
//        /// </summary>
//        public void DeleteOldFiles2()
//        {
//            BackupLog.InformationMethodBase.GetCurrentMethod().Name.ToString());
//            var spaceNeedsToGetReclaimed = _lowspaceWarningThreshold*_currentAvgDailyUsage;

//            // get combined sorted dir listing interspersed in orders and runs folder
//            var dirs = from d in
//                (from d in _diOrders.GetDirectories(Pattern)
//                    orderby d.Name
//                    select d).Union(from d in _diRuns.GetDirectories(Pattern)
//                    orderby d.Name
//                    select d)
//                orderby d.Name, d.CreationTime
//                select d;

//            spaceNeedsToGetReclaimed *= Megabytes;
//            BackupLog.InformationString.Format("Need to free {0} Mbytes", spaceNeedsToGetReclaimed));
//            long spaceReclaimedSoFar = 0;

//            var deleteCommands = new StringBuilder();

//            foreach (var d in dirs)
//            {
//                spaceReclaimedSoFar += d.Size();
//                if (spaceReclaimedSoFar > spaceNeedsToGetReclaimed)
//                    break;

//                deleteCommands.AppendLine(String.Format(DeleteCommand, d.FullName));
//                //backupLog.Information String.Format( "robocopy {0}  {1} /MOVE /E /S /COPYALL", d.FullName, backupPath ) );
//            }

//            BackupLog.InformationdeleteCommands.ToString());
//            SendEmailDeleteScript(deleteCommands);
//        }

//        private DateTime cronLogTime;

//        public void DoDailyTasksBasedOnRemainingSpace()
//        {
//            if (DateTime.Now.Hour == 11 && cronLogTime.Date != DateTime.Now.Date)
//            {
//                try
//                {
//                    BackupLog.InformationMethodBase.GetCurrentMethod().Name.ToString());
//                    LoadSettings();
//                    CalculateUsage();
//                    DumpLogInfo();

//                    if (_currentAvgDailyUsage > _allowedMaxDailyUsage)
//                    {
//                        SendEmailUsageFasterThanExpected();
//                    }

//                    if (_spaceRemainingInDays < _lowspaceWarningThreshold)
//                    {
//                        SendEmailRunningOnLowSpace();
//                    }

//                    if (_spaceRemainingInDays < _cleanupThreshold)
//                    {
//                        DeleteOldFiles2();
//                        //DeleteOldFiles3();
//                    }
//                }
//                catch (Exception ex)
//                {
//                    BackupLog.Error(ex.Message, ex);
//                }
//                finally
//                {
//                    cronLogTime = DateTime.Now;
//                }
//            }
//        }

//        public long GetFreeSpace()
//        {
//            var drive = new DriveInfo(_resourcePath[0].ToString());
//            return (long) (drive.AvailableFreeSpace/Megabytes);

//            //return 10;
//        }

//        // Gets Megabytes used on a given day for a given Date
//        public long GetUsageForGivenDate(DateTime givenDate)
//        {
//            BackupLog.InformationMethodBase.GetCurrentMethod().Name.ToString());
//            long totalSize = 0;
//            var givenDateSuffix = givenDate.ToString("yyyyMMdd");
//            var basePath = ConfigurationApplication.DataDirectory.FullName;
//            var orderFolderForGivenDate = Path.Combine(Path.Combine(basePath, "orders"), givenDateSuffix);
//            var runFolderForGivenDate = Path.Combine(Path.Combine(basePath, "runs"), givenDateSuffix);

//            var orderFolderSize = new DirectoryInfo(orderFolderForGivenDate).Size();
//            var runFolderSize = new DirectoryInfo(runFolderForGivenDate).Size();

//            BackupLog.InformationorderFolderForGivenDate + " Order Folder Useage : " + orderFolderSize/Megabytes);
//            BackupLog.InformationrunFolderForGivenDate + " Run   Folder Useage : " + runFolderSize/Megabytes);
//            totalSize = runFolderSize + orderFolderSize;

//            BackupLog.InformationgivenDateSuffix + " Total        Useage : " + runFolderSize/Megabytes);
//            return totalSize/Megabytes;
//        }

//        public void LoadSettings()
//        {
//            BackupLog.InformationMethodBase.GetCurrentMethod().Name.ToString());

//            try
//            {
//                _emailTo = ConfigurationApplication.GetValue(Config.EmailErrorAddress);

//                _allowedMaxDailyUsage = Convert.ToInt32(ConfigurationApplication.GetValue(Config.BackupDailySpaceUsage));
//                _lowspaceWarningThreshold =
//                    Convert.ToInt32(ConfigurationApplication.GetValue(Config.BackupDeletionWarningThreshold));
//                _cleanupThreshold = Convert.ToInt32(ConfigurationApplication.GetValue(Config.BackupDeletionThreshold));

//                _resourcePath = ConfigurationApplication.GetValue(Config.ResourceSourcePath);
//                _backupPath = ConfigurationApplication.GetValue(Config.ResourceBackupPath);

//                _diOrders = new DirectoryInfo(Path.Combine(_resourcePath, "orders"));
//                if (!_diOrders.Exists)
//                {
//                    throw new DirectoryNotFoundException(
//                        String.Format("Resource Path {0} was invalid or does not exist", _diOrders.FullName));
//                }

//                _diRuns = new DirectoryInfo(Path.Combine(_resourcePath, "runs"));
//                if (!_diRuns.Exists)
//                {
//                    throw new DirectoryNotFoundException(
//                        String.Format("Resource Path {0} was invalid or does not exist", _diRuns.FullName));
//                }
//            }
//            catch (Exception ex)
//            {
//                BackupLog.Error(ex.Message, ex);
//            }
//        }

//        public void SendEmailDeleteScript(StringBuilder deleteCommands)
//        {
//            BackupLog.Information("Sending Delete Script to Admin...");

//            var email = EmailApplication.NewTextMessage(SiteLocation.AU);
//            EmailApplication.ErrorMessageAddressing(email);
//            email.To = _emailTo;
//            email.Subject = "Please run these script to clear disk space";
//            email.Body = deleteCommands.ToString();
//            EmailApplication.Send(email);
//        }

//        public void SendEmailRunningOnLowSpace()
//        {
//            BackupLog.Information
//                String.Format("Sending Low on Space warning email. Current free space allows for {0} days order data", 0));
//            var email = EmailApplication.NewTextMessage(SiteLocation.AU);
//            EmailApplication.ErrorMessageAddressing(email);
//            email.Subject = "System running on low space";
//            email.To = _emailTo;
//            email.Body = email.Subject;
//            email.Body += String.Format(@"
//The system can only cater for additional {0} days of order data
//at the current average consumption rate of {1} Megabytes per day", _spaceRemainingInDays, _currentAvgDailyUsage);
//            EmailApplication.Send(email);
//        }

//        public void SendEmailUsageFasterThanExpected()
//        {
//            BackupLog.Information("Using Disk space faster than expected. Sending Warning Email");
//            var email = EmailApplication.NewTextMessage(SiteLocation.AU);
//            EmailApplication.ErrorMessageAddressing(email);
//            email.To = _emailTo;
//            email.Subject = "Using Disk space faster than expected";
//            email.Body = email.Subject;
//            email.Body += String.Format(@"
//The system  is using up disk space at a faster rate of {0} Megabytes per day
//than expected maximum {1} Megabytes per day", _currentAvgDailyUsage, _allowedMaxDailyUsage);
//            EmailApplication.Send(email);
//        }

//        #endregion
//    }
//}
