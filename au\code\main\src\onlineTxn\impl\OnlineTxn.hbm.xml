<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2"
                   assembly="lep"
                   namespace="lep.onlineTxn"
                   auto-import="true"
				   default-cascade="all">
    <class name="IOnlineTxn" table="`OnlinePayments`"  discriminator-value="null">
        <cache usage="read-write" />
        <id name="OlpId" column="OlpId" type="Int32" unsaved-value="0">
            <generator class="identity" />
        </id>
        <discriminator column="OlpId" type="Int32" insert="false" />
        <many-to-one name="Order" column="orderid" class="lep.order.IOrder, lep" not-null="true" cascade="none" />
         <property name="PaymentDate" column="PaymentDate" type="lumen.hibernate.type.DateTimeType, lumen" not-null="false" />
            <property column="vpc_AVSResultCode"     type="String" name="VpcAvsResultCode"    length="255" />
        <property column="vpc_AcqResponseCode"   type="String" name="VpcAcqResponseCode"  length="255" />
        <property column="vpc_Amount"            type="String" name="VpcAmount"           length="255" />
        <property column="vpc_AuthorizeId"       type="String" name="VpcAuthorizeId"      length="255" />
        <property column="vpc_BatchNo"           type="String" name="VpcBatchNo"          length="255" />
        <property column="vpc_CSCResultCode"     type="String" name="VpcCscResultCode"    length="255" />
        <property column="vpc_Card"              type="String" name="VpcCard"             length="255" />
        <property column="vpc_Command"           type="String" name="VpcCommand"          length="255" />
        <property column="vpc_MerchTxnRef"       type="String" name="VpcMerchTxnRef"      length="255" />
        <property column="vpc_Merchant"          type="String" name="VpcMerchant"         length="255" />
        <property column="vpc_Message"           type="String" name="VpcMessage"          length="255" />
        <property column="vpc_OrderInfo"         type="String" name="VpcOrderInfo"        length="255" />
        <property column="vpc_ReceiptNo"         type="String" name="VpcReceiptNo"        length="255" />
        <property column="vpc_TransactionNo"     type="String" name="VpcTransactionNo"    length="255" />
        <property column="vpc_TxnResponseCode"   type="String" name="VpcTxnResponseCode"  length="255" />
        <property column="vpc_Version"           type="String" name="VpcVersion"          length="255" />
        <property name="IsSuccessful" column="IsSuccessful" type="YesNo" />
        <subclass name="lep.onlineTxn.impl.OnlineTxn, lep" proxy="IOnlineTxn" discriminator-value="not null" />
    </class>
</hibernate-mapping>