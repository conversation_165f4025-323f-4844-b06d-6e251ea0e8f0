/*
using System;
using System.Collections.Generic;
using System.Collections;
using System.Text;
using System.Text.RegularExpressions;
using System.IO;
using NHibernate.Criterion;
using NHibernate;
using System.Linq;

using lep.job;
using lep.order;
using lep.freight.csv;
using lep.configuration;
using lep.courier;
using Serilog;
using System.Reflection;
using lep.security;
using lep.src.freight.impl;
using Newtonsoft.Json.Linq;

namespace lep.freight.impl
{
	/// <summary>
	///
	/// </summary>
	public class CartonApplication : BaseApplication, ICartonApplication
	{
		// private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		public CartonApplication(ISession sf, ISecurityApplication _securityApp,
 IConfigurationApplication configApp, IJobApplication jobApp) : base(sf, _securityApp)
		{
			ConfigurationApplication = configApp;
			JobApplication = jobApp;
		}

		#region paperpack

		public IList<string> ImportPaperPack(Stream file, out int processCount)
		{
			processCount = 0;

			CartonOptionReader reader = new CartonOptionReader(Session);
			reader.JobApplication = JobApplication;
			//todo: iwen
			//reader.SessionFactory = sessionFactory;
			try {
				reader.Parse(new StreamReader(file));
				processCount = reader.ProcessCount;
			}
			catch (Exception e) {
				IList<string> errors = reader.Errors;
				errors.Add(e.ToString());
				return errors;
			}
			return reader.Errors;
		}

		public IList ExportPaperPack()
		{
			return Session.CreateCriteria(typeof(CartonOption))
				.CreateAlias("Stock", "st")
				.CreateAlias("Finish", "fi")
				.CreateAlias("Fold", "fo")
				.CreateAlias("Carton", "ca")
				.CreateAlias("JobTemplate", "j")
				.SetProjection(Projections.ProjectionList()
					.Add(Projections.Property("j.Name"))
					.Add(Projections.Property("st.Name"))
					.Add(Projections.Property("fi.Name"))
					.Add(Projections.Property("fo.Name"))
					.Add(Projections.Property("ca.Code"))
					.Add(Projections.Property("Magnet"))
					.Add(Projections.Property("Capacity")))
				.List();
		}

		//private IList<KeyValuePair<ICarton, int>> GetCartonOptions(IJob job)
		//{
		//	return	LepCartonFinder.FindBest(job);
		//}

		private IList<KeyValuePair<ICarton, int>> GetCartonOptions(IJobTemplate jobTemplate, IStock stock, IPaperSize finish, IPaperSize fold, bool hasMagnet)
		{
			var options = Session.CreateCriteria(typeof(CartonOption))
				.Add(Expression.Eq("JobTemplate", jobTemplate))
				.Add(Expression.Eq("Stock", stock))
				.Add(Expression.Eq("Magnet", stock))
				.Add(Expression.Eq("Finish", finish.FreightPaperSize != null ? finish.FreightPaperSize : finish))
				.Add(Expression.Eq("Fold", fold.FreightPaperSize != null ? fold.FreightPaperSize : fold))
				.AddOrder(Order.Desc("Capacity"))
				.List<CartonOption>();

			int freightModifier = 1;
			if (finish.FreightPaperSize != null) {
				freightModifier = finish.FreightPaperSizeQty.Value;
			}

			return options.Select(o => new KeyValuePair<ICarton, int>(o.Carton, o.Capacity / freightModifier)).ToList();
		}

		private int GetExtraPrintByType(JobTypeOptions jobType)
		{
			if (jobType != JobTypeOptions.BusinessCard && jobType != JobTypeOptions.BusinessCardNdd && jobType != JobTypeOptions.BusinessCardSdd && jobType != JobTypeOptions.PresentationFolder) {
				return GetExtraPrint();
			}

			return 0;
		}

		public int GetExtraPrint()
		{
			if (ConfigurationApplication == null) return 0;
			return Convert.ToInt32(ConfigurationApplication.GetValue(Configuration.FreightExtraPrint));
		}

		public void SaveExtraPrint(int amount)
		{
			ConfigurationApplication.SetValue(Configuration.FreightExtraPrint, amount.ToString());
		}

		public int GetOverpack()
		{
			if (ConfigurationApplication == null) return 0;
			return Convert.ToInt32(ConfigurationApplication.GetValue(Configuration.FreightOverpack));
		}

		public void SaveOverpack(int over)
		{
			ConfigurationApplication.SetValue(Configuration.FreightOverpack, over.ToString());
		}

		#endregion paperpack

		#region carton

		//public IList<ICarton> ListCarton(int? packagingLevel)
		//{
		//	//var query = Session.CreateCriteria(typeof(ICarton));
		//	//if (packagingLevel.HasValue) {
		//	//	query = query.Add(Expression.Eq("PackagingLevel", packagingLevel.Value));
		//	//}
		//	//return query.List<ICarton>();

		//	return CartonFinder.Instance.ListCarton(packagingLevel);
		//}

		//public ICarton GetCarton(string code)
		//{
		//	return CartonFinder.Instance.GetCarton(code);
		//}

		//public void Save(ICarton carton)
		//{
		//	base.Save<ICarton>(carton);
		//}

		#endregion carton

		#region Service Properties

		private IJobApplication JobApplication;
		private IConfigurationApplication ConfigurationApplication;

		#endregion Service Properties

		public string PadStockName { get; set; }
	}
}
*/
