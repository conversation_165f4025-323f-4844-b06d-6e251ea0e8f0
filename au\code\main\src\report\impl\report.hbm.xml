<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2"
				   namespace="lep.report.impl"
				   assembly="lep"
				   auto-import="true"
				   default-cascade="all">

	<sql-query name="ManagementReport_GetData">
		<return-scalar column="Name" type="String" />
		<return-scalar column="Val" type="Int32" />
		exec ManagementReport_GetData
	</sql-query>
</hibernate-mapping>