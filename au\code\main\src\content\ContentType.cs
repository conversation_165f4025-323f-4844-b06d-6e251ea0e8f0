namespace lep.content
{
	public enum ContentType
	{
		FooterNotification = 3,
		BodyNotification = 4,
		JobQuoteRequestSubmit = 5,
		JobQuoteRequestAttention = 6,
		OrderSubmit = 7,
		JobReject = 8,
		JobArtWorkApproval = 9,
		OrderPrePayment = 10,
		JobGonePlate = 11,
		JobGoneFinish = 12,

		JobAwaitingCourier = 13,
		JobPaymentAwaitingCourier = 14,

		CustomerOnHold = 15,
		CustomerDetailChange = 16,

		//cr18
		OnlinePaymentSuccessful = 18,

		OnlinePaymentFail = 19,

		//cr21
		Reprint = 21,

		Restart = 22,
		PasswordReset = 24,

		CourierDispatch = 25,

		WebOrderRaised = 28,
        WebOrderRaisedWithPaypalPayment = 29,
		WebOrderRaisedWithStripePayment = 30,
		WebOrderRaisedWithAccountPayment = 31,
		PayMeOrder = 32,


		OrderReadyForPickup = 33,

		ReprintOrder = 34,


		UnableToMeetPrice = 35,
		RejectionOfVariationRequest = 36,
	}
}
