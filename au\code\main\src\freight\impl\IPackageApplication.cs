using lep.job;
using lep.order;
using System.Collections.Generic;

namespace lep.freight
{

	public class SingleJobDetails
	{
		public decimal w { get; set; }
		public decimal h { get; set; }
		public decimal depthOf1Job { get; set; }
		public decimal weight { get; set; }
		public string finishedSize { get; set; }
	}



	public interface IPackageApplication
	{
		bool LORD1254 { get; set; }

		IList<Package> SetPackage(IJob job);

		void SetPackage(IOrder order);

		IList<Package> PackItems(IList<Package> items, bool allowSkidDelivery);

		IList<Package> GetPackages(IJob job, int jobQuantity, BrochureDistPackInfo brochureDistPackInfo);

		IList<Package> GetPackagesConsolidated(IJob job, int jobQuantity, bool allowSkidDelivery, BrochureDistPackInfo brochureDistPackInfo);

		// remove the following 2 once the new freight is fully deployed
		bool AllowSkidDelivery(IOrder order);

		IList<Package> GetPackage(IOrder order, Facility? facility);

		bool IsSkidPossible(IOrder order);
		int GetInBundlesOf(IJob j, SingleJobDetails jd, BrochureDistPackInfo brochureDistPackInfo);
		SingleJobDetails GetSingleJobThicknessWeight(IJob j);

		void GetSingleJobThicknessWeight2(IJobTemplate template, ISize finishedSize1, ISize foldedSize1, IStock stock, int pagesPerJob, IStock stockForCover, bool magnet, out decimal w, out decimal h, out decimal thickness, out decimal weight, out string finishedSize);
		void GetSingleJobThicknessWeight2(IJob j, out decimal w, out decimal h, out decimal thickness, out decimal weight, out string finishedSize);


	}
}
