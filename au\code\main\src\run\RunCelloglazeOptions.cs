namespace lep.run
{
	public enum RunCelloglazeOptions
    {
        None,
        [Description("M-f")] <PERSON><PERSON><PERSON><PERSON>,
        [Description("M-f/b")] <PERSON><PERSON><PERSON>,
        [Description("G-f")] <PERSON><PERSON>Front,
        [Description("G-f/b")] <PERSON><PERSON><PERSON>oth,
        [Description("V-f")] VelvetF<PERSON>t,
        [Description("V-f/b")] Velvet<PERSON>oth,

        [Description("EMBOSS G-f")]
        EmbossGlossFront,

        [Description("EMBOSS M-f")]
        EmbossMattFront,

        [Description("FOIL M-f/b")]
        FoilFrontMattBoth,

        [Description("SpotUV M-f/b")]
        SpotUVFrontMattBoth,

		[Description("SpotUV M-f")]
		SpotUVFrontMattFront,

		[Description("G-f/M-b")]
		GlossFrontMattBack,


		[Description("M-AntiScuff-f")] MattAntiScuffFront,
		[Description("M-AntiScuff-f/b")] Matt<PERSON>ntiScuffBoth,

	}
}
