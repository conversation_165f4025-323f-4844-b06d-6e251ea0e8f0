using Newtonsoft.Json;
using System.CodeDom.Compiler;
using System.Diagnostics;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;

namespace lep.freight
{
	[DebuggerDisplay("{Carton}")]
	[Serializable]
	public class Package : ICloneable, IEquatable<Package>, IDisposable
	{
		public string CartonCode { get; set; }
		public int Level { get; set; }
		public decimal Weight { get; set; }
		public int Width { get; set; }
		public int Height { get; set; }
		public int Depth { get; set; }

		public int JobQty { get; set; }
		public int JobId { get; set; }

		public bool StopPackingFurther { get; set; }

		[JsonIgnore]
		public string Carton
		{
			get {
				bool hasItems = Items != null && Items.Any();
				var s = $"{CartonCode,-10} ";
				if (JobQty > 0)
					s += $" @ {JobQty} each, ";
				if (Weight > 0)
					s += $" {Weight: 0.###}kg ";
				if (JobId > 0)
					s += $" J{JobId} ";
				if (hasItems && CartonCode != "")
					s += "...";

				return s;
			}
		}

		public List<Package> Items { get; set; }

		public Package()
		{
		}

		[JsonIgnore]
		public long Dimention => Width * Height * Depth;

		public Package(JObject data)
		{
			CartonCode = data.GetValue("CartonCode").Value<string>();
			Weight = data.GetValue("Weight").Value<decimal>();
			Width = data.GetValue("Width").Value<int>();
			Height = data.GetValue("Height").Value<int>();
			Depth = data.GetValue("Depth").Value<int>();
			Level = data.GetValue("Level").Value<int>();

			if (data["Items"] != null)
			{
				Items = new List<Package>();

				foreach (JObject item in (JArray)data["Items"])
				{
					Items.Add(new Package(item));
				}
			}

			if (data["JobQty"] != null)
			{
				JobQty = data.GetValue("JobQty").Value<int>();
			}

			if (data["JobId"] != null)
			{
				JobId = data.GetValue("JobId").Value<int>();
			}
		}

		public JObject ToJson()
		{
			JObject obj = new JObject();

			obj.Add("CartonCode", CartonCode);
			obj.Add("Weight", Weight);
			obj.Add("Width", Width);
			obj.Add("Height", Height);
			obj.Add("Depth", Depth);
			obj.Add("Level", Level);

			if (Items != null && Items.Count > 0)
			{
				var itemArray = new JArray();
				foreach (var i in Items)
				{
					itemArray.Add(i.ToJson());
				}
				obj.Add("Items", itemArray);
			}

			if (JobQty > 0)
			{
				obj.Add("JobQty", JobQty);
			}

			if (JobId > 0)
			{
				obj.Add("JobId", JobId);
			}

			return obj;
		}

		public object Clone()
		{
			return new Package
			{
				Level = Level,
				CartonCode = CartonCode,
				Weight = Weight,
				Width = Width,
				Height = Height,
				Depth = Depth,
				JobQty = JobQty,
				JobId = JobId,
				Items = Items != null ? Items.Select(t => (Package)t.Clone()).ToList() : null
			};
		}

		public override bool Equals(object obj)
		{
			if (obj is Package other)
			{
				var r =
					CartonCode == other.CartonCode &&
					Level == other.Level &&
					Weight == other.Weight &&
					Width == other.Width &&
					Height == other.Height &&
					Depth == other.Depth &&
					JobQty == other.JobQty &&
					JobId == other.JobId;

				if (!r)
				{
					return false;
				}

				if (this.Items != null && other.Items != null)
				{
					var r2 = this.Items.SequenceEqual(other.Items);
					if (!r2)
					{
						return false;
					}
				}
				if (this.Items == null && other.Items == null)
				{
					return true;
				}
				else
				{
					return false;
				}
			}
			return false;
		}

		bool IEquatable<Package>.Equals(Package other)
		{
			return this.Equals(other);
		}

		public void Dispose()
		{
			this.Items = null;
		}
	}

	//(new PackagePrinter(x)).Result.Dump();
	public class PackagePrinter
	{
		private Dictionary<string, int> cartonCount = new Dictionary<string, int>(50); // https://www.py4u.net/discuss/718546
		private System.IO.StringWriter baseTextWriter;
		private System.IO.StringWriter baseTextWriter2;

		private IndentedTextWriter indentWriter;
		private IndentedTextWriter indentWriter2;

		private int l = 0;

		public PackagePrinter(IList<Package> items)
		{
			baseTextWriter = new System.IO.StringWriter();
			baseTextWriter2 = new System.IO.StringWriter();

			indentWriter = new IndentedTextWriter(baseTextWriter, "    ");
			indentWriter2 = new IndentedTextWriter(baseTextWriter2, "    ");

			Print(new List<Package>() { new Package() { Items = items.ToList() } });
			Print2(items);
			Count(items);
		}

		private void Count(IList<Package> items)
		{
			foreach (var i in items)
			{
				if (i.CartonCode == null) continue;
				string str = "";
				try
				{
					str = i.CartonCode;
					if (cartonCount.ContainsKey(str))
						cartonCount[str]++;
					else
						cartonCount.Add(str, 1);

					if (i.Items != null)
						Count(i.Items);
				}
				catch (Exception ex)
				{
					var m = ex.Message;
				}
			}
		}

		private void Print(IList<Package> items)
		{
			foreach (var i in items.OrderBy(_ => _.JobId))
			{
				if (i.CartonCode == null)
					indentWriter.WriteLine("");
				else
					indentWriter.WriteLine(i.Carton);

				if (i.Items != null && i.Items.Count > 0)
				{
					l++;
					++indentWriter.Indent;

					var g = i.Items.GroupBy(_ => _.CartonCode + _.JobId.ToString() + _.JobQty.ToString());

					if (g.Count() < i.Items.Count && g.All(x => x.All(y => y.Items == null)))
					{
						g.ForEach(_ =>
						{
							var f = _.First();
							var s = $"{_.Count(),2} {f.CartonCode,-10} ";
							if (f.JobQty > 0)
								s += $"@ {f.JobQty} each,";
							if (f.Weight > 0)
								s += $" {f.Weight: 0.###}Kg , J{f.JobId}";
							indentWriter.WriteLine(s);
						});
					}
					else
					{
						Print(i.Items);
					}
					--indentWriter.Indent;
				}
			}
		}

		private void Print2(IList<Package> items)
		{
			foreach (var i in items)
			{
				//if (i.Level == 1)
				//	continue;
				indentWriter2.WriteLine(i.Carton);

				if (i.Items != null && i.Items.Count > 0)
				{
					l++;
					++indentWriter2.Indent;

					var g = i.Items.GroupBy(_ => _.CartonCode + _.JobId.ToString() + _.JobQty.ToString());

					if (g.Count() < i.Items.Count && g.All(x => x.All(y => y.Items == null)))
					{
						g.ForEach(_ =>
						{
							var f = _.First();
							var s = $"{_.Count(),2} {f.CartonCode,-10} ";
							if (f.JobQty > 0)
								s += $"@ {f.JobQty} each,";
							if (f.Weight > 0)
								s += $" {f.Weight: 0.###}Kg , J{f.JobId}";
							indentWriter2.WriteLine(s);
						});
					}
					else
					{
						Print2(i.Items);
					}
					--indentWriter2.Indent;
				}
			}
		}

		public string Result()
		{
			var s = "";

			foreach (var kvp in cartonCount)
				s += $" {kvp.Value} {kvp.Key}.";

			if (s != "")
				s = $"\nTotal: {s}\n";

			return baseTextWriter.ToString() + s;
		}

		public string Result2()
		{
			var s = "";

			foreach (var kvp in cartonCount)
				s += $" {kvp.Value} {kvp.Key}.";

			if (s != "")
				s = "\nTotal: " + s;

			return baseTextWriter2.ToString() + s;
		}


		public string Result4()
		{
			var s = "";

			foreach (var kvp in cartonCount)
				s += $" {kvp.Value} {kvp.Key}.";

			if (s != "")
				s = "Total: " + s;

			return baseTextWriter2.ToString() + s;
		}


		public string Result3()
		{
			var s = "";

			foreach (var kvp in cartonCount)
				s += $" {kvp.Value} {kvp.Key}.";

			if (s != "")
			{
				s = s.Split(new[] { '.' }).FirstOrDefault();
				if(!String.IsNullOrEmpty(s))
					s = "TLP: " + s;
			}
				

			return s;
		}
	}


	[Serializable]
	public class ListOfPackages : List<Package>
	{
		public ListOfPackages() : base(new List<Package>())
		{
		}
		public ListOfPackages(List<Package> lp) : base(lp)
		{
		}

		public decimal TotalWeight()
		{
			var w = 0m;
			w = this.Sum(_ => _.Weight);
			return w;
		}

		public override string ToString()
		{
			var y = (new PackagePrinter(this)).Result();
			return y;
		}

		public string ToString2()
		{
			var y = (new PackagePrinter(this)).Result2();
			return y;
		}
		public string ToString3()
		{
			var y = (new PackagePrinter(this)).Result3();
			return y;
		}

		public IEnumerable<Package> Traverse()
		{
			var stack = new Stack<Package>();

			foreach (var i in this)
				stack.Push(i);

			while (stack.Any())
			{
				var next = stack.Pop();
				yield return next;
				if (next.Items != null)
					foreach (var child in next.Items)
						stack.Push(child);
			}
		}

		public override bool Equals(object obj)
		{
			var t = obj.GetType();
			if (obj is ListOfPackages other)
			{
				return this.SequenceEqual(other);
			}

			return false;
		}
	}
}
