<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" 
				   namespace="lep.quote"
				   assembly="lep"
				   auto-import="true"
				   default-cascade="all">

	<class name="IQuote" table="Quote" discriminator-value="null">
		<cache usage="read-write"/>
		<id name="Id" type="Int32" unsaved-value="0">
			<generator class="identity" />
		</id>
		<discriminator column="Id" type="Int32" insert="false"/>
		<timestamp name="DateModified" column="DateModified" />
		<property name="Status" type="lep.quote.impl.QuoteStatusOptionsEnum, lep" not-null="true" />

     

        <many-to-one name="Customer" class="lep.user.impl.CustomerUser, lep" column="CustomerId" not-null="true" cascade="none" />
		
		<property name="Description" type="StringClob" not-null="true"/>
		<property name="Quotation" type="StringClob" />

        <property name="ValidUntil"  type="lumen.hibernate.type.DateTimeType, lumen" />
 		
		<property name="DateCreated" column="DateCreated" type="lumen.hibernate.type.DateTimeType, lumen" not-null="false" update="false" insert="false"/>
		<subclass name="lep.quote.impl.Quote, lep" proxy="IQuote" discriminator-value="not null"/>
	</class>
	
</hibernate-mapping>