﻿using Serilog;
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Net.Http;
using System.Reflection;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace lep.src.onlineTxn.impl.Westpac
{
	public class LEPWestpac
	{
		#region Readonly

		// private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		#endregion Readonly

		private static readonly Dictionary<string, string> responceCodes = new()
		{
			{"00", "Approved or completed successfully"},
			{"01", "Refer to card issuer"},
			{"03", "Invalid merchant"},
			{"04", "Pick-up card"},
			{"05", "Do not honour"},
			{"08", "Honour with identification"},
			{"12", "Invalid transaction"},
			{"13", "Invalid amount"},
			{"14", "Invalid card number(no such number)"},
			{"30", "Format error"},
			{"36", "Restricted card"},
			{"41", "Lost card"},
			{"42", "No universal account"},
			{"43", "Stolen card, pick up"},
			{"51", "Not sufficient funds"},
			{"54", "Expired card"},
			{"61", "Exceeds withdrawal amount limits"},
			{"62", "Restricted card"},
			{"65", "Exceeds withdrawal frequency limit"},
			{"91", "Issuer or switch is inoperative"},
			{"92", "Financial institution or intermediate network facility cannot be found for routin"},
			{"94", "Duplicate transmission"},
			{"Q2", "Transaction Pending"},
			{"Q3", "Payment Gateway Connection Error"},
			{"Q4", "Payment Gateway Unavailable"},
			{"QD", "Invalid Payment Amount - Payment amount less than minimum/exceeds maximum allowed limit"},
			{"QE", "Internal Error"},
			{"QI", "Transaction incomplete - contact Westpac to confirm reconciliation"},
			{"QQ", "Invalid Credit Card or Invalid Credit Card Verification Number"},
			{"QX", "Network Error has occurred"},
			{"QY", "Card Type Not Accepted"},
			{"QZ", "Zero value transaction"}
		};

		private static readonly Dictionary<string, string> summaryCodes = new ()
		{
			{"0", "Transaction Approved"},
			{"1", "Transaction Declined"},
			{"2", "Transaction Erred"},
			{"3", "Transaction Rejected"}
		};

		//todo: customise these message's to read better
		private static readonly Dictionary<string, string> descriptions = new ()
		{
			{
				"01",
				"Refer to Issuer<br/> This indicates an error or problem on the issuer's side. The problem may be related to the card holder's account. In general the reason for this response code may be any of the following:-<br/> • Suspected Fraud<br/> • Insufficient Funds<br/> • Stolen Card<br/> • Expired Card<br/> • Invalid CVN<br/> • Any other rule imposed by the card issuer that causes a decline (e.g. daily limit exceeded, duplicate transaction suspected, etc)."
			},
			{
				"03",
				"This can be returned when there is a problem with the merchant configuration. This can also be returned for AMEX transactions when there is a problem with the setup at American Express. This code can be returned from an issuing bank if they don't like the acquiring bank. An example of this would be someone trying to pay their speeding fine with an overseas credit card. The overseas issuing bank would return a 03, indicating that they wouldn't allow the transaction over the internet for an Australian bank."
			},
			{
				"04",
				"Pickup Card Error code 04 normally means that the card has been reported as lost or stolen. In all cases where this response code is being returned and the customer does not know why they need to follow this up with the issuing bank."
			},
			{
				"05",
				"This code is usually returned from Westpac for Westpac issued cards for similar reasons that other issuers return 01. It can indicate any of the following:- Suspected Fraud • Insufficient Funds<br/> • Stolen Card<br/> • Expired Card<br/> • Invalid CVN<br/> • Any other rule imposed by the card issuer that causes a decline (e.g. daily limit exceeded, duplicate transaction suspected, etc)."
			},
			{
				"12",
				"This code is often returned from the issuer when they do not accept the transaction. This can possibly be when a transaction for the same amount and merchant is attempted multiple times quickly for the same card. The best approach is for the card holder to contact their issuing bank."
			},
			{
				"14",
				"This code indicates that the card number either did not pass the check digit algorithm, or is not an account that exists at the issuing bank. Westpac returns this code if the card number passes the check digit algorithm, but is not an existing card. Westpac also returns this code if an AMEX card is used, but the merchant is not setup for AMEX cards at the Westpac end."
			},
			{
				"22",
				"Westpac returns this code if the card number does not pass the check digit algorithm. This is considered a malfunction, since Westpac expect the terminal to check the card number before transmission."
			},
			{
				"42",
				"This error is returned from some issuers when the credit account does not exist at the issuing bank. This situation is similar to the 14 response code - the card number passes the check digit algorithm, but there is no credit account associated with the card number. This error is also returned if you are using the TEST merchant without using one of the test card numbers."
			},
			{"51", "Not sufficient funds"},
			{
				"61",
				"Exceeds withdrawal amount limits - This error is returned when the card holder does not have enough credit to pay the specified amount. Ask the card holder if they have another card to use for the payment."
			},
			{
				"54",
				"Expired Card This error is returned when the wrong expiry date has been entered for the credit card. Check that the expiry date is correct and attempt the transaction again. If the transaction still does not work, check with the card holder to see if they have a new card with a new expiry date. "
			},
			{
				"91",
				"This code is used to indicate that the next party in a credit card transaction timed out and the transaction has been reversed. This may happen between PayWay and Westpac, or further down the chain."
			},
			{
				"92",
				"Financial institution or intermediate network facility cannot be found for routing<br/> The card number is incorrect.The first 6 digits of the credit card number indicate which bank issued the card.These are used for routing credit card requests through the credit card network to the issuing bank.This error indicates that there is no bank that corresponds to the first 6 digits of the card number."
			},
			{
				"QI",
				"Transaction incomplete - This response code indicates that a request message was sent to the PayWay server but no response was received within the timeout period."
			},
			{
				"QQ",
				"Invalid Card - This error code indicates that the credit card details(card number, expiry date or CVN) are invalid. This could be because the card number does not meet check digit validation, an invalid expiry date was entered or an invalid CVN was entered."
			},
			{
				"QY",
				"Card Type not accepted The Merchant is not enabled for the particular Card Scheme(normally returned for American Express and Diners Club cards).To register for American Express or Diners Club, click the Register to accept Amex or Diners through PayWay link on the “Merchants” page.Alternatively, you may have entered a bad card number with too many or too few digits."
			}
		};

		private static string LookupWithDefault(string s, Dictionary<string, string> dic, string def)
		{
			var result = "";
			if (!dic.TryGetValue(s, out result))
			{
				return def;
			}
			return result;
		}

		public static string getResponceFromCode(string c)
		{
			return LookupWithDefault(c, responceCodes, "Can not determine");
		}

		public static string getSummaryFromCode(string c)
		{
			return LookupWithDefault(c, summaryCodes, "Can not determine");
		}

		public static string getDescription(string c)
		{
			return LookupWithDefault(c, descriptions, @"You should check that the card details are correct.
                        If they are, use an alternative credit card. If this still does not resolve the problem,
                        the card holder should contact their issuing bank. If you receive a response code starting
                        with ‘Q’ that you do not understand, you should contact Technical Support at your Bank and or LEP");
		}

		public static IDictionary DecryptParameters(string base64Key, string encryptedParametersText,
			string signatureText)
		{
			if (string.IsNullOrEmpty(encryptedParametersText) || string.IsNullOrEmpty(base64Key) ||
				string.IsNullOrEmpty(signatureText))
			{
				return null;
			}


			byte[] key;
			try
			{
				key = Convert.FromBase64String(base64Key);
			} catch(Exception ex)
			{
				base64Key = base64Key.Replace(" ", "+");
				key = Convert.FromBase64String(base64Key);
			}

			byte[] array;

			 
				encryptedParametersText = encryptedParametersText.Replace(" ", "+");
				array = Convert.FromBase64String(encryptedParametersText);
		 
			

			var aes = Aes.Create();
			aes.KeySize = 128;
			aes.BlockSize = 128;
			aes.Mode = CipherMode.CBC;
			aes.Padding = PaddingMode.PKCS7;
			aes.IV = new byte[16];
			aes.Key = key;
			var cryptoTransform = aes.CreateDecryptor();
			var array2 = cryptoTransform.TransformFinalBlock(array, 0, array.Length);
			var @string = Encoding.ASCII.GetString(array2);
			var md5 = MD5.Create();
			var array3 = md5.ComputeHash(array2);



			byte[] array4;
			   signatureText = signatureText.Replace(" ", "+");
				array4 = Convert.FromBase64String(signatureText);
	 

			var array5 = cryptoTransform.TransformFinalBlock(array4, 0, array4.Length);
			if (array5.Length != array3.Length)
			{
				throw new CryptographicException("Invalid parameters signature");
			}
			for (var i = 0; i < array5.Length; i++)
			{
				if (array3[i] != array5[i])
				{
					throw new CryptographicException("Invalid parameters signature");
				}
			}
			var hashtable = new Hashtable();
			var array6 = @string.Split(new char[] { '&' });
			var array7 = array6;
			for (var j = 0; j < array7.Length; j++)
			{
				var text = array7[j];
				var array8 = text.Split(new char[] { '=' });
				hashtable.Add(HttpUtility.UrlDecode(array8[0]), HttpUtility.UrlDecode(array8[1]));
			}
			return hashtable;
		}

		public static class Constants
		{
			public static readonly string EncryptedParameters = "EncryptedParameters";
			public static readonly string Signature = "Signature";
			public static readonly string PaymentApproved = "approved";
			public static readonly string PaymentDeclined = "declined";
		}
	}

	public class TokenUtil
	{
		private bool isLocked = false;
		private ArrayList myErrors = new ArrayList();

		private Dictionary<string, Field> myFields = new Dictionary<string, Field>();
		private List<string> myHiddenFieldNames = new List<string>();
		private List<string> myInformationFieldNames = new List<string>();
		private ArrayList myLog = new ArrayList();
		private string myPayWayUrl;
		private string myProxyName;
		private string myProxyPassword;
		private int myProxyPort;
		private string myProxyUsername;
		private List<string> mySuppressedFieldNames = new List<string>();
		private string myToken = null;

		public void Init(string username, string password, string billerCode, string merchantId, string payWayUrl,
			string paymentReference, string proxyName, int proxyPort, string proxyUsername, string proxyPassword)
		{
			AddConfigurationParameter("username", username);
			AddConfigurationParameter("password", password);
			AddConfigurationParameter("biller_code", billerCode);
			AddConfigurationParameter("merchant_id", merchantId);
			AddConfigurationParameter("payment_reference", paymentReference);
			myPayWayUrl = payWayUrl;
			if (!myPayWayUrl.EndsWith("/"))
			{
				myPayWayUrl += "/";
			}
			myProxyName = proxyName;
			myProxyPort = proxyPort;
			myProxyUsername = username;
			myProxyPassword = proxyPassword;
		}

		public void AddInformationField(string fieldName, string fieldValue, bool suppressFieldName)
		{
			AddField(fieldName, new SingleValueField(fieldName, fieldValue));
			myInformationFieldNames.Add(fieldName);
			if (suppressFieldName)
			{
				mySuppressedFieldNames.Add(fieldName);
			}
		}

		public void AddHiddenField(string fieldName, string fieldValue)
		{
			AddField(fieldName, new SingleValueField(fieldName, fieldValue));
			myHiddenFieldNames.Add(fieldName);
		}

		public void AddProductField(string name, int price)
		{
			AddProductField(name, price, 1.0);
		}

		public void AddProductField(string productName, int unitPrice, double quantity)
		{
			AddField(productName, new ProductField(productName, unitPrice, quantity));
		}

		public void AddConfigurationParameter(string parameterName, string parameterValue)
		{
			AddField(parameterName, new SingleValueField(parameterName, parameterValue));
		}

		private void AddField(string parameterName, Field field)
		{
			var hashtable = new Hashtable();
			if (myFields.ContainsKey(parameterName))
			{
				myErrors.Add(string.Concat(new string[]
				{
					"Parameter name '",
					parameterName,
					"' has already been added. Overwriting original value of '",
					myFields[parameterName].Value,
					"'"
				}));
				myFields.Remove(parameterName);
			}
			myFields.Add(parameterName, field);
		}

		public ArrayList GetErrors()
		{
			return myErrors;
		}

		public string GetToken()
		{
			if (!isLocked)
			{
				isLocked = true;

				System.Net.ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;
				ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;

				var response = GetResponseAsync(myPayWayUrl + "RequestToken", getParametersString()).Result;

				if (response.Contains("error="))
				{
					throw new Exception(response);
				}

				log("Got Response: " + response);
				var array = response.Split(new char[] { '&' });
				for (var i = 0; i < array.Length; i++)
				{
					var text = array[i];
					var array2 = text.Split(new char[] { '=' }, 2);
					if ("token".Equals(array2[0]))
					{
						log("Got token: " + array2[1]);
						myToken = array2[1];
					}
					else if ("error".Equals(array2[0]))
					{
						log("Got error: " + array2[1]);
						myErrors.Add(array2[1]);
					}
				}
			}
			log("Returning token");
			return myToken;
		}

		private async Task<string> GetResponseAsync(string serverUrl, byte[] requestParameters)
		{
			log("Fetching token");

			HttpClient httpClient;
			if (myProxyName != null)
			{
				log(string.Concat(new object[]
				{
					"Using proxy: ",
					myProxyName,
					":",
					myProxyPort
				}));
				var proxy = new WebProxy(myProxyName + ":" + myProxyPort);
				if (myProxyUsername != null)
				{
					proxy.Credentials = new NetworkCredential(myProxyUsername, myProxyPassword);
				}
				var handler = new HttpClientHandler()
				{
					Proxy = proxy,
					UseProxy = true
				};
				httpClient = new HttpClient(handler);
			}
			else
			{
				httpClient = new HttpClient();
			}

			using (httpClient)
			{
				httpClient.Timeout = TimeSpan.FromMilliseconds(60000);

				var content = new ByteArrayContent(requestParameters);
				content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/x-www-form-urlencoded")
				{
					CharSet = Encoding.UTF8.WebName
				};

				try
				{
					var response = await httpClient.PostAsync(serverUrl, content);
					log("Got a response");
					var result = await response.Content.ReadAsStringAsync();
					return result;
				}
				catch (HttpRequestException ex)
				{
					handleException(ex);
					return string.Empty;
				}
			}
		}

		private byte[] getParametersString()
		{
			if (myInformationFieldNames != null && myInformationFieldNames.Count > 0)
			{
				AddField("information_fields",
					new SingleValueField("information_fields", string.Join(",", myInformationFieldNames.ToArray())));
			}
			if (myHiddenFieldNames != null && myHiddenFieldNames.Count > 0)
			{
				AddField("hidden_fields",
					new SingleValueField("hidden_fields", string.Join(",", myHiddenFieldNames.ToArray())));
			}
			if (mySuppressedFieldNames != null && mySuppressedFieldNames.Count > 0)
			{
				AddField("suppress_field_names",
					new SingleValueField("suppress_field_names", string.Join(",", mySuppressedFieldNames.ToArray())));
			}
			log(JoinFields("&", myFields));
			return Encoding.UTF8.GetBytes(JoinFields("&", myFields));
		}

		private void handleException(Exception e)
		{
			try
			{
				throw e;
			}
			finally
			{
				log(e.Message);
				myErrors.Add(e.Message);
			}
		}

		private string JoinFields(string delimiter, Dictionary<string, Field> fields)
		{
			StringBuilder stringBuilder = null;
			foreach (var current in fields)
			{
				if (stringBuilder == null)
				{
					stringBuilder = new StringBuilder();
				}
				else
				{
					stringBuilder.Append(delimiter);
				}
				stringBuilder.Append(current.Value.ToString());
			}
			return stringBuilder.ToString();
		}

		private void log(string entry)
		{
			myLog.Add(entry);
		}

		public ArrayList GetLog()
		{
			return myLog.Clone() as ArrayList;
		}

		internal class SingleValueField : Field
		{
			private string myFieldValue;

			public SingleValueField(string fieldName, string fieldValue) : base(fieldName)
			{
				myFieldValue = fieldValue;
			}

			protected override string GetValue()
			{
				return myFieldValue;
			}
		}

		internal class ProductField : Field
		{
			private decimal myQuantity;
			private decimal myUnitPrice;

			public ProductField(string productName, int unitPrice, double quantity) : base(productName)
			{
				myUnitPrice = decimal.Divide(unitPrice, 100m);
				myQuantity = (decimal)quantity;
			}

			protected override string GetValue()
			{
				string result;
				if (myQuantity > 1m)
				{
					result = myQuantity + "," + myUnitPrice;
				}
				else
				{
					result = myUnitPrice.ToString();
				}
				return result;
			}
		}

		internal abstract class Field
		{
			protected string myFieldName;

			public Field(string fieldName)
			{
				myFieldName = HttpUtility.UrlEncode(fieldName);
			}

			public string Name
			{
				get { return myFieldName; }
			}

			public string Value
			{
				get { return HttpUtility.UrlEncode(GetValue()); }
			}

			protected abstract string GetValue();

			public override string ToString()
			{
				return Name + "=" + Value;
			}
		}
	}
}
