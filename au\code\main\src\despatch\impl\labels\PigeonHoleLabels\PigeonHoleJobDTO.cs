namespace lep.despatch.impl.label
{
    /// <summary>
    /// Job Data Transfer struct  for Pigeon hole lables
    /// The ordering of the properties are important and not to be altered
    /// </summary>
    public sealed class PigeonHoleJobDTO
    {
        public string JobNumber { get; set; }
        public string JobName { get; set; }
        public string JobDetails { get; set; }
        public string Status { get; set; }
        public string Pkgs { get; set; }
        public string RequiredBy { get; set; }
    }
}