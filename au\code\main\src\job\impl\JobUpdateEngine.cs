//using System;
//using System.Reflection;
//using Serilog;
//using lep.barcode;
//using NHibernate;
//using NHibernate.Criterion;
//using lep.user;

//namespace lep.job.impl
//{
//	public class JobUpdateEngine : IInitializingObject
//    {
//        // private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);
//        private IBarcodeService barcodeApplication;

//        private bool initialised;
//        private IJobApplication jobApplication;

//        private ISession session;

//        public JobUpdateEngine (ISession session,  IBarcodeService barcodeApplication)
//        {
//            this.session = session;
//            this.barcodeApplication = barcodeApplication;
//            AfterPropertiesSet();
//        }


//        public IJobApplication JobApplication
//        {
//            set { jobApplication = value; }
//        }

//        public IBarcodeService BarcodeApplication
//        {
//            set { barcodeApplication = value; }
//        }

//        public void AfterPropertiesSet()
//        {
//            initialised = true;
//		}

//		public void CronTask()
//        {
//            if (!initialised)
//            {
//                throw new ApplicationException("JobUpdateEngine not initialised");
//            }
//            return;
//            //avoid nest transaction
//            // var ids = jobApplication.GetDispatchedJobId();

//            var ids =  session.CreateCriteria(typeof(JobDespatchUpdate))
//                .Add(Restrictions.IsNull("Response"))
//                .SetProjection(Projections.Property("JobId"))
//                .List<int>();

//			IUser user = session.Get<IUser>(1);

//            foreach (var id in ids)
//            {
//                try
//                {
//                    var message = String.Empty;

//                    if (barcodeApplication.ScanBarcode("Dispatched", "J" + id, DateTime.Now, out message, user))
//                    {
//                        message = "Completed";
//                    }
//                    if (message.Length > 255)
//                    {
//                        message = message.Substring(0, 255);
//                    }

//                    //var update = jobApplication.GetJobDespatchUpdate(id);

//                    var update = session.Get<JobDespatchUpdate>(id);
//                    update.Response = message;

//                    session.SaveOrUpdate(update);
//                }
//                catch (Exception ex)
//                {
//                    Log.Error(ex.Message);
//                }
//            }
//        }
//    }
//}
