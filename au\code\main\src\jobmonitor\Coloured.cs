﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

using lep;
using lep.job;
using lep.order;
using lep.run;
using System.Drawing;

namespace lep.jobmonitor
{

	/// <summary>
	/// Decorates a Job by measuing if at any stage if the job has surpassed standard production timing and then assigns a color.
	/// </summary>
 
	public class Coloured<T>  
	{
		public T instance;
		Color Colour { get; set; }
		public Coloured ( T t )
		{
			instance = t;

			if (typeof( T ) == typeof( IJob )) {
				Colour = "green";
			}

			if (typeof( T ) == typeof( IRun )) {
				// see all jobs color and set color
				Colour = "red";
			}

			if (typeof( T ) == typeof( IOrder )) {
				// see all jobs color and set color
				Colour = "blue";
			}
		}
	}
}
