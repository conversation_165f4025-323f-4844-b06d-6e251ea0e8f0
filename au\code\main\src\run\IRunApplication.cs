using lep.job;
using lep.user;
using NHibernate;
using System.Collections.Generic;
using System.IO;

namespace lep.run
{
	/// <summary>
	/// Run DAO and Run-Engine processing.
	/// </summary>
	public interface IRunApplication
    {
        IRun NewRun(IJob job);

        ILayout NewLayout(LayoutType Type, int pageNo, string FileName);

        IRun SaveLayout(IRun run, ILayout layout, Stream input, string extension, IStaff prepressBy);

        IList<IRun> FindRunsForJob(IJob job);

        ICriteria FindCurrentRunsCriteria(Facility facility, List<RunStatusOptions> status, string runNr,
            string orderNr, string jobNr, IStock stock,
            RunSearchOptions searchOption, bool isOnHold, RunCelloglazeOptions? runCello = null);

        IList<IRun> FindCurrentRuns(Facility facility, List<RunStatusOptions> status, string runNr, string orderNr, string jobNr, IStock stock, RunSearchOptions searchOption, bool isOnHold);

        IList<IRun> FindOldRun();

        IList<IRun> FindFileCopyRun();

        void RemoveRunFile(IRun run);

        void CopyRunFile(IRun run);

        IRun GetRun(int Id);

        void Save(IRun run);

        void Delete(IRun run);

        string GenerateRunScript(IRun run);

        void SetLayout(IRun run, RunStatusOptions status);

        void CronTask_RunFileRemove();

        void CronTask_RunFileCopy();
    }
}
