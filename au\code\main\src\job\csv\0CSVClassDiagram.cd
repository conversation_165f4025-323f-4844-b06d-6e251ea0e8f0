﻿<?xml version="1.0" encoding="utf-8"?>
<ClassDiagram MajorVersion="1" MinorVersion="1">
  <Class Name="lep.job.csv.QuantityOptionVerifySchemaHandler" Collapsed="true">
    <Position X="4.25" Y="2" Width="2.75" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>src\job\csv\QuantityOptionVerifySchemaHandler.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="lep.job.csv.BaseCsvReader" Collapsed="true">
    <Position X="8.5" Y="3.5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAABAAAAgAAAAQEAAAEAAAAAAAAAAAAQCAAAAgIAAAA=</HashCode>
      <FileName>src\job\csv\BaseCsvReader.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="lep.job.csv.BaseVerifySchemaHandler" Collapsed="true">
    <Position X="9.75" Y="0.5" Width="2" />
    <TypeIdentifier>
      <HashCode>QAAAAAAAgAIAAEEAAAQIAAAEAAAIAAAAAAAAAAIAAAA=</HashCode>
      <FileName>src\job\csv\BaseVerifySchemaHandler.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="lep.job.csv.FoldOptionsCsvReader" Collapsed="true">
    <Position X="10" Y="5" Width="2.25" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAEABAAEAAQAAAAAAAAAAAAAACIAAQA=</HashCode>
      <FileName>src\job\csv\FoldOptionsCsvReader.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="lep.job.csv.FoldOptionsImportHandler" Collapsed="true">
    <Position X="10" Y="6" Width="2.75" />
    <TypeIdentifier>
      <HashCode>QAABAAAAgAAAAAEABAAACAQAAAAIAAAAAAAAAgIAAAA=</HashCode>
      <FileName>src\job\csv\FoldOptionsImportHandler.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="lep.job.csv.FoldOptionsVerifyForeignKey" Collapsed="true">
    <Position X="10" Y="7" Width="2.75" />
    <TypeIdentifier>
      <HashCode>QAABAAAAgAAAAAEAAAAAAAAAAAAIAAAAAAAAAgIAAAA=</HashCode>
      <FileName>src\job\csv\FoldOptionsVerifyForeignKey.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="lep.job.csv.FoldOptionsVerifySchemaHandler" Collapsed="true">
    <Position X="11" Y="2" Width="2.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>src\job\csv\FoldOptionsVerifySchemaHandler.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="lep.job.csv.JobOptionCsvReader" Collapsed="true">
    <Position X="14.75" Y="5" Width="1.75" />
    <TypeIdentifier>
      <HashCode>AAAAAAEAAEAAAAEAAAAcAAAAGAEAAAAAAAAACCIAAQk=</HashCode>
      <FileName>src\job\csv\JobOptionCsvReader.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="lep.job.csv.JobOptionImportHandler" Collapsed="true">
    <Position X="14.75" Y="6" Width="2.25" />
    <TypeIdentifier>
      <HashCode>QAABAEGAgEAAAAFABAAYCAAAWAAIAAAAAAAQCgAAQAk=</HashCode>
      <FileName>src\job\csv\JobOptionImportHandler.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="lep.job.csv.JobOptionSpecStockCsvReader" Collapsed="true">
    <Position X="4" Y="5" Width="2.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAEABAAAAAQAAAAAAAAAAAAAACIAAQA=</HashCode>
      <FileName>src\job\csv\JobOptionSpecStockCsvReader.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="lep.job.csv.JobOptionSpecStockImportHandler" Collapsed="true">
    <Position X="3.75" Y="6" Width="3" />
    <TypeIdentifier>
      <HashCode>QCIBAAAAgAAAAQEABAAACAQAAAAIAAAAAAAAAgIAAAA=</HashCode>
      <FileName>src\job\csv\JobOptionSpecStockImportHandler.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="lep.job.csv.JobOptionSpecStockVerifySchemaHandler" Collapsed="true">
    <Position X="14.25" Y="2" Width="3" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>src\job\csv\JobOptionSpeStockVerifySchemaHandler.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="lep.job.csv.JobOptionVerifyForeignKey" Collapsed="true">
    <Position X="3.75" Y="7" Width="3" />
    <TypeIdentifier>
      <HashCode>QAABAAAAgEAAAAFAAAAYAAAAGAAIAAAAAAAACgIAAAg=</HashCode>
      <FileName>src\job\csv\JobOptionVerifyForeignKey.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="lep.job.csv.JobOptionVerifySchemaHandler" Collapsed="true">
    <Position X="7.75" Y="2" Width="2.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>src\job\csv\JobOptionVerifySchemaHandler.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="lep.job.csv.QuantityOptionCsvReader" Collapsed="true">
    <Position X="7.25" Y="5" Width="2" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAEABAAAAAQAAAAAAAAAAAAAACIAAQA=</HashCode>
      <FileName>src\job\csv\QuantityOptionCsvReader.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="lep.job.csv.QuantityOptionImportHandler" Collapsed="true">
    <Position X="7.25" Y="6" Width="2" />
    <TypeIdentifier>
      <HashCode>QAABAAAAgAAAAAEABAAACAQAAAAIAAAAAAAAAgIAAAA=</HashCode>
      <FileName>src\job\csv\QuantityOptionImportHandler.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Font Name="Segoe UI" Size="9" />
</ClassDiagram>