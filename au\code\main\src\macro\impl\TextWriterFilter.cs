using System;
using System.Globalization;
using System.IO;
using System.Text;

namespace lep.macro.impl
{
	public abstract class TextWriterFilter : TextWriter
    {
        protected TextWriterFilter(TextWriter writer) : base(CultureInfo.InvariantCulture)
        {
            Writer = writer;
        }

        protected TextWriter Writer { get; set; }

        public override Encoding Encoding
        {
            get { return Writer.Encoding; }
        }

        public override IFormatProvider FormatProvider
        {
            get { return Writer.FormatProvider; }
        }

        public override String NewLine
        {
            get { return Writer.NewLine; }
            set { Writer.NewLine = value; }
        }

        public override void Close()
        {
            Writer.Flush();
            Writer.Close();
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                ((IDisposable)Writer).Dispose();
            }
        }

        public override void Flush()
        {
            Writer.Flush();
        }

        public override void Write(char value)
        {
            Writer.Write(value);
        }

        public override void Write(char[] buffer, int index, int count)
        {
            for (var i = 0; i < count; i++)
            {
                Write(buffer[index + i]);
            }
        }

        public override void Write(String value)
        {
            var arr = value.ToCharArray();
            Write(arr, 0, arr.Length);
        }
    }
}