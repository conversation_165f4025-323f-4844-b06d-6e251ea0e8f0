using lep.job;
using lep.user;
using System;
using System.Diagnostics;
using static lep.job.JobCelloglazeOptions;

namespace lep.pricing.impl
{
	[DebuggerDisplay("{Quantity}, {Price} ")]
    public class PricePoint : IPricePoint
    {
        public PricePoint()
        {
        }

		// TODO need unit test for constructor
		public PricePoint(IJobTemplate template, IStock stock, IPaperSize papersize, JobPrintOptions frontPrint,
			JobPrintOptions backPrint, JobCelloglazeOptions frontCello, JobCelloglazeOptions backCello, int quantity,
			decimal price, int numpages, PrintType printType)
        {
            Template = template;
            Stock = stock;
            PaperSize = papersize;
            Price = price;
            Quantity = quantity;
            NumPages = numpages;

            PrintType = printType;

            NumColourSides = template.ColourSide(frontPrint, backPrint);

            if (frontCello == None && backCello == None)
            {
                Celloglazing = "NN";
            }
            else if (frontCello == Gloss && backCello == None)
            {
                Celloglazing = "GN";
            }
            else if (frontCello == Gloss && backCello == Gloss)
            {
                Celloglazing = "GG";
            }
            else if (frontCello == Matt && backCello == None)
            {
                Celloglazing = "MN";
            }
            else if (frontCello == Matt && backCello == Matt)
            {
                Celloglazing = "MM";
            }
            else if (frontCello == Velvet && backCello == None)
            {
                Celloglazing = "VN";
            }
            else if (frontCello == Velvet && backCello == Velvet)
            {
                Celloglazing = "VV";
            }
            else
            {
                throw new ArgumentOutOfRangeException("Illegal Celloglazing combination");
            }
        }

        public PricePoint(IJobTemplate template, IStock stock, IPaperSize papersize, int numColourSides,
            string celloglazing, int quantity, decimal price, int numPages)
        {
            Template = template;
            Stock = stock;
            PaperSize = papersize;
            NumColourSides = numColourSides;
            Celloglazing = celloglazing;
            Price = price;
            Quantity = quantity;
            NumPages = numPages;
        }

		//create a copy constrictor
        public PricePoint(IPricePoint pricePoint)
        {
            Template = pricePoint.Template;
            Stock = pricePoint.Stock;
            PaperSize = pricePoint.PaperSize;
            NumColourSides = pricePoint.NumColourSides;
            Celloglazing = pricePoint.Celloglazing;
            Price = pricePoint.Price;
            Quantity = pricePoint.Quantity;
            NumPages = pricePoint.NumPages;
            PrintType = pricePoint.PrintType;
        }


        #region IPricePoint Members

        public int Id { get; set; }

        public IJobTemplate Template { get; set; }

        public IStock Stock { get; set; }

        public IPaperSize PaperSize { get; set; }

        public int NumPages { get; set; }

        public int NumColourSides { get; set; }

        public string Celloglazing { get; set; }

        public int Quantity { get; set; }

        public decimal Price { get; set; }

        public string MYOB { get; set; }

        public IUser ChangeBy { get; set; }

        public SiteLocation SiteLocation { get; set; }

        public PrintType PrintType { get; set; }

        public DateTime DateCreated { get; set; }

        public DateTime DateModified { get; set; }

        #endregion IPricePoint Members
    }
}
