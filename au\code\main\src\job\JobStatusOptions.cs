namespace lep.job
{
	public enum JobStatusOptions
	{
		[Description("Not Submitted")]
		Open,

		Submitted,

		[Description("Preflight Done")]
		PreflightDone,

		[Description("DPC Pre Production")]
		DPCPreProduction,

		[Description("In Wide Format Production")]
		WideFormatProduction,

		[Description("In Run ")]
		InRun,

		Filling,

		[Description("Layout In Progress")]
		LayoutRequired,

		[Description("Layout Done")]
		LayoutDone,

		[Description("Approved For Plating")]
		ApprovedForPlating,

		[Description("Plating Done")]
		PlatingDone,

		// default  ordering that things happen

		[Description("Press Done")]
		PressDone,


		Celloglazed,
		Cut,
		Scored,
		Perforated,

		[Description("Letterpressed")]
		Letterpressed,

		Folded,
		Stitched,
		Drilled,
		Rounded,
		Finished,


		[Description("DPC Printed")]
		DPCPrinted,

		[Description("DPC Complete")]
		DPCComplete,

		[Description("Wide Format Complete")]
		WideFormatComplete,

		[Description("Shrink Wrapped")]
		ShrinkWrapped,

		Outwork,
		PayMe,
		Packed,

		Dispatched,
		Complete,


		//Unable to meet price
		[Description("Unable to meet price")]
		UnableToMeetPrice,

		//Unable to meet price
		[Description("Rejected Variation")]
		RejectedVariation,
	}
}
