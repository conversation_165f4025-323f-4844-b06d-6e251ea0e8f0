using System.Text.RegularExpressions;

namespace lep.job.csv
{
    public class JobOptionSpecStockVerifySchemaHandler : BaseVerifySchemaHandler
    {
        public JobOptionSpecStockVerifySchemaHandler()
            : base()
        {
            fileType = "Spec Print File";
            headers = new string[]
            {
                "pid", "colour-front", "colour-back", "cello-front", "cello-back"
            };
            regexs = new Regex[]
            {
                new Regex(@"^\d+$"),
                new Regex(@"^(N||FB||F||NFB||NF||R||E||\-)$"),
                new Regex(@"^(N||FB||F||NFB||NF||\-)$"),
                new Regex(@"^(N||G||M||V||E||F||S||E&F||X||EMF||EGF||SMF||W)$"),  //
	            new Regex(@"^(N||G||M||S||V||W)$")
            };
        }
    }
}
