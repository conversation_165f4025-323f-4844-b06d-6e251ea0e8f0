using lep.extensionmethods;
using lep.job;
using lep.job.impl;
using lep.order;
using lep.user;

using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using static lep.job.JobStatusOptions;
using static lep.job.JobTypeOptions;
using static lep.RunStatusOptions;

namespace lep.run.impl
{
	public class Run : IRun
	{
		// private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);
		private JobPrintOptions backPrint;

		private RunCelloglazeOptions celloglaze;
		private bool cmyk;

		private int id;

		private bool isBusinessCard;
		private IList<IJob> jobs = new List<IJob>();

		private IList<ILayout> layoutFiles = new List<ILayout>();
		private bool manuallyManage;

		private IStaff runOperator;
		private IList<IRunSlot> slots = new List<IRunSlot>();
		private RunStatusOptions status;
		private IStock stock;
		private bool urgent;

		public virtual PrintType PrintType { get; set; } = PrintType.O;
		public Run()
		{
			// ApplicationSecurityManager.AssertPermission("run.create");
			ManuallyManage = true;
			Status = RunStatusOptions.Filling;
		}

		public Run(bool isBusinessCard, RunCelloglazeOptions celloglaze, bool cmyk, IStock stock,
			JobPrintOptions backPrint, Facility facility)
		{
			//  ApplicationSecurityManager.AssertPermission("run.create");
			if (stock == null)
			{
				throw new ArgumentNullException("stock");
			}
			this.isBusinessCard = isBusinessCard;
			this.celloglaze = celloglaze;
			this.cmyk = cmyk;
			this.stock = stock;
			this.backPrint = backPrint;
			Facility = facility;
			StartedDate = new DateTime();
			Status = RunStatusOptions.Filling;

			var sb = new StringBuilder("Run created : ");
			sb.Append("Facility=").Append(facility.ToString()).Append("/");
			sb.Append("IsBusinessCard: ").Append(isBusinessCard.ToString()).Append("/");
			sb.Append(stock.Name).Append("/");
			sb.Append("Celloglaze=").Append(celloglaze.ToString()).Append("/");
			sb.Append("CmykBack=").Append(cmyk.ToString()).Append("/");
			sb.Append("Urgent=").Append(urgent.ToString()).Append("/");

			Log.Information(sb.ToString());
		}

		public ILayout GetLayout(LayoutType layouttype, int page)
		{
			foreach (var file in layoutFiles)
			{
				if (file.Type == layouttype && file.PageNo == page)
				{
					return file;
				}
			}
			return null;
		}

		public void LayoutDone(ILayout layout, IStaff prepressBy)
		{
			//ApplicationSecurityManager.AssertPermission("run.update.prepress");
			RemoveLayout(layout);
			layoutFiles.Add(layout);
			Status = RunStatusOptions.LayoutDone;
			runOperator = null;
			foreach (var job in ActiveJobs)
			{
				if (job.Prepress == null)
				{
					job.Prepress = new Prepress();
				}
				job.Prepress.PrepressBy = prepressBy;
				job.Prepress.PrepressDate = DateTime.Now;
			}
			LogStatusChange();
		}

		//set status depend on jobs
		public void SetStatus()
		{
			if (ActiveJobs.Count == 0)
			{
				return;
			}
			var minOption = ActiveJobs[0].Status;
			for (var i = 1; i < ActiveJobs.Count; i++)
			{
				if ((int)minOption > (int)ActiveJobs[i].Status)
				{
					minOption = ActiveJobs[i].Status;
				}
			}
			switch (minOption)
			{
				case Open:
				case Submitted:
				case PreflightDone:
				case InRun:
					if (!new[] { RunStatusOptions.Filling, RunStatusOptions.LayoutRequired, RunStatusOptions.LayoutDone }
							.Contains(status))
					{
						status = RunStatusOptions.Filling;
					}
					break;

				case JobStatusOptions.Filling:
					status = RunStatusOptions.Filling;
					break;

				case JobStatusOptions.LayoutRequired:
					status = RunStatusOptions.LayoutRequired;
					break;

				case JobStatusOptions.LayoutDone:
					status = RunStatusOptions.LayoutDone;
					break;
				//1165099
				case JobStatusOptions.ApprovedForPlating:
					status = RunStatusOptions.ApprovedForPlating;
					break;

				case JobStatusOptions.PlatingDone:
					status = RunStatusOptions.PlatingDone;
					break;

				case JobStatusOptions.PressDone:
					status = RunStatusOptions.PressDone;
					break;

				case JobStatusOptions.Outwork:
					status = RunStatusOptions.Outwork;
					break;


				case JobStatusOptions.DPCPreProduction:
					status = RunStatusOptions.DPCPreProduction;
					break;

				case Cut:
				case Folded:
				case Scored:
				case Drilled:
				case Perforated:

				case Celloglazed:
				case Letterpressed:
				case Stitched:
				case Rounded:
					status = Finishing;
					break;

				case Finished:
					status = FinishingDone;
					break;

				case Packed:
				case Dispatched:
				case Complete:
					status = PackingDone;
					break;
			}
		}

		public void SetStatus(RunStatusOptions s, IUser user)
		{
			//if (Status == s) {	return;	}
			if (ActiveJobs.Count == 0)
			{
				s = RunStatusOptions.Filling;
			}
			switch (s)
			{
				case RunStatusOptions.PressDone:
					//ApplicationSecurityManager.AssertPermission("run.update.press");
					foreach (var job in ActiveJobs)
					{
						if (job.Prepress == null)
						{
							job.Prepress = new Prepress();
						}
						job.Prepress.PrepressDate = DateTime.Now;
					}
					break;

				case FinishingDone:
					foreach (var job in ActiveJobs)
					{
						job.FinishedDate = DateTime.Now;
					}
					//ApplicationSecurityManager.AssertPermission("run.update.finishing");
					break;
			}

			Status = s;

			switch (s)
			{
				case RunStatusOptions.Filling:
					foreach (var j in ActiveJobs)
					{
						j.SetStatus(JobStatusOptions.Filling, user);
					}
					break;

				case RunStatusOptions.LayoutRequired:
					foreach (var j in ActiveJobs)
					{
						j.SetStatus(JobStatusOptions.LayoutRequired, user);
					}
					break;

				case RunStatusOptions.LayoutDone:
					foreach (var j in ActiveJobs)
					{
						j.SetStatus(JobStatusOptions.LayoutDone, user);
					}
					break;


				case RunStatusOptions.DPCPreProduction:
					foreach (var j in ActiveJobs)
					{
						j.SetStatus(JobStatusOptions.DPCPreProduction, user);
					}
					break;

				//1165099
				case RunStatusOptions.ApprovedForPlating:
					foreach (var j in ActiveJobs)
					{
						j.SetStatus(JobStatusOptions.ApprovedForPlating, user);
					}
					break;

				case RunStatusOptions.PlatingDone:
					foreach (var j in ActiveJobs)
					{
						j.SetStatus(JobStatusOptions.PlatingDone, user);
					}
					break;

				case RunStatusOptions.PressDone:
					foreach (var j in ActiveJobs)
					{
						if (j.IsDigital())
							j.SetStatus(JobStatusOptions.DPCPrinted, user);
						else
							j.SetStatus(JobStatusOptions.PressDone, user);
					}
					break;

				case RunStatusOptions.Outwork:
					foreach (var j in ActiveJobs)
					{
						j.SetStatus(JobStatusOptions.Outwork, user);
					}
					break;

				case Finishing:
					foreach (var j in ActiveJobs)
					{
						if (!new[]
						{
							Cut, Folded, Scored,
							Drilled, Perforated,
							Rounded, Celloglazed, Letterpressed,
							Stitched
						}.Contains(j.Status))
						{
							j.SetStatus(Cut, user);
						}
					}
					break;

				case FinishingDone:
					foreach (var j in ActiveJobs)
					{
						j.SetStatus(Finished, user);
					}
					break;

				case PackingDone:
					//SR 1153131
					//foreach (IJob j in jobs) {
					//	if (!j.IsFurtherProcessingRequired && j.JobsOrderIsInSingleRun && j.IsCustomerGood)
					//		j.SetStatus(JobStatusOptions.Packed);
					//}
					break;
			}
			if (status == RunStatusOptions.LayoutDone)
			{
				runOperator = null;
			}
			LogStatusChange();
		}

		public bool CanAccept(IJob job, out bool forceNeeded, out bool oversize, out string message)
		{
			forceNeeded = false;
			oversize = false;
			message = "";

			if (Status != RunStatusOptions.Filling || Facility != job.Facility)
			{
				return false;
			}
			if (Jobs.Contains(job))
			{
				return false;
			}

			if (isBusinessCard)
			{
				if (job.Template.Is(BusinessCard, Postcard, BusinessCardNdd, BusinessCardSdd))
				{
					if (job.Celloglaze.ToString().Contains(celloglaze.ToString())) { }
					else if (job.Celloglaze != celloglaze)
					{
						message = $"Job has cello: {job.Celloglaze} but run has cello: {celloglaze} ";
						return false;
					}

					if (stock.Name != job.FinalStock.Name)
					{
						message = $"Job has stock: {job.FinalStock.Name} but run has stock: {stock.Name} ";
						return false;
					}
				}
			}

			forceNeeded = !HasMeetRule(job);

			if (job.Template.Is(BusinessCard, BusinessCardNdd, BusinessCardSdd, Postcard))
			{
				oversize = CalculateUsedBC() + job.CalculateJobSize() > 42;
			}
			return true;
		}

		public bool HasMeetRule()
		{
			foreach (var j in ActiveJobs)
			{
				if (!HasMeetRule(j))
				{
					return false;
				}
			}
			return true;
		}

		/// <summary>
		/// Add jobs to this run.
		/// Default Runs have null template - only cello, stock and backprint
		/// limits the jobs that can be added.
		/// </summary>
		public void AddJob(IJob job, IStaff staff, bool addComment = true)
		{
			// ApplicationSecurityManager.AssertPermission("run.update.prepress");
			Log.Information(String.Format("Run {0:D6} added job {1:D8}", Id, job.Id));
			var forceneeded = false;
			var oversize = false;
			var message = "";
			if (CanAccept(job, out forceneeded, out oversize, out message))
			{
				Jobs.Add(job);
				job.AddToRun(staff, this, addComment);
				if (forceneeded)
				{
					manuallyManage = true;
				}
				if (isBusinessCard)
				{
					if (CalculateUsedBC() == 42)
					{
						SetStatus(RunStatusOptions.LayoutRequired, staff);
					}
				}
				stock = job.FinalStock;
				cmyk = job.IsCMYK;
				backPrint = job.BackPrinting;
				//always record first job's celloglaze
				celloglaze = Jobs[0].Celloglaze;
				if (job.Urgent)
				{
					urgent = true;
				}
				UpdateRunFlag();
			}
		}

		public void RemoveJob(IJob job, IStaff staff, bool addComment = true)
		{
			//ApplicationSecurityManager.AssertPermission("run.update.prepress");
			if (!Jobs.Contains(job))
			{
				throw new ArgumentOutOfRangeException("job", "Job is not part of this run");
			}
			Jobs.Remove(job);
			job.RemoveFromRun(staff, this, addComment);
			if (jobs.Count > 0)
			{
				stock = jobs[0].FinalStock;
				cmyk = jobs[0].IsCMYK;
				celloglaze = jobs[0].Celloglaze;
				backPrint = jobs[0].BackPrinting;
			}
			UpdateRunFlag();
			Log.Information(String.Format("Run {0:D6} removed job {1:D8}", Id, job.Id));
		}

		public void AddSlot(IJob job, int slot)
		{
			IRunSlot s = new RunSlot();
			s.Job = job;
			s.Slot = slot;
			slots.Add(s);
		}

		/// <summary>
		/// Calculate how many slots this businesscard/poster run has already used
		/// </summary>
		/// <returns></returns>
		public virtual int CalculateUsedBC()
		{
			if(IsBusinessCard && Status >= RunStatusOptions.LayoutRequired && TSlots != null)
			{
				// get the information from the Json
				return TSlots.Value;
			}
			else
			{
				var spaceused = 0;
				foreach (var j in Jobs)
				{
					spaceused += j.CalculateJobSize();
				}
				return spaceused;
			}
		}
		public virtual int? TSlots { get; set; }

		public virtual bool IsHalfBC
		{
			get {
				if (isBusinessCard)
				{
					foreach (var j in jobs)
					{
						if (j.Quantity > 500)
						{
							return false;
						}
					}
					return true;
				}
				return false;
			}
		}

		public virtual bool Is250
		{
			get
			{
				if (isBusinessCard)
				{
					foreach (var j in jobs)
					{
						if (j.Quantity > 250)
						{
							return false;
						}
					}
					return true;
				}
				return false;
			}
		}

		public IList<IJob> ActiveJobs
		{
			get {
				return Jobs;
				//	return jobs.Where(j => j.Enable).ToList();
			}
		}

		private void RemoveLayout(ILayout layout)
		{
			if (layoutFiles.Count > 0)
			{
				foreach (var oldLayout in layoutFiles)
				{
					if (oldLayout.PageNo == layout.PageNo && oldLayout.Type == layout.Type)
					{
						layoutFiles.Remove(oldLayout);
						break;
					}
				}
			}
		}

		private bool HasMeetRule(IJob job)
		{
			if (isBusinessCard)
			{
				if (job.Template.Is(BusinessCard, Postcard, BusinessCardNdd, BusinessCardSdd))
				{
					if (job.Celloglaze == celloglaze && stock.Name == job.FinalStock.Name)
					{
						if (jobs.Count == 0 || (job.Quantity <= 500 && IsHalfBC) || (job.Quantity > 500 && !IsHalfBC))
						{
							if (jobs.Contains(job))
							{
								return CalculateUsedBC() <= 42;
							}
							else
							{
								if (CalculateUsedBC() + job.CalculateJobSize() <= 42)
								{
									return true;
								}
							}
						}
					}
				}
			}
			else if (job.Template.IsNot(Notepads)

			  //&& job.FinishedSize.PaperSize.Name != "8pp A4"
			  //&& job.FinishedSize.PaperSize.Name != "6pp A4"
			  //&& job.FinishedSize.PaperSize.Name != "8pp DL"
			  && job.FinishedSize.PaperSize.Name.IsNot("8pp A4", "6pp A4", "8pp DL")

			  )
			{
				if (stock.Name.Is("80 GSM Uncoated", "90 GSM Uncoated", "100 GSM Uncoated", "80 GSM Recycled Uncoated", "120 GSM Uncoated", "Magnet Back"))
				{
					return stock.Name == job.FinalStock.Name;
				}
			}
			return false;
		}

		private void UpdateRunFlag()
		{
			isBusinessCard = true;
			foreach (var j in jobs)
			{
				if (!j.Template.Is(BusinessCard, Postcard, BusinessCardNdd, BusinessCardSdd))
				{
					isBusinessCard = false;
					break;
				}
			}

			if (!manuallyManage && !HasMeetRule())
			{
				manuallyManage = true;
			}
		}

		private void LogStatusChange()
		{
			Log.Information(String.Format("Run {0:D6} status " + Status.ToString(), Id));
		}

		#region properties

		public virtual int Id
		{
			get { return id; }
			set { id = value; }
		}

		public virtual string RunNr
		{
			get { return String.Format("{0:D6}", id); }
		}

		public virtual string Barcode
		{
			get { return String.Format("R{0}", RunNr); }
		}

		public virtual bool IsBusinessCard
		{
			get { return isBusinessCard; }
			set { isBusinessCard = value; }
		}

		public virtual JobPrintOptions BackPrint
		{
			get { return backPrint; }
			set { backPrint = value; }
		}

		public virtual DateTime? StartedDate { get; set; }

		public virtual DateTime? MinSubmitDate { get { return Jobs.Min(_ => _.ReceivedDate); } }

		public virtual RunStatusOptions Status
		{
			get { return status; }
			set { status = value; }
		}

		public virtual IStaff Operator
		{
			get { return runOperator; }
			set { runOperator = value; }
		}

		public virtual RunCelloglazeOptions Celloglaze
		{
			get { return celloglaze; }
			set { celloglaze = value; }
		}

		public virtual bool Cmyk
		{
			get { return cmyk; }
			set { cmyk = value; }
		}

		public virtual IStock Stock
		{
			get { return stock; }
			set { stock = value; }
		}

		public virtual bool Urgent
		{
			get { return urgent; }
			set { urgent = value; }
		}

		public virtual bool ManuallyManage
		{
			get { return manuallyManage; }
			set { manuallyManage = value; }
		}

		public virtual IList<ILayout> LayoutFiles
		{
			get { return layoutFiles; }
			set { layoutFiles = value; }
		}

		public virtual IList<IJob> Jobs
		{
			get { return jobs; }
			set { jobs = value; }
		}

		public virtual IList<IRunSlot> Slots
		{
			get { return slots; }
			set { slots = value; }
		}

		public DateTime DateCreated { get; set; }

		public DateTime DateModified { get; set; }

		public virtual bool FileRemoved { get; set; } = false;

		public virtual bool FileMoveRequired { get; set; } = false;

		public virtual int? NumOfPressSheets { get; set; }

		public virtual void AddComment(IUser author, string commentText, bool staffOnly = false)
		{
			// Add a comment to the run
			foreach (var job in Jobs)
			{
				job.AddComment(author, commentText, staffOnly);
			}
		}
		public virtual string QtyList { get; set; }

		public IList<IOrder> FreightReadyOrders
		{
			get {
				return ActiveJobs.Where(j =>
						j.IsCustomerGood() &&
						j.JobsOrderIsInSingleRun() &&
						!j.IsFurtherProcessingRequiredForAnyJobInOrder())
					.Select(x => x.Order).Distinct().OrderBy(o => o.Id).ToList();
			}
		}

		public virtual int ScanCount { get; set; }

		public virtual Facility Facility { get; set; }
		public DateTime? EarliestMinEDD
		{
			get {
				var earliestEDDs = Jobs.Select(_ => _.RequiredByDate.HasValue ? _.RequiredByDate : _.Order.DispatchEst);
				var earliestEDD = earliestEDDs.Min();
				return earliestEDD;
			}
		}

		#endregion properties
	}
}
