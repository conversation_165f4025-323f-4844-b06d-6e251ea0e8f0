using Newtonsoft.Json;

namespace lep.courier
{
	using System;

	[Serializable]
	public class CourierType
	{
		public virtual string CourierName { get; set; }
		public virtual string ServiceName { get; set; }

		public virtual string CarrierAccount { get; set; }

		public CourierType()
		{
			this.CourierName = CourierType.None;
			this.ServiceName = CourierType.None;
		}

		public CourierType(string d)
		{
			if (d == null)
			{
				d = "None~None~0";
			}

			if (d == "None")
			{
				d = "None~None~0";
			}

			var parts = d.Split(new[] { '~' });

			if (parts.Length == 3)
			{
				this.CourierName = parts[0].Trim();
				this.ServiceName = parts[1].Trim();
				this.CarrierAccount = parts[2].Trim();
			}
			else if (parts.Length == 2)
			{
				this.CourierName = parts[0].Trim();
				this.ServiceName = parts[1].Trim();
			}
			else
			{
				this.CourierName = parts[0].Trim();
				this.ServiceName = "None";
			}
		}

		public static implicit operator string(CourierType d)
		{
			// if (d.CourierName != CourierType.None && d.ServiceName != CourierType.None)
			return d.CourierName + " ~ " + d.ServiceName + " ~ " + d.CarrierAccount;
		}

		public static implicit operator CourierType(string d)
		{
			if (d == "None")
			{
				d = "None~None";
			}

			var parts = d.Split(new[] { '~' });

			if (parts.Length == 3)
			{
				return new CourierType()
				{
					CourierName = parts[0].Trim(),
					ServiceName = parts[1].Trim(),
					CarrierAccount = parts[2].Trim()
				};
			}
			else if (parts.Length == 2)
			{
				return new CourierType()
				{
					CourierName = parts[0].Trim(),
					ServiceName = parts[1].Trim()
				};
			}
			else
			{
				return new CourierType()
				{
					CourierName = parts[0].Trim(),
					ServiceName = "None"
				};
			}
		}

		public override string ToString()
		{
			return CourierName + " ~ " + ServiceName + " ~ " + CarrierAccount;
		}

		public override int GetHashCode()
		{
			int hash = 13;
			hash = (hash * 7) + CourierName.GetHashCode();
			hash = (hash * 7) + ServiceName.GetHashCode();
			hash = (hash * 7) + CarrierAccount.GetHashCode();
			return hash;
		}

		public override bool Equals(object obj)
		{
			if (obj is CourierType other)
			{
				return this.ToString() == other.ToString();
			}
			return false;
		}
		
		
		[JsonIgnore] /*  */
		public bool IsPickup => CourierName == "Customer" && ServiceName.IndexOf("Pickup", StringComparison.Ordinal) > -1;
		[JsonIgnore] /*  */
		public bool IsPickupFG => CourierName == "Customer" && ServiceName.IndexOf("Pickup from Forest Glen, QLD 4556", StringComparison.Ordinal) > -1;
		[JsonIgnore] /*  */
		public bool IsPickupPM => CourierName == "Customer" && ServiceName.IndexOf("Pickup from Port Melbourne, VIC 3207", StringComparison.Ordinal) > -1;
		//JsonIgnore] /*  */
		public bool IsNone => string.IsNullOrEmpty(CourierName) || CourierName == None || CourierName == None;
		[JsonIgnore] /*  */
		public bool IsTNT => CourierName.Contains(TNT);
		[JsonIgnore] /*  */
		public bool IsAusPost => CourierName.Contains(AusPost) || CourierName.Contains("Australia POST") || CourierName.Contains("AUSTRALIA POST");
		[JsonIgnore] /*  */
		public bool IsFastWay => CourierName.Contains(FastWay);
		[JsonIgnore] /*  */
		public bool IsStarTrack => CourierName.Contains(StarTrack) || CourierName.Contains(StarTrack2);
		[JsonIgnore] /*  */
		public bool IsCourierPleaseVic => CourierName.Contains(CourierVicPlease) || CourierName.Contains(CourierVicPlease2);
		public bool IsOther => CourierName.Contains(CourierVicPlease);

		[JsonIgnore] /*  */
		public bool IsFarlow => CourierName.Contains(Farlow);
		[JsonIgnore] /*  */
		public bool IsTollNqx => CourierName.Contains(TOLLNQX);

		[JsonIgnore] /*  */
		public bool IsAramex => CourierName.Contains(Aramex);
		//

		public const string TNT = "TNT";
		public const string AusPost = "AusPost";
		public const string FastWay = "FASTWAY";
		public const string StarTrack = "StarTrack";
		public const string StarTrack2 = "STARTRACK EXP";
		public const string CourierVicPlease = "COURIER PLEASE VIC";
		public const string CourierVicPlease2 = "COURIERS PLS II";
		public const string TOLLNQX = "TOLL NQX";
		public const string Farlow = "FARLOW TPT";
		public const string Aramex = "ARAMEX";
		public const string None = "None";
		public const string CustomerPickup = "Customer ~ Pickup";
		public const string CustomerPickupFG = "Customer ~ Pickup from Forest Glen, QLD 4556";
		public const string CustomerPickupPM = "Customer ~ Pickup from Port Melbourne, VIC 3207";
		public const string Other = "Other ~ Courier";

		public string ToDescription()
		{
			string val = "";

			if (CourierName == None)
			{
				val = None;
			}
			else if (CourierName == "Customer")
			{
				val = "Customer";
				// ServiceName = "Pickup";
			}
			else if (CourierName == TNT)
			{
				val = "TNT International";
			}
			else if (CourierName == AusPost)
			{
				val = "Australia POST";
			}
			else if (CourierName == FastWay)
			{
				val = "Fastway";
			}
			else if (CourierName == StarTrack)
			{
				val = "Star Track";
			}
			else
			{
				val = CourierName;
			}
			return val + " ~ " + ServiceName;
		}
	}

	//[Serializable]
	//public class CourierType : IUserType
	//{
	//    string Value = CourierType.None;

	//    public CourierType()
	//    {
	//        this.Value = CourierType.None;
	//    }
	//    public CourierType(string value)
	//    {
	//        if (string.IsNullOrEmpty(value))
	//            value = CourierType.None;
	//        this.Value = value;
	//    }
	//    public static implicit operator string(CourierType d)
	//    {
	//        return d.Value.ToString();
	//    }
	//    public static implicit operator CourierType(string d)
	//    {
	//        return new CourierType(d);
	//    }

	//    public bool IsPickup
	//    {
	//        get { return  Value != null &&  Value == Pickup || Value == "Customer - Pickup"; }
	//    }

	//    public bool IsNone
	//    {
	//        get { return Value != null &&  Value == None; }
	//    }

	//    // public void SetAsNone() { Value = "None"; }

	//    // the following 4 are just for old code comatibility
	//    public bool IsTNT { get { return Value.Contains(TNT); } }
	//    public bool IsAusPost { get { return Value.Contains(AustraliaPOST); } }
	//    public bool IsFastWay { get { return Value.Contains(FastWay); } }
	//    public bool IsStarTrack { get { return Value.Contains(StarTrack); } }

	//    public const string TNT = "TNT";
	//    public const string AustraliaPOST = "AustraliaPOST";
	//    public const string FastWay = "FastWay";
	//    public const string StarTrack = "StarTrack";
	//    public const string None = "None";
	//    public const string Pickup = "Pickup";

	//    public string ToDescription()
	//    {
	//        string val = "";

	//        if (Value == None) {
	//            val = None;
	//        } else if (Value == Pickup) {
	//            val = Pickup;
	//        } else if (Value == TNT) {
	//            val = "TNT International";
	//        } else if (Value == AustraliaPOST) {
	//            val = "Australia POST";
	//        } else if (Value == FastWay) {
	//            val = "Fastway";
	//        } else if (Value == StarTrack) {
	//            val = "Star Track";
	//        } else {
	//            val = Value;
	//        }
	//        return val;
	//    }

	//    #region Identity
	//    public override bool Equals(object obj)
	//    {
	//        return this.Value.Equals(  (obj as CourierType).Value);
	//    }

	//    public override int GetHashCode()
	//    {
	//        return Value.GetHashCode();
	//    }

	//    public override string ToString()
	//    {
	//        if (Value != null) return Value;
	//        return "None";
	//    }

	//    #endregion

	//    #region IUserType
	//    public SqlType[] SqlTypes
	//    {
	//        get { return new[] { NHibernateUtil.String.SqlType }; }
	//    }

	//    public Type ReturnedType
	//    {
	//        get { return typeof(CourierType); }
	//    }

	//    public bool IsMutable
	//    {
	//        get { return false; }
	//    }

	//    public object NullSafeGet(IDataReader rs, string[] names, object owner)
	//    {
	//        var obj = NHibernateUtil.String.NullSafeGet(rs, names[0]);
	//        if (obj == null) return new CourierType(CourierType.None);
	//        return new CourierType((string)obj);
	//    }

	//    public void NullSafeSet(IDbCommand cmd, object value, int index)
	//    {
	//        if (value == null) {
	//            ((IDataParameter)cmd.Parameters[index]).Value = "None";
	//        } else {
	//            ((IDataParameter)cmd.Parameters[index]).Value = (value as CourierType).Value;
	//        }
	//    }

	//    public bool Equals(object x, object y)
	//    {
	//        if (x == null && y == null)
	//            return true;
	//        CourierType x1 = x as CourierType;
	//        CourierType x2 = y as CourierType;

	//        if (x1 == null || x2 == null) {
	//            return false;
	//        }

	//        return x1.Value.ToString() == x2.Value.ToString();
	//    }

	//    public int GetHashCode(object x)
	//    {
	//        return x.GetHashCode();
	//    }

	//    public object DeepCopy(object value)
	//    {
	//        if (value != null) {
	//            return new CourierType(((CourierType)value).Value.ToString());
	//        }
	//        return null;
	//    }

	//    public object Assemble(object cached, object owner)
	//    {
	//        return DeepCopy(cached);
	//    }

	//    public object Disassemble(object value)
	//    {
	//        return DeepCopy(value);
	//    }

	//    public object Replace(object original, object target, object owner)
	//    {
	//        return DeepCopy(original);
	//    }
	//    #endregion

	//}

	/*
    public enum CourierType
	{
		None,

        [Description( "Pickup" )]
		Pickup,

        [Description("Star Track")]
        StarTrack,

        [Description("Fastway")]
        FastWay,

        [Description("Australia POST")]
        AustraliaPOST,

        [Description("TNT International")]
        TNT
    }
    */

	//public class Carrier
	//{
	//    public String Name { get; set; }
	//    public String Code { get; set; }
	//    public String Service { get; set; }

	//    public override string ToString()
	//    {
	//        return Name + " - " + Service;
	//    }

	//    bool IsPickup()
	//    {
	//        return Name == "Pickup";
	//    }
	//}
}
