﻿using System;

namespace lep.job.impl
{
	[Serializable]
    public class BindingOption : IBindingOption
    {
        private int fixedValue;
        private int id;
        private string name;
        private int numberFiles;
        private int perSectionValue;
        private int perThousandValue;

        public BindingOption()
        {
        }

        public BindingOption(int id, string name, int numberFiles, int fixedValue, int perThousandValue)
        {
            Id = id;
            Name = name;
            NumberFiles = numberFiles;
            FixedValue = fixedValue;
            PerThousandValue = perThousandValue;
        }

        #region BindingOption Members

        public virtual int Id
        {
            get { return id; }
            set { id = value; }
        }

        public virtual string Name
        {
            get { return name; }
            set { name = value; }
        }

        public virtual int NumberFiles
        {
            get { return numberFiles; }
            set { numberFiles = value; }
        }

        public virtual int FixedValue
        {
            get { return fixedValue; }
            set { fixedValue = value; }
        }

        public virtual int PerThousandValue
        {
            get { return perThousandValue; }
            set { perThousandValue = value; }
        }

        public virtual int PerSectionValue
        {
            get { return perSectionValue; }
            set { perSectionValue = value; }
        }

        #endregion BindingOption Members
    }
}