using lep.job;
using System.IO;
using static System.IO.Path;

namespace lep
{
	public class LepArtworkScriptFiles
    {
        public LepArtworkScriptFiles(string baseFolder)
        {
            LegacyMacWorkflow = false;

            FileInfo _(string s) => new FileInfo(  Path.Combine(baseFolder , s));
            // -------------------- from order app

            OrderMacScript = _("mac-order-status-script.txt");
            OrderWinScript = _("win-order-status-script.txt");
            MountScriptTemplate = _("mac-order-folder-script.txt");
            OrderIcon = _("mac-order-status-script-icon.wflow");
            JobMacPreflightScript = _("mac-job-preflight-script.txt");
            JobMacWaitingApproveScript = _("mac-job-waiting-approve-script.txt");
            JobWinScript = _("win-job-status-script.txt");
            JobRejectScript = _("job-reject-script.txt");
            RejectIcon = _("job-reject-script-icon.txt");
            PreflightIcon = _("mac-job-preflight-script-icon.wflow");
            WaitingApproveIcon = _("mac-job-waiting-approve-script-icon.wflow");
            RunScriptTemplate = _("mac-run-folder-script.txt");
            RunMacScript = _("mac-run-status-script.txt");
            RunMacPauseScript = _("mac-run-pause-script.txt");
            RunWinScript = _("win-run-status-script.txt");
            RunIcon = _("mac-run-status-script-icon.wflow");
            PauseIcon = _("mac-run-pause-script-icon.wflow");

            if (!LegacyMacWorkflow)
            {
                OrderIcon = _("mac-order-status-script-icon.webloc");
                RejectIcon = _("job-reject-script-icon.webloc");
                PreflightIcon = _("mac-job-preflight-script-icon.webloc");
                WaitingApproveIcon = _("mac-job-waiting-approve-script-icon.webloc");
                RunIcon = _("mac-run-status-script-icon.webloc");
                PauseIcon = _("mac-run-pause-script-icon.webloc");
            }
        }

        public FileDetector FileDetector { get; set; } = new FileDetector();

        // from order app
        public bool LegacyMacWorkflow { get; private set; }

        public FileInfo MountScriptTemplate { get; private set; }
        public FileInfo OrderMacScript { get; private set; }
        public FileInfo OrderWinScript { get; private set; }
        public FileInfo OrderIcon { get; private set; }

        // from job app
        public FileInfo PreflightIcon { get; private set; }

        public FileInfo WaitingApproveIcon { get; private set; }
        public FileInfo RejectIcon { get; private set; }
        public FileInfo JobWinScript { get; private set; }

        #region Legacy Mac workflow

        public FileInfo JobMacPreflightScript { get; private set; }
        public FileInfo JobMacWaitingApproveScript { get; private set; }
        public FileInfo JobRejectScript { get; private set; }

        #endregion Legacy Mac workflow

        // from run app
        public FileInfo RunIcon { get; private set; }

        public FileInfo PauseIcon { get; private set; }
        public FileInfo RunScriptTemplate { get; private set; }
        public FileInfo RunMacPauseScript { get; private set; }
        public FileInfo RunMacScript { get; private set; }
        public FileInfo RunWinScript { get; private set; }
    }
}
