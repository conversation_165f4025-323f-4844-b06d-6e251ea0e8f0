using System.Text.RegularExpressions;

namespace lep.job.csv
{
	public class QuantityOptionVerifySchemaHandler : BaseVerifySchemaHandler
	{
		public QuantityOptionVerifySchemaHandler()
			: base()
		{
			fileType = "Spec Range File ";
			headers = new string[]
			{
				"rid", "min", "step1", "change", "step2", "change2"
			};
			regexs = new Regex[]
			{
				new Regex(@"^\d+$"), new Regex(@"^(\d+)||\-$"), new Regex(@"^(\d+)||\-$"), new Regex(@"^(\d+)||\-$"),
				new Regex(@"^(\d+)||\-$"), new Regex(@"^(\d+)||\-$")
			};
		}
	}
}
