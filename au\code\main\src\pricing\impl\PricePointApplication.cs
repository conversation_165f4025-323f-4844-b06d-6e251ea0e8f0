using lep.job;
using lep.pricing.csv;
using lep.security;
using lep.user;

using Serilog;
using lumen.csv;
using NHibernate;
using NHibernate.Criterion;
using NHibernate.Linq;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Reflection;
using static lep.job.JobCelloglazeOptions;
using static lep.job.JobTypeOptions;
using static NHibernate.Criterion.Restrictions;
using lep.job.impl;


namespace lep.pricing.impl
{
	public class PricePointApplication : BaseApplication, IPricePointApplication
	{
		// private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		//private static readonly ILog importLog = LogManager.GetLogger(String.Format("{0}.Import", MethodBase.GetCurrentMethod().DeclaringType.ToString()));
		public PricePointApplication(ISession sf, ISecurityApplication _securityApp, IJobApplication jobApplication)
			: base(sf, _securityApp)
		{
			_jobApp = jobApplication;
		}

		public IJobApplication _jobApp { get; set; }

		public IUserApplication UserApplication { get; set; }



		public IPaperSize FindSmallestFit(IJob job)
		{
			Func<IPaperSize, ISize, bool> PaperSizeCanFitSize = (ps, s) =>
			{
				if (ps.Size.Width >= s.Width && ps.Size.Height >= s.Height)
					return true;
				if (ps.Size.Width >= s.Height && ps.Size.Height >= s.Width)
					return true;
				return false;
			};

			var customSize = job.FinishedSize;

			var possiblePaperSizesForThisJobTemplate = new List<IPaperSize>();

			foreach (var sizeOption in _jobApp.ListSizeOptions(job.Template))
			{
				possiblePaperSizesForThisJobTemplate.Add(sizeOption.PaperSize);
			}

			var candidateSizes0 = from p in possiblePaperSizesForThisJobTemplate
								  where PaperSizeCanFitSize(p, customSize)
								  where p.Name != "Custom"
								  orderby p.Size.Width * p.Size.Height
								  select p;

			var candidateSizes = candidateSizes0.Select(_ => (IPaperSize)Session
																		 .GetSessionImplementation()
																		 .PersistenceContext.Unproxy(_)).ToList();

			if (candidateSizes.Count() == 0)
			{
				// if this happens
			}
			// uptill this point we can return p.first but if the price point does not exist eg
			// user enters 210 x 250

			var siteLoc = job.Order.Customer != null ? job.Order.Customer.SiteLocation : SiteLocation;
			var printTyp = job.PrintType;

			IPaperSize optimalPaperSizeWithExistingPricePoint = null;
			foreach (var cs in candidateSizes)
			{
				var count = (Int32)Session.CreateCriteria(typeof(IPricePoint))
											.Add(Eq("SiteLocation", siteLoc))
											.Add(Eq("Template", job.Template))
											.Add(Eq("PaperSize", cs))
											.SetProjection(Projections.RowCount())
											.UniqueResult();

				if (count > 0)
				{
					optimalPaperSizeWithExistingPricePoint = cs;
					break;
				}
			}

			return optimalPaperSizeWithExistingPricePoint;
		}


		//public IPaperSize FindSmallestFit(IJob job)
		//{
		//	Func<IPaperSize, ISize, bool> PaperSizeCanFitSize = (ps, s) =>
		//	{
		//		if (ps.Size.Width >= s.Width && ps.Size.Height >= s.Height)
		//			return true;
		//		if (ps.Size.Width >= s.Height && ps.Size.Height >= s.Width)
		//			return true;
		//		return false;
		//	};

		//	var customSize = job.FinishedSize;

		//	var possiblePaperSizesForThisJobTemplate = new List<IPaperSize>();
		//	foreach (var sizeOption in job.Template.SizeOptions)
		//	{
		//		possiblePaperSizesForThisJobTemplate.Add(sizeOption.PaperSize);
		//	}

		//	var candidateSizes = (from p in possiblePaperSizesForThisJobTemplate
		//						  where PaperSizeCanFitSize(p, customSize)
		//						  orderby p.Size.Width * p.Size.Height
		//						  select p);
		//	//.Select(_ => (IPaperSize) Session.GetSessionImplementation().PersistenceContext.Unproxy(_)).ToList<IPaperSize>();

		//	return candidateSizes.Where(_ => _.Name != "Custom").FirstOrDefault();
		//}

		public void SetPricePoints(PrintType printType, IJobTemplate template, IStock stock, IPaperSize size,
			int numcoloursides, JobCelloglazeOptions frontcelloglaze, JobCelloglazeOptions backcelloglaze, int numpages,
			Dictionary<int, decimal> prices, Dictionary<int, string> myobNumbers, IUser changeBy, string myob1,
			string myob2)
		{
			var celloglaze = CelloglazeCode(frontcelloglaze, backcelloglaze);

			//RunWithTransaction(Session, () =>
			//{
			foreach (var p in FindPricePoints(SiteLocation, printType, template, stock, size, numcoloursides, frontcelloglaze, backcelloglaze, numpages))
			{
				Delete<IPricePoint>(p);
			}

			foreach (var q in prices.Keys)
			{
				IPricePoint pp = new PricePoint()
				{
					Template = template,
					Stock = stock,
					PaperSize = size,
					NumPages = numpages,
					NumColourSides = numcoloursides,
					Celloglazing = celloglaze,
					Quantity = q,
					Price = prices[q],
					//MYOB = myobNumbers[q],
					ChangeBy = changeBy,
					SiteLocation = SiteLocation,
					PrintType = printType
				};
				Save<IPricePoint>(pp);
			}

			//var productMYOB = FindProductMYOB(SiteLocation, printType, template, stock, size, numcoloursides,
			//	frontcelloglaze, backcelloglaze, numpages);
			//if (productMYOB == null)
			//{
			//	productMYOB = new ProductMYOB(SiteLocation, printType, template, stock, size, numcoloursides,
			//		frontcelloglaze, backcelloglaze, numpages, myob1, myob2);
			//}
			//else
			//{
			//	productMYOB.MYOB1 = myob1;
			//	productMYOB.MYOB2 = myob2;
			//}
			//Save<IProductMYOB>(productMYOB);

			//}
			//);
		}

		public bool IsValidPoints(PrintType printType, IJobTemplate template, IStock stock, IPaperSize size,
			Dictionary<int, decimal> prices)
		{
			foreach (var specsize in _jobApp.ListSizeOptions(template))
			{
				if (specsize.PaperSize == size)
				{
					foreach (var specstock in specsize.StockOptions)
					{
						if (specstock.Stock == stock && specstock.PrintType == printType)
						{
							return ContainEnoughPoint((JobTypeOptions)template.Id, specstock.QuantityOption, prices);
						}
					}
				}
			}
			return false;
		}

		public bool ContainEnoughPoint(JobTypeOptions jobType, IJobOptionSpecQuantity specQ, Dictionary<int, decimal> prices)
		{
			var qtys = QuantitiesByTemplate.CreateEnoughPoints(jobType, specQ);
			return qtys.All((q) => prices.ContainsKey(q));
		}

		public IDictionary<int, decimal> CreateEnoughPoints(JobTypeOptions jobType,
			IJobOptionSpecQuantity specQ, IDictionary<int, decimal> prices)
		{
			Debug.Assert(specQ != null, "specQ is null.");

			Action<int> put = (q) =>
			{
				if (!prices.ContainsKey(q)) prices.Add(q, 0);
			};

			var qtys = QuantitiesByTemplate.CreateEnoughPoints(jobType, specQ);

			foreach (var q in qtys)
			{
				put(q);
			}

			return prices;
		}

		public void ExportPriceCSV(CsvSerialiser serial, int desiredTemplate = 0)
		{
			serial.AlwaysQuote = true;
			serial.RowData(new string[]
			{
				"Job Type", "Size", "Colour", "Cello", "Stock", "Page", "Required", "Qty", "Price", // "MYOB",
				"SiteLocation", "PrintType"
			});

			foreach (var template in _jobApp.ListAllTemplates())
			{
				//magazine separate does not need own prices
				if (template.Is(MagazineSeparate, A4CalendarSeparateCover))
					continue;

				if (desiredTemplate != 0 && template.Id != desiredTemplate)
				{
					continue;
				}

				if (template == null) continue;
				var priceList0 = Session.Query<IPricePoint>()
									 .Where(p => p.SiteLocation == SiteLocation &&
												 p.Template.Id == template.Id
												 ).ToList();

				//var myobs0 = Session.Query<IProductMYOB>()
				//					.Where(p => p.SiteLocation == SiteLocation &&
				//								p.Template.Id == template.Id
				//								).ToList();

				foreach (var size in _jobApp.ListSizeOptions(template))
				{
					foreach (var stock in size.StockOptions)
					{
						foreach (var colour in _jobApp.GetColourOptions(stock))
						{
							foreach (ICelloOption cello in stock.CelloOptions)
							{
								var _cello = String.Format("{0}/{1}", cello.CelloFront.ToString(),
									cello.CelloBack.ToString());
								IList<int> numOfPages = GetNumberOfPages(template);

								foreach (var numOfPage in numOfPages)
								{
									#region loopBody

									IDictionary<int, decimal> savedPriceDict = new SortedDictionary<int, decimal>();

									IDictionary<int, string> savedMyobs = new SortedDictionary<int, string>();
									//
									//read all qty and price in to a dict
									//var priceList = FindPricePoints(SiteLocation, stock.PrintType, template, stock.Stock,
									//    size.PaperSize, colour, cello.CelloFront, cello.CelloBack, numOfPage);
									var celloglaze = CelloglazeCode(cello.CelloFront, cello.CelloBack);
									var priceList = priceList0.Where(px =>
											  px.SiteLocation == SiteLocation &&
											  px.PrintType == stock.PrintType &&
											  px.Template == template &&
											  px.Stock == stock.Stock &&
											  px.PaperSize == size.PaperSize &&
											  px.NumColourSides == colour &&
											  px.Celloglazing == celloglaze &&
											  px.NumPages == numOfPage

									).ToList();

									foreach (var pp in priceList)
									{
										if (!savedPriceDict.ContainsKey(pp.Quantity))
										{
											savedPriceDict.Add(pp.Quantity, pp.Price);
											//savedMyobs.Add(pp.Quantity, pp.MYOB);
										}
									}

									// get the merged list = existing + extapolated
									IList<int> reqdQtys = QuantitiesByTemplate.CreateEnoughPoints((JobTypeOptions)template.Id, stock.QuantityOption);
									MergePricePoint2(stock.QuantityOption, savedPriceDict, reqdQtys);

									foreach (var kvp in savedPriceDict)
									{
										var qty = kvp.Key;
										var price = kvp.Value;
										var quantityRequiredIndicator = reqdQtys.Contains(qty) ? "Y" : "N";
										//var thisPpsMyobNumber = savedMyobs.ContainsKey(qty) ? savedMyobs[qty] : "";

										serial.RowData(new string[]
										{
											template.Name, size.PaperSize.Name, colour.ToString(), _cello,
											stock.Stock.Name, numOfPage.ToString(), quantityRequiredIndicator,
											qty.ToString(), price.ToString(), //thisPpsMyobNumber, 
											SiteLocation.ToString(),
											stock.PrintType.ToString()
										});
									}

									// Export Other MMYOB for this type
									//var productMYOB = FindProductMYOB(SiteLocation, stock.PrintType, template,
									//    stock.Stock, size.PaperSize, colour, cello.CelloFront, cello.CelloBack,
									//    numOfPage);
									var celloglaze2 = CelloglazeCode(cello.CelloFront, cello.CelloBack);
									//var productMYOB = myobs0.Where(m => m.SiteLocation == SiteLocation &&
									//									m.PrintType == stock.PrintType &&
									//									m.Template.Id == template.Id &&
									//									m.Stock.Id == stock.Stock.Id &&
									//									m.PaperSize.Id == size.PaperSize.Id &&
									//									m.NumColourSides == colour &&
									//									m.Celloglazing == celloglaze2 &&
									//									m.NumPages == numOfPage)
									//			 .FirstOrDefault();

									//var thisProductsOtherMYOB = "";

									//if (productMYOB != null)
									//{
									//	thisProductsOtherMYOB = productMYOB.MYOB1;
									//}

									serial.RowData(new string[]
									{
										template.Name, size.PaperSize.Name, colour.ToString(), _cello, stock.Stock.Name,
										numOfPage.ToString(), "Other", "0", "0", //thisProductsOtherMYOB,
										SiteLocation.ToString(), stock.PrintType.ToString()
									});

									#endregion loopBody
								}
							}
						}
					}
				}
			}
		}

		private static IList<int> GetNumberOfPages(IJobTemplate template)
		{
			if (!template.HasMultiplePages())
			{
				return new List<int> { 1 };
			}

			IList<int> numOfPages = new List<int>();

			if (template.Is(WiroMagazines))
			{
				for (var n = 4; n <= 132; n += 2)
				{
					numOfPages.Add(n);
				}
			}
			else if (template.Is(Magazine, MagazineNDD, MagazineSeparate))
			{
				for (var n = 4; n <= 200; n += 4)
				{
					numOfPages.Add(n);
				}
			}
			else if (template.Is(A4CalendarSelfCover))
			{
				numOfPages.Add(28);
			}
			else if (template.Is(A4CalendarSeparateCover))
			{
				numOfPages.Add(24);
			}
			else if (template.Is(Notepads))
			{
				numOfPages = new List<int> { 25, 50, 100 };
			}
			else
			if (template.Is(DuplicateNCRBooks, TriplicateNCRBooks, QuadruplicateNCRBooks))
			{
				numOfPages = new List<int> { 50, 100 };
			}

			return numOfPages;
		}

		public IList<IPricePoint> FindPricePoints(IJob job, IPaperSize finishSize)
		{
			var template = job.Template;
			var frontcello = job.FinalFrontCelloglaze;
			var backcello = job.FinalBackCelloglaze;
			var pages = 0;
			var colourpage = template.ColourSide(job.FrontPrinting, job.BackPrinting);
			if (job.IsMagazineSeparate()) // || job.Template.Id == (int)WiroMagazines
			{
				template = Get<IJobTemplate>((int)Magazine);
				frontcello = None;
				backcello = None;
				colourpage = 2;
			}

			// LORD-1231
			if (job.Celloglaze.ToDescription().Contains("SpotUV") && job.IsPresentationFolder())
			{
				frontcello = Matt;
				backcello = Matt;
			}

			if (template.HasMultiplePages())
			{
				pages = job.Pages;
			}
			var siteLoc = job.Order.Customer != null ? job.Order.Customer.SiteLocation : SiteLocation;
			var printTyp = job.PrintType;
			var pps = FindPricePoints(siteLoc, printTyp, template, job.Stock, finishSize, colourpage, frontcello, backcello, pages);


			if (job.IsWiroMagazine()
				// pages is multiple of 2 but not 4
				&& (job.Pages % 2 == 0 && job.Pages % 4 != 0)
				// no price point found
				&& !pps.Any())
			{
				// get price points for #pages - 2 and #pages + 2
				var ppsLowerPages = FindPricePoints(siteLoc, printTyp, template, job.Stock,
					finishSize, colourpage, frontcello, backcello, pages - 2);
				var ppsHigherPages = FindPricePoints(siteLoc, printTyp, template, job.Stock,
					finishSize, colourpage, frontcello, backcello, pages + 2);

				var newMidPagesPricePoints = new List<IPricePoint>();
				// create new price points with price as average of lower and higher price points
				foreach (var lowerPoint in ppsLowerPages)
				{
					var higherPoint = ppsHigherPages
						.FirstOrDefault(p => p.Quantity == lowerPoint.Quantity);

					if (higherPoint == null)
						continue;

					decimal midPrice = (lowerPoint.Price + higherPoint.Price) / 2;

					IPricePoint newPricePoint = new PricePoint(lowerPoint);
					newPricePoint.Price = midPrice;
					newMidPagesPricePoints.Add(newPricePoint);
				}

				return newMidPagesPricePoints;
			}

			return pps;
		}

		public IList<IPricePoint> FindPricePointsIgnoringPrintType(IJob job)
		{


			IPaperSize finishSize = job.FinishedSize.PaperSize;

			if (job.Template.Id == (int)WiroMagazines)
			{
				return FindPricePoints(job, finishSize);
			}




			var template = job.Template;
			var frontcello = job.FinalFrontCelloglaze;
			var backcello = job.FinalBackCelloglaze;
			var pages = 0;
			var colourpage = template.ColourSide(job.FrontPrinting, job.BackPrinting);
			if (job.IsMagazineSeparate())
			{
				template = Get<IJobTemplate>((int)Magazine);
				frontcello = None;
				backcello = None;
				colourpage = 2;
			}

			// LORD-1231
			if (job.Celloglaze.ToDescription().Contains("SpotUV") && job.IsPresentationFolder())
			{
				frontcello = Matt;
				backcello = Matt;
			}

			if (template.HasMultiplePages())
			{
				pages = job.Pages;
			}
			var siteLoc = job.Order.Customer != null ? job.Order.Customer.SiteLocation : SiteLocation;

			return FindPricePoints(siteLoc, template, job.Stock, finishSize, colourpage, frontcello, backcello, pages);
		}

		public IList<IPricePoint> FindPricePoints(SiteLocation siteLocation, IJobTemplate template, IStock stock,
			IPaperSize size, int numcoloursides, JobCelloglazeOptions frontcelloglaze, JobCelloglazeOptions backcelloglaze)
		{
			return FindPricePoints(siteLocation, template, stock, size, numcoloursides, frontcelloglaze, backcelloglaze, 0);
		}

		public IList<IPricePoint> FindPricePoints(SiteLocation siteLocation, IJobTemplate template, IStock stock,
			IPaperSize size, int numcoloursides, JobCelloglazeOptions frontcelloglaze,
			JobCelloglazeOptions backcelloglaze, int pages)
		{
			var celloglaze = CelloglazeCode(frontcelloglaze, backcelloglaze);
			var cr = Session.CreateCriteria(typeof(IPricePoint))
				.Add(Eq("SiteLocation", siteLocation))
				.Add(Eq("Template", template))
				.Add(Eq("Stock", stock))
				.Add(Eq("PaperSize", size))
				.Add(Eq("NumColourSides", numcoloursides))
				.Add(Eq("Celloglazing", celloglaze));
			if (pages > 0)
			{
				cr.Add(Eq("NumPages", pages));
			}
			return cr.AddOrder(Order.Asc("Quantity")).AddOrder(Order.Asc("NumPages"))
				.List<IPricePoint>();
		}

		public IList<IPricePoint> FindPricePoints(PrintType printType, IJob job, IPaperSize finishSize)
		{
			var template = job.Template;
			var frontcello = job.FinalFrontCelloglaze;
			var backcello = job.FinalBackCelloglaze;
			var pages = 0;
			var colourpage = template.ColourSide(job.FrontPrinting, job.BackPrinting);

			if (job.IsMagazineSeparate())
			{
				template = Get<IJobTemplate>((int)Magazine);
				frontcello = None;
				backcello = None;
				colourpage = 2;
			}

			if (job.IsMagazine())
			{
				pages = job.Pages;
			}
			var siteLoc = job.Order.Customer != null ? job.Order.Customer.SiteLocation : SiteLocation;
			var printTyp = job.PrintType;
			return FindPricePoints(siteLoc, printTyp, template, job.Stock, finishSize, colourpage, frontcello, backcello,
				pages);
		}

		public IList<IPricePoint> FindPricePoints(SiteLocation siteLocation, PrintType printType, IJobTemplate template,
			IStock stock, IPaperSize size, int numcoloursides, JobCelloglazeOptions frontcelloglaze,
			JobCelloglazeOptions backcelloglaze)
		{
			return FindPricePoints(siteLocation, printType, template, stock, size, numcoloursides, frontcelloglaze,
				backcelloglaze, 0);
		}

		public IList<IPricePoint> FindPricePoints(SiteLocation siteLocation, PrintType printType, IJobTemplate template,
			IStock stock, IPaperSize size, int numcoloursides, JobCelloglazeOptions frontcelloglaze,
			JobCelloglazeOptions backcelloglaze, int pages)
		{
			var celloglaze = CelloglazeCode(frontcelloglaze, backcelloglaze);
			var cr = Session.CreateCriteria(typeof(IPricePoint))
				.Add(Eq("SiteLocation", siteLocation))
				.Add(Eq("PrintType", printType))
				.Add(Eq("Template", template))
				.Add(Eq("Stock", stock))
				.Add(Eq("PaperSize", size))
				.Add(Eq("NumColourSides", numcoloursides))
				.Add(Eq("Celloglazing", celloglaze));
			if (pages > 0)
			{
				cr.Add(Eq("NumPages", pages));
			}
			cr.SetCacheable(true);
			return cr.AddOrder(Order.Asc("Quantity")).AddOrder(Order.Asc("NumPages"))
				.List<IPricePoint>();
		}
		/*
		public IProductMYOB FindProductMYOB(IJob job, IPaperSize finishSize)
		{
			var template = job.Template;
			var frontcello = job.FrontCelloglaze;
			var backcello = job.BackCelloglaze;
			var pages = 0;
			var colourpage = template.ColourSide(job.FrontPrinting, job.BackPrinting);
			if (job.IsMagazineSeparate())
			{
				template = Get<IJobTemplate>((int)Magazine);
				frontcello = None;
				backcello = None;
				colourpage = 2;
			}

			if (job.Template.HasMultiplePages())
			{
				pages = job.Pages;
			}
			var siteLoc = job.Order.Customer != null ? job.Order.Customer.SiteLocation : SiteLocation;
			var printType = job.PrintType;

			return FindProductMYOB(siteLoc, printType, template, job.Stock, finishSize, colourpage, frontcello,
				backcello, pages);
		}

		public IProductMYOB FindProductMYOB(SiteLocation siteLocation, PrintType printType, IJobTemplate template,
			IStock stock, IPaperSize size,
			int numcoloursides, JobCelloglazeOptions frontcelloglaze, JobCelloglazeOptions backcelloglaze, int pages)
		{
			var celloglaze = CelloglazeCode(frontcelloglaze, backcelloglaze);
			ICriteria cr = null;
			try
			{
				cr = Session.CreateCriteria(typeof(IProductMYOB))
					.Add(Eq("SiteLocation", siteLocation))
					.Add(Eq("PrintType", printType))
					.Add(Eq("Template", template))
					.Add(Eq("Stock", stock))
					.Add(Eq("PaperSize", size))
					.Add(Eq("NumColourSides", numcoloursides))
					.Add(Eq("Celloglazing", celloglaze));
				if (pages > 0)
				{
					cr.Add(Eq("NumPages", pages));
				}
				var myob_lst = cr.List<IProductMYOB>();

				if (myob_lst == null || myob_lst.Count == 0)
				{
					return null;
					//return new ProductMYOB() {
					//    SiteLocation = siteLocation,
					//    Template = template,
					//    PrintType = printType,
					//    PaperSize = size,
					//    MYOB1 = "ERROR! in CSV!"
					//};
				}

				if (myob_lst != null && myob_lst.Count > 1)
				{
					//log.Warn("FindProductMYOB - multiple myob for same product. ");
					//log.Warn(
					//    $"{siteLocation}, {printType}, Template {template.Id} {template.Name}, Stock:{stock.Id} {stock.Name},   Size {size.Id} {size.Name},  Sides: {numcoloursides},  Cello: {celloglaze}, Pages: {pages}");

					//var dups = String.Join(", ", myob_lst.Select(x => x.MYOB1).ToArray());
					//log.Warn(dups);
				}

				return myob_lst.FirstOrDefault();

				// return cr.UniqueResult<IProductMYOB>();
				//SA: TODO  make sure still a Unique as there is a D or O now.
			}
			catch (Exception ex)
			{
				Log.Error(ex.Message, ex);
			}
			return null;
		}
		*/
		public void DeletePricePoints(PrintType printType, IJobTemplate template, IStock stock, IPaperSize paperSize,
			int numColourSides, string celloglazing, int numPages)
		{
			var cellos = celloglazing.Split('/');
			celloglazing = cellos[0].Substring(0, 1) + cellos[1].Substring(0, 1);
			//CR 20 - Rob wants to clear old prices
			var deleteCr = Session.CreateCriteria(typeof(IPricePoint))
				.Add(Eq("SiteLocation", SiteLocation))
				.Add(Eq("PrintType", printType))
				.Add(Eq("Template", template))
				.Add(Eq("Stock", stock))
				.Add(Eq("PaperSize", paperSize))
				.Add(Eq("NumColourSides", numColourSides))
				.Add(Eq("Celloglazing", celloglazing))
				.Add(Eq("NumPages", numPages));

			foreach (var oldPP in deleteCr.List<IPricePoint>())
			{
				Session.Delete(oldPP);
			}
		}

		public void UpdateOrSavePricePoint(PrintType printType, IJobTemplate template, IStock stock,
			IPaperSize papersize, int numColourSides, string celloglazing, int numPages, int quantity, decimal price,
			string myobNumber)
		{
			//var cellos = celloglazing.Split('/');
			//celloglazing = cellos[0].Substring(0, 1) + cellos[1].Substring(0, 1);

			if (price == 0)
			{
				// price = 0 means delete row
				// find and delete row

				Session.Query<IPricePoint>()
					.Where(p => p.SiteLocation == SiteLocation &&
								p.PrintType == printType &&
								p.Template == template &&
								p.Stock == stock &&
								p.PaperSize == papersize &&
								p.NumColourSides == numColourSides &&
								p.Celloglazing == celloglazing &&
								p.Quantity == quantity &&
								p.NumPages == numPages)
					 .Delete();
			}
			else
			{
				//find row
				//insert if not exists
				// update if already exists
				var cr = Session.CreateCriteria(typeof(IPricePoint))
					.Add(Eq("SiteLocation", SiteLocation))
					.Add(Eq("PrintType", printType))
					.Add(Eq("Template", template))
					.Add(Eq("Stock", stock))
					.Add(Eq("PaperSize", papersize))
					.Add(Eq("NumColourSides", numColourSides))
					.Add(Eq("Celloglazing", celloglazing))
					.Add(Eq("Quantity", quantity))
					.Add(Eq("NumPages", numPages));

				IPricePoint ppOld = null;
				try
				{
					ppOld = cr.UniqueResult<IPricePoint>();

					if (ppOld != null)
					{
						ppOld.Price = price;
						ppOld.MYOB = myobNumber;
						ppOld.ChangeBy = CurrentUser;
						Session.Save(ppOld);
						// this is important in gettting a 3000++% improvement than Save<IPricePoint>( ppOld );
					}
					else
					{
						IPricePoint pp = new PricePoint()
						{
							SiteLocation = SiteLocation,
							PrintType = printType,
							Template = template,
							Stock = stock,
							PaperSize = papersize,
							NumPages = numPages,
							NumColourSides = numColourSides,
							Celloglazing = celloglazing,
							Quantity = quantity,
							Price = price,
							MYOB = myobNumber,
							ChangeBy = CurrentUser,
						};
						Session.Save(pp);
						// this is important in gettting a 3000++% improvement than Save<IPricePoint>( pp );
					}
				}
				catch (Exception ex)
				{
					Log.Error(string.Format("{0}", ex.Message), ex);
				}
			}
		}

		//public void UpdateOrSaveProductMYOB(PrintType printType, IJobTemplate template, IPaperSize paperSize,
		//	IStock stock, int numColourSides, int numPages, string myobNumber, JobCelloglazeOptions frontCelloglaze,
		//	JobCelloglazeOptions backCelloglaze)
		//{
		//	var productMYOB = FindProductMYOB(SiteLocation, printType, template, stock, paperSize, numColourSides,
		//		frontCelloglaze, backCelloglaze, numPages);
		//	if (productMYOB == null)
		//	{
		//		productMYOB = new ProductMYOB(SiteLocation, printType, template, stock, paperSize, numColourSides,
		//			frontCelloglaze, backCelloglaze, numPages, myobNumber, "");
		//	}
		//	else
		//	{
		//		productMYOB.MYOB1 = myobNumber;
		//	}

		//	Save<IProductMYOB>(productMYOB);
		//}

		public string ImportPriceCSV(TextReader reader)
		{
			var result = "";

			var parser = new PriceCsvReader();
			parser.JobApplication = _jobApp;
			parser.PricePointApplication = this;

			try
			{
				Session.FlushMode = FlushMode.Commit;
				RunWithTransaction(Session, () => parser.Parse(reader));
				Session.FlushMode = FlushMode.Auto;

				var resultLine = "\r\nPrice uploading complete";
				string errorLine;

				errorLine = parser.ErrorList.Count == 0
					? "No errors were encountered"
					: parser.ErrorList.Count.ToString() + " errors were encountered";

				Log.Information(resultLine);
				Log.Information(errorLine);

				result += resultLine + "\r\n";
				result += errorLine + "\r\n";

				if (parser.ErrorList.Count > 0)
				{
					result = parser.ErrorList.Aggregate(result, (current, msg) => current + msg + "\r\n");
				}
			}
			catch (Exception ex)
			{
				Log.Error("Error in import job price CSV option  :" + ex.Message, ex);
				result = "Failed reading job price CSV file. <br />" + ex.Message;
			}
			finally
			{
				Session.Flush();
				if (reader != null)
				{
					reader.Close();
				}
			}
			return result;
		}

		/*
		public PrintType? GetPrintTypesAvailable(int templateId, int paperSizeId, int stockId, int quantity)
		{
			PrintType? result = null;

			var earlyDetectedPrintType = GetPrintTypesAvailableFromSpecStock(templateId, paperSizeId, stockId);
			if (earlyDetectedPrintType != null && quantity == 0)
			{
				return earlyDetectedPrintType;
			}

			try
			{
				var pps = Session
					.CreateCriteria(typeof(IPricePoint))
					.Add(Eq("SiteLocation", SiteLocation))
					.Add(Eq("Template", Session.Load<IJobTemplate>(templateId)))
					.Add(Eq("Stock", Session.Load<IStock>(stockId)))
					.Add(Eq("PaperSize", Session.Load<IPaperSize>(paperSizeId)))
					.Add(Gt("Quantity", 0))
					.AddOrder(Order.Asc("Quantity"))
					.List<IPricePoint>();

				Debug.Assert(pps != null, "pps != null");

				int minD = 0, maxD = 0, minO = 0, maxO = 0;

				try
				{
					var ptD = pps.Where(pp => pp.PrintType == PrintType.D).ToList();
					if (ptD.Count > 0)
					{
						// find min max quantities of digital jobs with specified template, papersize and stock
						minD = ptD.Min(pp => pp.Quantity);
						maxD = ptD.Max(pp => pp.Quantity);
					}
				}
				catch (Exception e)
				{
					Log.Error(e.Message, e);
				}
				try
				{
					var ptO = pps.Where(pp => pp.PrintType == PrintType.O).ToList();
					if (ptO.Count > 0)
					{
						// find min max quantities of digital jobs with specified template, papersize and stock
						minO = ptO.Min(pp => pp.Quantity);
						maxO = ptO.Max(pp => pp.Quantity);
					}
				}
				catch (Exception e)
				{
					Log.Error(e.Message, e);
				}

				// if quantity falls in the min/max range we can do the job in that print type
				var digitalAvailable = quantity >= minD && quantity <= maxD;
				var offsetAvailable = quantity >= minO && quantity <= maxO;

				if (digitalAvailable && offsetAvailable)
					result = PrintType.B;
				else if (digitalAvailable)
					result = PrintType.D;
				else
					result = PrintType.O;

				// user enters a quantity like 1 which is less than specified min of both D and O
				if (quantity < minD && quantity < minO)
				{
					result = minD < minO ? PrintType.D : PrintType.O;
				}

				return result;
			}
			catch (Exception e)
			{
				Log.Error(e.Message, e);
				return null;
			}
		}
		*/

		public PrintType? GetPrintTypesAvailable(int templateId, int paperSizeId, int stockId, int quantity)
		{
			//PrintType? result = null;

			//PrintType? earlyDetectedPrintType = null;

			IJobTemplate jobTemplate = null;
			IPaperSize paperSize = null;
			IJobOptionSpecStock specStock = null;
			IJobOptionSpecSize specSize = null;
			var pps = Session.QueryOver<IJobOptionSpecStock>(() => specStock)
				.JoinAlias(() => specStock.JobOptionSpecSize, () => specSize)
				.JoinAlias(() => specSize.JobTemplate, () => jobTemplate)
				.JoinAlias(() => specSize.PaperSize, () => paperSize)
				.Where(a => jobTemplate.Id == templateId && paperSize.Id == paperSizeId && a.Stock.Id == stockId)
				.List<IJobOptionSpecStock>();

			var x = pps.Where(b => b.QuantityOptions.Max() >= quantity &&
								   b.QuantityOptions.Min() <= quantity)
										.Select(c => c.PrintType);
			if (!x.Any())
			{
				x = pps.Where(b => b.QuantityOptions.Max() >= quantity)
					.OrderBy(_ => _.QuantityOption.Minium)
					.Select(c => c.PrintType);
				if (x.Any())
				{
					return x.First();
				}
				else
				{
					x = pps
					.OrderBy(_ => _.QuantityOption.Minium)
					.Select(c => c.PrintType);
				}
			}

			Debug.Assert(pps != null, "pps != null");
			var printTypesWoQty = x.Distinct().ToList();
			if (printTypesWoQty.Count() == 1)
			{
				var onlyPrintTypeAvailableIs = (PrintType?)printTypesWoQty[0];
				return onlyPrintTypeAvailableIs;
			}
			if (printTypesWoQty.Count() == 2 &&

				printTypesWoQty.Contains(PrintType.D) &&
				printTypesWoQty.Contains(PrintType.O))
			{
				return PrintType.B;
			}
			return null;
		}

		public PrintType? GetPrintTypesAll(int templateId, int paperSizeId, int stockId)
		{
			//PrintType? result = null;

			//PrintType? earlyDetectedPrintType = null;

			IJobTemplate jobTemplate = null;
			IPaperSize paperSize = null;
			IJobOptionSpecStock specStock = null;
			IJobOptionSpecSize specSize = null;
			var pps = Session.QueryOver<IJobOptionSpecStock>(() => specStock)
				.JoinAlias(() => specStock.JobOptionSpecSize, () => specSize)
				.JoinAlias(() => specSize.JobTemplate, () => jobTemplate)
				.JoinAlias(() => specSize.PaperSize, () => paperSize)
				.Where(a => jobTemplate.Id == templateId && paperSize.Id == paperSizeId && a.Stock.Id == stockId)
				.List<IJobOptionSpecStock>();

			var x = pps.Select(c => c.PrintType);

			Debug.Assert(pps != null, "pps != null");
			var printTypesWoQty = x.Distinct().ToList();
			if (printTypesWoQty.Count() == 1)
			{
				var onlyPrintTypeAvailableIs = (PrintType?)printTypesWoQty[0];
				return onlyPrintTypeAvailableIs;
			}
			if (printTypesWoQty.Count() == 2 &&

				printTypesWoQty.Contains(PrintType.D) &&
				printTypesWoQty.Contains(PrintType.O))
			{
				return PrintType.B;
			}
			return null;
		}

		public decimal GetPriceMargin(string priceCode, int jobOptionID)
		{
			if (!string.IsNullOrEmpty(priceCode) && (jobOptionID != 0))
			{
				var sql = @"Select dbo.GetProductPriceMargin(:PriceCode, :JobOptionID)";
				IQuery query = Session.CreateSQLQuery(sql);
				query.SetString("PriceCode", priceCode);
				query.SetInt32("JobOptionID", jobOptionID);
				return query.UniqueResult<decimal>();
			}
			else
			{
				return 0;
			}
		}

		/*
		private void MergePricePoint2(IJobOptionSpecQuantity qtyInfo, IDictionary<int, decimal> saved,
			IList<int> reqdQty)
		{
			Action<int> put = (q) =>
			{
				if (!saved.ContainsKey(q))
				{
					saved.Add(q, 0);
				}
				reqdQty.Add(q);
			};

			put(qtyInfo.Minium);
			if (qtyInfo.Change > 0)
			{
				put(qtyInfo.Change);
			}

			if (qtyInfo.Change2 > 0)
			{
				put(qtyInfo.Change2);
			}

			//check for step1, if change exists and change = min + (step1 * Z) then ignore
			if (qtyInfo.Step1 > 0)
			{
				if (qtyInfo.Change > 0)
				{
					if ((qtyInfo.Change - qtyInfo.Minium) % qtyInfo.Step1 != 0)
					{
						throw new Exception("Invalid change point");
					}
				}
				else
				{
					var found = false;
					foreach (var qty in saved.Keys)
					{
						if ((qty - qtyInfo.Minium) % qtyInfo.Step1 == 0)
						{
							found = true;
							reqdQty.Add(qtyInfo.Minium + qtyInfo.Step1);
							break;
						}
					}
					if (!found)
					{
						//saved.Add(qtyInfo.Minium + qtyInfo.Step1, 0);
						put(qtyInfo.Minium + qtyInfo.Step1);
					}
				}
			}

			//check for step2, if change2 exists and change = change + (step2 * Z) then ignore
			if (qtyInfo.Step2 > 0)
			{
				if (qtyInfo.Change2 > 0)
				{
					if ((qtyInfo.Change2 - qtyInfo.Change) % qtyInfo.Step2 != 0)
					{
						throw new Exception("Invalid change point 2");
					}
				}
				else
				{
					var found = false;
					foreach (var qty in saved.Keys)
					{
						if (qty > qtyInfo.Change && (qty - qtyInfo.Change) % qtyInfo.Step2 == 0)
						{
							found = true;
							reqdQty.Add(qtyInfo.Change + qtyInfo.Step2);
							break;
						}
					}
					if (!found)
					{
						//saved.Add(qtyInfo.Change + qtyInfo.Step2, 0);
						put(qtyInfo.Change + qtyInfo.Step2);
					}
				}
			}
		}*/

		private void MergePricePoint2(IJobOptionSpecQuantity qtyInfo, IDictionary<int, decimal> saved, IList<int> reqdQty)
		{
			Action<int> put = (q) =>
			{
				if (!saved.ContainsKey(q))
				{
					saved.Add(q, 0);
				}
			};
			reqdQty.ForEach(x => put(x));
		}

		private string CelloglazeCode(JobCelloglazeOptions frontcelloglaze, JobCelloglazeOptions backcelloglaze)
		{
			string celloglaze = "";
			var fs = frontcelloglaze.ToString();
			var bs = backcelloglaze.ToString();

			//celloglaze = fs[0].ToString() + bs[0].ToString();
			//if (celloglaze.Length != 2) {return ""; }

			celloglaze = fs + "/" + bs;

			return celloglaze;
		}

		//public PrintType? GetPrintTypesAvailable (int templateId, int paperSizeId, int stockId)
		//{
		//    PrintType? result = null;

		//   PrintType? earlyDetectedPrintType = GetPrintTypesAvailableFromSpecStock(templateId, paperSizeId, stockId);
		//    if (earlyDetectedPrintType != null)
		//    {
		//        return earlyDetectedPrintType;
		//    }
		//    // else try to fetch it up from price points

		//    var pps = Session
		//        .CreateCriteria(typeof(IPricePoint))
		//        .Add(Restrictions.Eq("SiteLocation", SiteLocation))
		//        .Add(Restrictions.Eq("Template", Session.Load<IJobTemplate>(templateId)))
		//        .Add(Restrictions.Eq("Stock", Session.Load<IStock>(stockId)))
		//        .Add(Restrictions.Eq("PaperSize", Session.Load<IPaperSize>(paperSizeId)))

		//        .List<IPricePoint>();

		//    Debug.Assert(pps != null, "pps != null");

		//    var printTypesWoQty = pps.Select(x => x.PrintType).Distinct().ToList();
		//    if (printTypesWoQty.Count() == 1) {
		//        var onlyPrintTypeAvailableIs = printTypesWoQty[0];
		//        return onlyPrintTypeAvailableIs;
		//    }

		//    return null;
		//}

		public PrintType? GetPrintTypesAvailableFromSpecStock(int templateId, int paperSizeId, int stockId)
		{
			IJobTemplate jobTemplate = null;
			IPaperSize paperSize = null;
			IJobOptionSpecStock specStock = null;
			IJobOptionSpecSize specSize = null;
			var pps = Session.QueryOver<IJobOptionSpecStock>(() => specStock)
				.JoinAlias(() => specStock.JobOptionSpecSize, () => specSize)
				.JoinAlias(() => specSize.JobTemplate, () => jobTemplate)
				.JoinAlias(() => specSize.PaperSize, () => paperSize)
				.Where(a => jobTemplate.Id == templateId && paperSize.Id == paperSizeId && a.Stock.Id == stockId)
				.Select(c => c.PrintType)
				.List<PrintType>();

			Debug.Assert(pps != null, "pps != null");
			var printTypesWoQty = pps.Distinct().ToList();
			if (printTypesWoQty.Count() == 1)
			{
				var onlyPrintTypeAvailableIs = printTypesWoQty[0];
				return onlyPrintTypeAvailableIs;
			}
			if (printTypesWoQty.Count() == 2 &&

				printTypesWoQty.Contains(PrintType.D) &&
				printTypesWoQty.Contains(PrintType.O))
			{
				return PrintType.D;
			}
			return null;
		}

		/*
        public IEnumerable<PrintType> GetPrintTypesAvailable2( int templateId, int quantity, int paperSizeId, int stockId )
        {
            PrintType? result = null;
            var pps = Session
                .CreateCriteria( typeof( IPricePoint ) )
                .Add( Restrictions.Eq( "SiteLocation", SiteLocation ) )
                .Add( Restrictions.Eq( "Template", Session.Load<IJobTemplate>( templateId ) ) )
                .Add( Restrictions.Eq( "Stock", Session.Load<IStock>( stockId ) ) )
                .Add( Restrictions.Eq( "PaperSize", Session.Load<IPaperSize>( paperSizeId ) ) )
                .Add( Restrictions.Gt( "Quantity", 0 ) )
                .AddOrder( Order.Asc( "Quantity" ) )
                .List<IPricePoint>();

            Debug.Assert( pps != null, "pps != null" );

            var ranges = from p in pps
                         group p by p.PrintType
                             into g
                             select new {
                                 printType = g.Key,
                                 min = g.Min( x => x.Quantity ),
                                 max = g.Max( x => x.Quantity )
                             };

            var printTypesForGivenQuantity = from r in ranges
                                             where quantity >= r.min && quantity <= r.max
                                             select r.printType;
            return printTypesForGivenQuantity;
        }
        */
	}
}
