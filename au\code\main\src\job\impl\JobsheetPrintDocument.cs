using lep.configuration;
using lep.extensionmethods;
using lep.run;

using Serilog;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Printing;
using System.IO;
using System.Linq;
using System.Reflection;

namespace lep.job.impl
{
	public class JobSheetPrintDocument : PrintDocument
	{
		//public string PrinterName
		//{ get; set; }

		// private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);
		private IConfigurationApplication ConfigurationApplication;

		private int currentIndex = 0;
		private IList<IJob> multijobs = new List<IJob>();

		private IRun run;
		private string tickimg;

		public JobSheetPrintDocument(IList<IJob> jobs, string tickpath, IConfigurationApplication configApplication)
			: base()
		{
			//multijobs = jobs.Where(j => !j.IsDigital()).ToList();
			//multijobs = jobs.ToList();

			foreach (IJob job in jobs)
			{
				multijobs.Add(job);
				if (job.IsMagazineSeparate())
				{
					multijobs.Add(job);
				}
			}

			run = multijobs[0].Runs[0];

			tickimg = tickpath;
			ConfigurationApplication = configApplication;
		}

		public JobSheetPrintDocument(IRun run, string tickpath, IConfigurationApplication configApplication)
			: base()
		{
			this.run = run;

			foreach (IJob job in run.Jobs)
			{
				multijobs.Add(job);
				if (job.IsMagazineSeparate())
				{
					multijobs.Add(job);
				}
			}

			tickimg = tickpath;
			ConfigurationApplication = configApplication;
		}

		public bool PrintBCasNonBC { get; set; }

		public int gX { get; set; }
		public int gY { get; set; }

		protected override void OnPrintPage(PrintPageEventArgs e)
		{
			gX = -20;
			gY = -16;
			//gX = 0;
			//gY = 0;
			base.OnPrintPage(e);
			//e.Graphics.PageUnit = GraphicsUnit.Millimeter;
			//this.DefaultPageSettings = ps.DefaultPageSettings;
			//e.Graphics.DrawImage( System.Drawing.Image.FromFile( "C:\\jobsheet96dpi4.gif" ), new Point( 0, 0 ) );

			if (LepGlobal.Instance.TestBox)
			{
				using (var imgbg = System.Drawing.Image.FromFile(@"C:\LepSF\A3_JOBSHEET_PRINT-1.jpg"))
				{
					var o = new Point(-10, -10);
					e.Graphics.DrawImage(imgbg, o);
				}
			}
			
			
			try
			{
				if (run != null && !run.IsBusinessCard)
				{
					DoPrintNonBC(gX, gY, e.Graphics, multijobs[currentIndex]);
					if (currentIndex < multijobs.Count - 1)
					{
						currentIndex++;
						e.HasMorePages = true;
					}
				}
				else if (PrintBCasNonBC)
				{
					DoPrintNonBC(gX, gY, e.Graphics, multijobs[currentIndex]);
					if (currentIndex < multijobs.Count - 1)
					{
						currentIndex++;
						e.HasMorePages = true;
					}
				}
				else
				{
					DoPrintBC(gX, gY, e.Graphics);
					e.HasMorePages = false;
				}
			}
			catch (Exception ex)
			{
				Log.Error(ex.Message, ex);
			}
		}

		private string StockString(IStock stock)
		{
			return stock.Name.Replace("Deluxe", "").Replace("LEP", "");
		}

		private string StockString(string stock)
		{
			return stock.Replace("Deluxe", "").Replace("LEP", "");
		}

		public string JobInfoElide(IJob job)
		{
			var jobinfo = job.Name.Length > 16 ? job.Name.Substring(0, 13) + "..." : job.Name;
			return String.Format("{2} - {0} x {1:0.##}K ({2})", jobinfo, job.Quantity / 1000.0, job.JobNr, job.Facility);
		}

		private void DoPrintNonBC(int x, int y, Graphics g, IJob job)
		{
			var cellFormat = new StringFormat();
			cellFormat.Trimming = StringTrimming.EllipsisCharacter;

			var wordFont = new Font("Arial", 10);
			var jobFont = new Font("Arial", 12, FontStyle.Bold);
			Brush brush = new SolidBrush(Color.Black);
			Font barcodefont = new Font(new Font("Free 3 of 9", 12F).FontFamily, (float)38, FontStyle.Regular);

			if (barcodefont.Name.IndexOf("3 of 9", StringComparison.Ordinal) == -1)
			{
				barcodefont = new Font(new Font("3 of 9 Barcode", 12F).FontFamily, (float)38, FontStyle.Regular);
			}

			//var tickImg = Image.FromFile(tickimg);

			var jobFontSize = g.MeasureString("Anything", jobFont);

			//barcode

			//g.DrawString( job.Barcode, jobFont, brush, x + 872, y + 68 );
			g.DrawString(String.Format("{0} ({1})", job.Barcode, job.Facility), jobFont, brush, x + 872, y + 68);

			g.DrawString(String.Format("*{0}*", job.Barcode), barcodefont, brush, x + 872, y + 100);

			if (job.Runs.Count > 0)
			{
				g.DrawString(job.Runs[0].Barcode, jobFont, brush, x + 872, y + 155);
			}

			//===================================================================================================
			// JOB DESCRIPTION - Black Box
			//===================================================================================================
			//Job Type , Stock, required by , date rcvd

			var l = 125;
			g.DrawString(job.Template.Name, jobFont, brush, x + l, y + 35);
			g.DrawString(StockString(job.FinalStock), jobFont, brush, x + 385, y + 35);

			if (job.RequiredByDate != null)
			{
				g.DrawString(job.RequiredByDate.Value.ToString("dd/MM/yyyy"), jobFont, brush, x + 690, y + 35);
			}

			#region draw art ready thumbnail

			try
			{
				var thumbs2 = LepGlobal.Instance.GetThumbs(job).Where(fn => !fn.Name.Contains("_bk")); ;
				if (thumbs2.Any())
				{
					using (Stream fs = thumbs2.First().OpenRead())
					{
						using (var image = Image.FromStream(fs, true))
						{
							g.DrawImage(image, 0, 1450, 150, 200);
							fs.Close();
						}
					}
				}
			}
			catch (Exception ex)
			{
				Log.Error(ex.Message, ex);
			}

			#endregion draw art ready thumbnail

			if (job.ReceivedDate.HasValue)
				g.DrawString(job.ReceivedDate.Value.ToString("dd/MM/yyyy HH:mm"), jobFont, brush, x + 968, y + 35);

			//Job Name, order number
			g.DrawString(job.Name, jobFont, brush, x + l, y + 68);
			g.DrawString(job.Order.OrderNr, jobFont, brush, x + 670, y + 68);

			//Job Qty Page Number,1 /2  sided
			g.DrawString(job.Quantity.ToString(), jobFont, brush, x + l, y + 100);

			// Number of pages

			// if notepads or deskpads
			var typesNotepadDeskpad = new List<JobTypeOptions>() { JobTypeOptions.Notepads };
			if (typesNotepadDeskpad.Contains((JobTypeOptions)job.Template.Id))
			{
				var defaultPP = job.Pages + " pages";
				if (job.Pages == 0)
				{
					if (new List<int>() { 22, 23 }.Contains(job.FinishedSize.PaperSize.Id))
						defaultPP = "25pages";
					else if (new List<int>() { 24, 25, 26 }.Contains(job.FinishedSize.PaperSize.Id))
						defaultPP = "50pages";
				}

				g.DrawString(defaultPP, jobFont, brush, x + 438, y + 100); // show number of pages
			}

			// if magazines or separaet magazine
	 
			if (job.IsMagazine())
			{
				g.DrawString(job.Pages.ToString() + "pp", jobFont, brush, x + 438, y + 100);
			}

			//g.DrawString( "1/Sided", wordFont, brush, x + 305, y + 102 );
			//g.DrawString( "2/Sided", wordFont, brush, x + 415, y + 102 );
			if (!job.IsCMYK)
			{
				g.DrawString("✓", jobFont, brush, x + 664, y + 100);
			}
			else
			{
				g.DrawString("✓", jobFont, brush, x + 794, y + 100);
			}

			//Job Size width height, TODO FOLDED
			var paperSize = job.FinishedSize.PaperSize.Name;

			paperSize = SanitizeString(paperSize);

			//SR 1114850 - Show Potrait or Landscape as P/L after size
			var typesWithoutRotation = new List<JobTypeOptions>()
			{
				JobTypeOptions.BusinessCard,
				JobTypeOptions.DoubleBusinessCard,
				JobTypeOptions.BusinessCardNdd, JobTypeOptions.BusinessCardSdd,
				JobTypeOptions.PresentationFolder
			};

			if (!typesWithoutRotation.Contains((JobTypeOptions)job.Template.Id))
			{
				// if nit presentation folder
				paperSize += job.Rotation == RotationOption.Portrait ? "(P)" : "(L)"; // show rotation
			}

			g.DrawString(paperSize, jobFont, brush, x + l, y + 130);
			g.DrawString(job.FinishedSize.Width.ToString(), jobFont, brush, x + 310, y + 130);
			g.DrawString(job.FinishedSize.Height.ToString(), jobFont, brush, x + 480, y + 130);

			//g.DrawString( job.MYOB, wordFont, brush, x + 380, 132 );

			if (job.FoldedSize != null)
			{
				if (job.FoldedSize.PaperSize.Name == "Custom")
				{
					g.DrawString(
						String.Format("{0}({1}x{2})", job.FoldedSize.PaperSize.Name, job.FoldedSize.Height,
							job.FoldedSize.Width), wordFont, brush, x + 660, y + 130);
				}
				else
				{
					g.DrawString(SanitizeString(job.FoldedSize.PaperSize.Name), jobFont, brush, x + 660, y + 130);
				}
			}


				try
				{
					string binding = job.BindingOption?.Name ?? "";
					if(job.BoundEdge != JobBoundEdgeOptions.None)
					{
						binding += "   "  + job.BoundEdge.ToDescription().Substring(0, 1);
					}
					g.DrawString(binding, jobFont, brush, x + 660, y + 160);
				} catch(Exception ex)
				{
					Log.Error(ex, ex.Message);
				}


			// TODO      REPRINT , price
			if (job.IsReprint)
			{
				var rstr = String.Format("From J{0}, R{1} {2}", job.ReprintFromPreviousJobNo,
					job.ReprintFromPreviousRunNo, job.ReprintReason);
				var r = new Rectangle(x + 38, y + 162, 530, 32);
				g.DrawString(rstr, wordFont, brush, r, cellFormat);
			}

			//=========================================================================================
			// Customer details
			//=========================================================================================
			g.DrawString(job.Order.Customer.Name, jobFont, brush, x + l, y + 253);
			g.DrawString(job.Order.PurchaseOrder, jobFont, brush, x + 960, y + 253);
			g.DrawString(job.Order.Contact.Name, jobFont, brush, x + l, y + 285);
			g.DrawString(job.Order.Contact.AreaCode + " " + job.Order.Contact.Phone, jobFont, brush, x + 542, y + 285);
			g.DrawString(job.Order.Contact.Mobile, jobFont, brush, x + 900, y + 285);

			//=========================================================================================
			// Additional Production Information
			//=========================================================================================
			var size = g.MeasureString(job.SpecialInstructions, wordFont, 542);
			var rect = new Rectangle(x + 33, y + 364, 565, 135);
			g.DrawString(job.SpecialInstructions, wordFont, brush, rect, cellFormat);

			//=========================================================================================
			// Additional Special Information
			//=========================================================================================
			var productionInstructions = job.ProductionInstructions;
			var celloExtra = job.CelloStringsExtra();
			if(celloExtra != "")
			{
				productionInstructions += "\n" + celloExtra;
			}

			var sizePI = g.MeasureString(productionInstructions, wordFont, 542);
			var rect2 = new Rectangle(620, y + 364, 530, 135);
			g.DrawString(productionInstructions, wordFont, brush, rect2, cellFormat);

			#region hide

			//if (job.Proofs.ProofsRequired) {
			//    g.DrawImage( tickImg, x + 155, y + 165 );
			//}
			//g.DrawString( job.Proofs.NumProofsSentA1.ToString(), wordFont, brush, x + 380, y + 165 );
			//g.DrawString( job.Proofs.NumProofsSentA2.ToString(), wordFont, brush, x + 445, y + 165 );
			//g.DrawString( job.Proofs.NumProofsSentA3.ToString(), wordFont, brush, x + 512, y + 165 );

			//prepress-printing
			/*
            g.DrawString( StockString(job.Stock) ,wordFont,brush,x + 160,y + 270 );
            if (job.Prepress.OneSided.Sheets > 0) {
                g.DrawString( job.Prepress.OneSided.Sheets.ToString(),wordFont,brush,x + 160,y + 300 );
            }
            if (job.Prepress.OneSided.A1) {
                g.DrawImage( tickImg,x + 360,y + 300 );
            }
            if (job.Prepress.OneSided.A2) {
                g.DrawImage( tickImg,x + 460,y + 300 );
            }

            if (job.Prepress.WorkAndTurn.Sheets > 0) {
                g.DrawString( job.Prepress.WorkAndTurn.Sheets.ToString(),wordFont,brush,x + 160,y + 332 );
            }
            if (job.Prepress.WorkAndTurn.A1) {
                g.DrawImage( tickImg,x + 360,y + 332 );
            }
            if (job.Prepress.WorkAndTurn.A2) {
                g.DrawImage( tickImg,x + 460,y + 332 );
            }

            if (job.Prepress.WorkAndTumble.Sheets > 0) {
                g.DrawString( job.Prepress.WorkAndTumble.Sheets.ToString(),wordFont,brush,x + 160,y + 365 );
            }
            if (job.Prepress.WorkAndTumble.A1) {
                g.DrawImage( tickImg,x + 360,y + 365 );
            }
            if (job.Prepress.WorkAndTumble.A2) {
                g.DrawImage( tickImg,x + 460,y + 365 );
            }

            if (job.Prepress.SheetWork.Sheets > 0) {
                g.DrawString( job.Prepress.SheetWork.Sheets.ToString(),wordFont,brush,x + 160,y + 395 );
            }
            if (job.Prepress.SheetWork.A1) {
                g.DrawImage( tickImg,x + 360,y + 395 );
            }
            if (job.Prepress.SheetWork.A2) {
                g.DrawImage( tickImg,x + 460,y + 395 );
            }

            if (job.Prepress.Cover.Sheets > 0) {
                g.DrawString( job.Prepress.Cover.Sheets.ToString(),wordFont,brush,x + 160,y + 425 );
            }
            if (job.Prepress.Cover.A1) {
                g.DrawImage( tickImg,x + 360,y + 425 );
            }
            if (job.Prepress.Cover.A2) {
                g.DrawImage( tickImg,x + 460,y + 425 );
            }

            if (job.Prepress.PrepressBy != null) {
                g.DrawString( String.Format( "{0} {1}",job.Prepress.PrepressBy.FirstName,job.Prepress.PrepressBy.LastName ),wordFont,brush,x + 630,y + 300 );
                g.DrawString( job.Prepress.PrepressDate.ToString( "dd/MM/yyyy HH:mm" ),wordFont,brush,x + 630,y + 330 );
            }

            if (job.PrintedDate > DateTime.MinValue) {
                g.DrawString( job.PrintedDate.ToString( "dd/MM/yyyy HH:mm" ),wordFont,brush,x + 630,y + 360 );
            }

            if (job.StockForCover != null) {
                g.DrawString( StockString(job.StockForCover) ,wordFont,brush,x + 630,y + 425 );
            }*/

			#endregion hide

			//=========================================================================================
			// pre-press /printing detail red box
			//=========================================================================================
			for (var i = 0; i < job.PressDetails.Count; i++)
			{
				//SR 1130601,  Max prepress details is 5 now
				if (i == 5)
					break;
				var posy = 584 + 32 * i;

				string stock = job.PressDetails[i].Stock;
				if(job.StockOverride != null)
					stock = job.StockOverride.Name;
				g.DrawString(StockString(stock), jobFont, brush, x + 38, y + posy);
				if (job.PressDetails[i].Sect > 0)
				{
					g.DrawString(job.PressDetails[i].Sect.ToString(), jobFont, brush, x + 274, y + posy);
				}
				g.DrawString(job.PressDetails[i].Size, jobFont, brush, x + 342, y + posy);
				g.DrawString(job.PressDetails[i].Method, jobFont, brush, x + 410, y + posy);
				if (job.PressDetails[i].Qty > 0)
				{
					g.DrawString(job.PressDetails[i].Qty.ToString(), jobFont, brush, x + 514, y + posy);
				}
			}

			//===================================================================================================
			// BINDERY - Green box
			//===================================================================================================

			// folding
			if (job.FoldedSize != null)
			{
				if (job.FoldedSize.PaperSize.Name == "Custom")
				{
					g.DrawString(
						String.Format("{0}({1}x{2})", job.FoldedSize.PaperSize.Name, job.FoldedSize.Height,
							job.FoldedSize.Width), jobFont, brush, x + 159, y + 794);
				}
				else
				{
					g.DrawString(SanitizeString(job.FoldedSize.PaperSize.Name), jobFont, brush, x + 159, y + 794);
				}
			}

			// finishing
			g.DrawString(
				String.Format("{0} ({1})", SanitizeString(job.FinishedSize.PaperSize.Name), job.Rotation.ToString()[0]),
				jobFont, brush, x + 159, y + 824);
			g.DrawString(job.FinishedSize.Width.ToString(), jobFont, brush, x + 366, y + 824);
			g.DrawString(job.FinishedSize.Height.ToString(), jobFont, brush, x + 523, y + 824);

			// padding
			if (job.Template.Id == (int)JobTypeOptions.Notepads)
			{
				if (job.PadDirection != PadDirection.None)
				{
					g.DrawString(String.Format("{0}", job.PadDirection.ToDescription()), jobFont, brush, x + 420,
						y + 792);
				}
			}

			// scoring
			if (job.Scoring)
			{
				g.DrawString("✓", jobFont, brush, x + 162, y + 854);
			}

			rect = new Rectangle(x + 238, y + 848, 350, (int)jobFontSize.Height);
			g.DrawString(job.ScoringInstructions, jobFont, brush, rect, cellFormat);

			// perforating
			if (job.Perforating)
			{
				g.DrawString("✓", jobFont, brush, x + 162, y + 888);
			}

			rect = new Rectangle(x + 238, y + 888, 350, (int)jobFontSize.Height);
			g.DrawString(job.PerforatingInstructions, jobFont, brush, rect, cellFormat);

			//diecut
			rect = new Rectangle(x + 162, y + 917, 437, (int)jobFontSize.Height);
			if (job.DieCutType != CutOptions.None)
			{
				g.DrawString(job.DieCutType.ToString() + " " + job.DieCutting, jobFont, brush, rect);
			}
			if (job.HoleDrilling != HoleDrilling.None)
			{
				var hdstr = "Hole Drilling: " + job.HoleDrilling.ToDescription();
				if (job.NumberOfHoles != null && job.NumberOfHoles > 0)
				{
					hdstr += ", #Of Holes: " + job.NumberOfHoles;
				}
				g.DrawString(hdstr, jobFont, brush, rect);
			}

			//cello
			//SR 1118175 When Celloglaze Front or back = none do not print none
			g.DrawString(job.CelloStringsFront(), jobFont, brush, x + 204, y + 950);
			g.DrawString(job.CelloStringsBack(), jobFont, brush, x + 360, y + 950);
		

			if (job.NumberOfMagnets > 0)
			{
				g.DrawString(job.NumberOfMagnets.ToString(), jobFont, brush, x + 531, y + 950);
			}

			//===================================================================================================
			// Packing Despatch - Blue Box
			//===================================================================================================

			// Address
			//rect = new Rectangle( x + 10, y + 1060, 266, (int) jobFontSize.Height );
			//String.Format("{0} {1}\r\n{2}\r\n{3}\r\n{4}\r\n{5}\r\n{6} {7}\r\n{8}", job.Order.RecipientName,
			//		job.Order.RecipientPhone, job.Order.DeliveryAddress.Address1, job.Order.DeliveryAddress.Address2,
			//      job.Order.DeliveryAddress.Address3, job.Order.DeliveryAddress.City, job.Order.DeliveryAddress.State,
			//      job.Order.DeliveryAddress.Postcode, job.Order.DeliveryAddress.Country)


			var dispatchBoxText = "";
			dispatchBoxText = job.Order.PackWithoutPallets ? "*** Pack Loose Cartons *** \n" : "";
			
			if(!job.HasSplitDelivery){
				dispatchBoxText += job.BrochureDistPackInfo?.ToInstructionsText() ?? "";
				dispatchBoxText += (job.Freight?.Packages?.ToString() ?? "");
				dispatchBoxText = dispatchBoxText.Replace("\n\n", "\n");
				g.DrawString(dispatchBoxText, wordFont, brush, x + 40, y + 1060);
			}

			var notestemp = "";
			if (job.Order.CustomerLogoRequired)
			{
				notestemp = "*** Logo label required ***" + '\n';
			}
			if (job.SendSamples == true)
			{
				notestemp += "*** Send samples ***" + '\n';
			}

			if(job.HasSplitDelivery)
			{
				notestemp += "*** SPLIT Delivery ***" + '\n';

				if(job.Splits.Any(s => s.CustomerLogoRequired))
				{
					notestemp += "*** Logo label required ***" + '\n';
				}
				if (job.Order.Customer.SendSamples)
				{

				}
				////if(job.Splits.Any(s => s.SendSamples))
				////{
				////	notestemp += "*** Send samples ***" + '\n';
				////}

			}

			if (job.Order.Customer.Notes != null && job.Order.Customer.Notes.Length > 0)
			{
				notestemp += job.Order.Customer.Notes;
			}

			if (notestemp != null && notestemp.Length > 0)
			{
				rect = new Rectangle(x + 333, y + 1064, 250, 140);
				g.DrawString(notestemp, wordFont, brush, rect);
			}

			// courier
			if (!job.Order.Courier.IsNone)

			{
				g.DrawString(job.Order.Courier, wordFont, brush, x + 288, y + 1000);
			}

			//===================================================================================================
			// List of all jobs in the current job's Order
			//===================================================================================================
			var jobinfo2 = "";
			foreach (var jx in job.Order.Jobs)
			{
				var jobinfo = jx.Name.Length > 16 ? jx.Name.Substring(0, 13) + "..." : job.Name;
				jobinfo2 += String.Format("{0} - {1} x {2:0.##}K\n", jx.JobNr, jobinfo, jx.Quantity / 1000.0);
			}
			jobinfo2 += job.TopLevelPackages();
			jobinfo2 = jobinfo2.Replace("\n\n", "\n");

			//rect = new Rectangle(x + 40, y + 1230, 371, 153);
			//g.DrawString(jobinfo2, wordFont, brush, rect, cellFormat);
			g.DrawString(jobinfo2, wordFont, brush, new Point(x + 40, y + 1230));
		}

		// if the Run is a BC run then the job Sheet is used like a summary of all the jobs in the run
		private void DoPrintBC(int x, int y, Graphics g)
		{
			var cellFormat = new StringFormat();
			cellFormat.Trimming = StringTrimming.EllipsisCharacter;

			var wordFont = new Font("Arial", 10);
			var jobFont = new Font("Arial", 14, FontStyle.Bold);
			Brush brush = new SolidBrush(Color.Black);
			var barcodefont = new Font(new Font("Free 3 of 9", 12F).FontFamily, (float)38, FontStyle.Regular);
			//var tickImg = Image.FromFile(tickimg);

			var jobFontSize = g.MeasureString("Anything", jobFont);

			//barcode
			g.DrawString(run.Barcode, jobFont, brush, x + 872, y + 68);
			g.DrawString(String.Format("*{0}*", run.Barcode), barcodefont, brush, x + 872, y + 100);

			//===================================================================================================
			// JOB DESCRIPTION - Black Box
			//===================================================================================================
			//Job Type , Stock, required by , date rcvd
			var l = 125;
			g.DrawString("Business Card", jobFont, brush, x + l, y + 35);
			g.DrawString(StockString(run.Stock), jobFont, brush, x + 390, y + 35);

			if(run.EarliestMinEDD != null)
			{
				g.DrawString("EDD: " + run.EarliestMinEDD.Value.ToString("dd/MM/yy"), jobFont, brush, x + 690, y + 35);
			} 
			
			// TODO      REPRINT , price
			try
			{
				var countOfReprinOrRestartsInRun = run.Jobs.Count(j => j.IsReprint);
				if (countOfReprinOrRestartsInRun > 0)
				{
					var ids = run.Jobs.Where(j => j.IsReprint).Select(j => j.Id.ToString()).ToArray();
					var idsStr = String.Join(",", ids);
					var r = new Rectangle(x + 38, y + 162, 530, 32);
					g.DrawString(countOfReprinOrRestartsInRun.ToString() + " Reprint/Restarted: " + idsStr, wordFont,
						brush, r,
						cellFormat);
				}
			}
			catch (Exception ex) { }

			//===================================================================================================
			// pre-press /printing detail red box
			//===================================================================================================

			var posy = 584;
			g.DrawString(StockString(run.Stock), jobFont, brush, x + 38, y + posy);
			g.DrawString("1", jobFont, brush, x + 274, y + posy);
			
			if(run.PrintType == PrintType.O)
				g.DrawString("A2", jobFont, brush, x + 342, y + posy);
			else  if (run.PrintType == PrintType.D)
				g.DrawString("A3", jobFont, brush, x + 342, y + posy);

			g.DrawString(run.BackPrint != JobPrintOptions.Unprinted ? "SW" : "SS", jobFont, brush, x + 410, y + posy);
			g.DrawString(run.NumOfPressSheets.ToString(), jobFont, brush, x + 514, y + posy);

			//===================================================================================================
			// BINDERY - Green box
			//===================================================================================================
			if (run.Celloglaze == RunCelloglazeOptions.GlossBoth)
			{
				g.DrawString("Gloss", jobFont, brush, x + 204, y + 950);
				g.DrawString("Gloss", jobFont, brush, x + 360, y + 950);
			}
			else if (run.Celloglaze == RunCelloglazeOptions.GlossFrontMattBack)
			{
				g.DrawString("Gloss", jobFont, brush, x + 204, y + 950);
				g.DrawString("Matt", jobFont, brush, x + 360, y + 950);
			}
			else if (run.Celloglaze == RunCelloglazeOptions.GlossFront)
			{
				g.DrawString("Gloss", jobFont, brush, x + 204, y + 950);
			}
			else if (run.Celloglaze == RunCelloglazeOptions.MattBoth)
			{
				g.DrawString("Matt", jobFont, brush, x + 204, y + 950);
				g.DrawString("Matt", jobFont, brush, x + 360, y + 950);
			}
			else if (run.Celloglaze == RunCelloglazeOptions.MattFront)
			{
				g.DrawString("Matt", jobFont, brush, x + 204, y + 950);
			}
			else if (run.Celloglaze == RunCelloglazeOptions.VelvetBoth)
			{
				g.DrawString("Velvet", jobFont, brush, x + 204, y + 950);
				g.DrawString("Velvet", jobFont, brush, x + 360, y + 950);
			}
			else if (run.Celloglaze == RunCelloglazeOptions.VelvetFront)
			{
				g.DrawString("Velvet", jobFont, brush, x + 204, y + 950);
			}
			else if (run.Celloglaze == RunCelloglazeOptions.SpotUVFrontMattBoth)
			{
				g.DrawString("Matt", jobFont, brush, x + 204, y + 950);
				g.DrawString("Matt", jobFont, brush, x + 360, y + 950);
			}
			else if (run.Celloglaze == RunCelloglazeOptions.SpotUVFrontMattFront)
			{
				g.DrawString("Matt", jobFont, brush, x + 204, y + 950);
			}
		}

		public void SetupPrintProperties()
		{
			var printerName = PrinterSettings.PrinterName;
			if (printerName == "Microsoft XPS Document Writer")
			{
				PrinterSettings.PrintToFile = true;
				var folder = LepGlobal.Instance.DataDirectory.FullName;
				var fullPath = Path.Combine(folder,
					$"{DateTime.Now.Ticks}_JobRunSheet_{(run != null ? run.Id : DateTime.Now.Ticks)}.xps");
				PrinterSettings.PrintFileName = fullPath;
			} 

			else if(printerName == "Microsoft Print to PDF")
			{
				var folder = LepGlobal.Instance.DataDirectory.FullName;
				var filename = string.Format("{0}_{1}.pdf", DateTime.Now.Ticks, "JobSheet");
				var fullPath = Path.Combine(folder, filename);
				PrinterSettings.PrintFileName = fullPath;
			}

			for (var i = 0; i <= PrinterSettings.PaperSizes.Count - 1; i++)
			{
				if (PrinterSettings.PaperSizes[i].RawKind == (int)PaperKind.A3)
				{
					PrinterSettings.DefaultPageSettings.PaperSize = PrinterSettings.PaperSizes[i];
					break;
				}
			}

			PrinterSettings.DefaultPageSettings.Margins = new Margins(10, 10, 10, 10);
			PrinterSettings.DefaultPageSettings.Landscape = false;

			DefaultPageSettings.PrinterSettings = PrinterSettings;

			OriginAtMargins = false;
		}

		private String SanitizeString(String s)
		{
			String r;
			r = s.Replace("Double", "Dbl");
			r = r.Replace("Business Card", "BC");
			r = r.Replace("(90 x 54)", "");
			r = r.Replace("(184 x 54)", "");
			r = r.Replace("(90 x 110)", "");

			return r;
		}
	}
}
