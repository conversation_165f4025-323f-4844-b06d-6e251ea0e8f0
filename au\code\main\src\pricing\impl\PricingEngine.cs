using lep.configuration;
using lep.extensionmethods;
using lep.job;
using lep.job.impl;
using lep.freight;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using static lep.configuration.Configuration;
using static lep.job.JobTypeOptions;
using static lep.SiteLocation;
using NHibernate.Mapping;
using static System.Net.Mime.MediaTypeNames;
using System.Numerics;
using System.Reflection.Metadata;

namespace lep.pricing.impl
{
	public class PricingEngine : IPricingEngine
	{
		private IConfigurationApplication _configApp;
		private IJobApplication _jobApp;
		private IPricePointApplication _pricePointApp;
		private IPackageApplication _packApp;
		private dynamic _wiroValues;

		public PricingEngine(IPricePointApplication pricePointApp, IJobApplication jobApp, IConfigurationApplication configApp, IPackageApplication packApp, WiroMagazineValues wiroValues)
		{
			_pricePointApp = pricePointApp;
			_jobApp = jobApp;
			_configApp = configApp;
			_packApp = packApp;
			//save wiro values
			_wiroValues = wiroValues.GetWiroMagazineValues();
		}

		private static readonly HashSet<JobTypeOptions> TypesNotToConsiderForCustomSizePricing = new()
		{
			PresentationFolder,
			RemovableWallDecals, VinylStickerOutdoor,RigidSigns,
			PullUpBannerStandardStand, PullUpBannerPremiumStand,
			EnvelopeBlack,Envelope1Pms, Envelope2Pms, EnvelopeCmyk,
			DuplicateNCRBooks, TriplicateNCRBooks, QuadruplicateNCRBooks
		};

		#region IPricingApplication Members
		public (IPaperSize, bool) AdjustSize(IJob job, StringBuilder sb)
		{
			var size = job.FinishedSize.PaperSize;

			if (size.Name == "Business Card (90 x 55)")
			{
				size = _jobApp.GetPaperSize("Business Card (90 x 54)");
			}

			var sizeAdjust = false;
			if (size.Name == "Custom")
			{
				if (!TypesNotToConsiderForCustomSizePricing.Contains((JobTypeOptions)job.Template.Id))
				{
					if (job.FoldedSize != null)
					{
						sb.Append("Can not price Custom Trim with Fold Specified").AppendLine();
						return (size, sizeAdjust);
					}

					var smallestSizeThatllFitThisCustomSize = _pricePointApp.FindSmallestFit(job);
					if (smallestSizeThatllFitThisCustomSize != null)
					{
						size = smallestSizeThatllFitThisCustomSize;
						sizeAdjust = true;
						sb.AppendFormat("Considering {0} will accomodate custom trim", size.Name).AppendLine();
					}
				}
			}

			if (job.DieCutType == CutOptions.Custom &&
			job.Template.Is(PresentationFolder, PresentationFolderNDD, TentCalendars))
			{
				sb.Append("Can not price Presentation Folder with Custom Die cut").AppendLine();
				return (size, sizeAdjust);
			}

			return (size, sizeAdjust);
		}

		public IList<IPricePoint> GetPricePoints(IJob job)
		{
			//decimal temp = 0;

			//const string fmt = "{0,-30} {1,10:C}";

			// private delegate void AddStep(string s, decimal v ){
			//    sb.AppendFormat( "{0:-15} {0:C}", s, v ).AppendLine();
			//    stepsFollowed = sb.ToString();
			//};

			//decimal price = 0;
			//decimal extras = 0;
			var modQty = job.Quantity;

			var sb = new StringBuilder();
			var (size, sizeAdjust) = AdjustSize(job, sb);
			var list = _pricePointApp.FindPricePoints(job, size);
			return list;
		}

		/// <summary>
		/// Calculates the price of a job based on various parameters.
		/// </summary>
		/// <param name="job">The job to calculate the price for.</param>
		/// <param name="sb">A <see cref="StringBuilder"/> used for logging and storing steps followed.</param>
		/// <param name="modQty">The modified quantity of the job.</param>
		/// <param name="productpriceCode">The product price code to use for pricing adjustments.</param>
		/// <returns>The calculated price of the job.</returns>
		public decimal PriceJob(IJob job, StringBuilder sb, out int modQty, string productpriceCode)
		{
			decimal temp = 0;

			const string fmt = "{0,-30} {1,10:C}";

			// private delegate void AddStep(string s, decimal v ){
			//    sb.AppendFormat( "{0:-15} {0:C}", s, v ).AppendLine();
			//    stepsFollowed = sb.ToString();
			//};

			decimal price = 0;
			decimal extras = 0;
			modQty = job.Quantity;

			Log.Information(formatJobLog(job));

			// custom diecutting - requires quote
			if (job.DieCutType == CutOptions.Custom)
			{
				sb.AppendLine("Cannot price Custom DieCut ");
				return 0;
			}

			if (job.RoundOption == RoundOption.Custom)
			{
				sb.AppendLine("Cannot price Custom round ");
				return 0;
			}

			if (!string.IsNullOrEmpty(job.SpecialInstructions))
			{
				sb.AppendLine("Cannot price job with special instructions");
				return 0;
			}

			//CR27 For Brochure & Flyers, 'Custom Folding' will return unable to Quote
			if (job.IsBrochure() && job.FoldedSize?.PaperSize?.Name == "Custom")
			{
				Log.Information("Job {JobId} requires quote", job.Id);
				sb.AppendLine("Can not price Custom Fold ");
				return 0;
			}

			var (size, sizeAdjust) = AdjustSize(job, sb);



			var list = _pricePointApp.FindPricePoints(job, size);

			// no matches - requires quote
			if (list.Count == 0)
			{
				Log.Information(" {JobId} requires quote", job.Id);
				sb.AppendLine("No price points set up in the system");
				return 0;
			}
			// there is always at least one element in the list in following statements

			// calculate base price, can be 0

			// if (job.Template.Is(WiroMagazines))
			// {
			// 	//Can we please adopt the same price file for 80gsm, 90gsm and 100gsm Uncoated 
			// 	// Self Cover Saddle Stitched Digital Magazines, as the Text pricing for Wiro Bound Magazines.
			// 	var sepMagTemplate = _jobApp.GetJobTemplate(MagazineSeparate);
			// 	var price_page2 = 0m;
			// 	var price_page4 = 0m;

			// 	if ((job.Pages % 4) == 0) {// (8, 12, 16, ...){
			// 		price_page4 = BasePrice(sepMagTemplate, job.Stock, size, job.Quantity, job.PrintType, list, out modQty);
			// 		price = price_page4;
			// 	}
			// 	else if ((job.Pages % 2) == 0){ //(10, 14, 18, ...)
			// 		 price_page2 = BasePrice(sepMagTemplate, job.Stock, size, job.Quantity, job.PrintType, list, out modQty);

			// 	}



			// }
			// else
			price = BasePrice(job.Template, job.Stock, size, job.Quantity, job.PrintType, list, out modQty);


			if (price == 0)
			{
				sb.AppendLine("Not enough price points to deduct price");
				return 0;
			}
			else if (job.IsMagazine())
			{
				//modQty = job.Quantity;
				if (job.BindingOption == null)
				{
					sb.AppendFormat("Need Binding options").AppendLine();
					return 0;
				}
				if (job.BindingOption.FixedValue == -1 && job.BindingOption.PerThousandValue == -1)
				{
					return 0;
				}
				//Add fixed price for binding option
				sb.AppendFormat("raw magazine price  {0}", price).AppendLine();

				decimal magFixedPriceValue = job.BindingOption.FixedValue;
				sb.AppendFormat("Added fixed price of {0}", magFixedPriceValue).AppendLine();
				price = price + magFixedPriceValue;

				sb.AppendFormat("New price of {0}", price).AppendLine();
				//Calculate per Thousand value.
				//variables are used due to accessing multple times in sb and log.No advantage to using Math.DivRem
				decimal perThousandValue = job.BindingOption.PerThousandValue;
				var perThousandMultiplier = job.Quantity / 1000;
				if ((decimal)job.Quantity % (decimal)1000 != 0)
				{
					perThousandMultiplier = perThousandMultiplier + 1;
				}
				var perThousandTotal = perThousandValue * perThousandMultiplier;
				sb.AppendFormat("Added per thousand price of {0} per thousand for qty {1} ({2}) = {3}", perThousandValue,
					job.Quantity, perThousandMultiplier, perThousandTotal).AppendLine();
				price = price + perThousandTotal;
				sb.AppendFormat("New price of {0}", price).AppendLine();

				//Calculate Per Section value
				//variables are used due to accessing multple times in sb and log.No advantage to using Math.DivRem
				decimal perSectionValue = job.BindingOption.PerSectionValue;
				var perSectionMultiplier = job.Pages / 16;
				if ((decimal)job.Pages % (decimal)16 != 0)
				{
					perSectionMultiplier = perSectionMultiplier + 1;
				}
				var perSectionTotal = perSectionValue * perSectionMultiplier;
				sb.AppendFormat("Added per Section price of {0} for pages {1} ({2}) = {3}", perSectionValue, job.Pages,
					perSectionMultiplier, perSectionTotal).AppendLine();
				price = price + perSectionTotal;
				sb.AppendFormat("New price of {0}", price).AppendLine();
			}

			sb.AppendFormat("Quantity rounded to  {0}", modQty).AppendLine();
			var priceBase = price;
			//Log.Information(job.JobNr + " Price base $" + priceBase);
			sb.AppendFormat(fmt, "Price base", priceBase).AppendLine();

			decimal priceAdjusted = 0;
			if (!string.IsNullOrEmpty(productpriceCode))
			{
				sb.AppendLine($"Cust product price code used = '{productpriceCode}'");
			}

			var priceMargin = _pricePointApp.GetPriceMargin(productpriceCode, job.Template.Id);
			var priceMarginValue = priceBase * priceMargin / 100;
			if (priceMargin != 0)
			{
				sb.AppendFormat("Cust margin %{0}", priceMargin).AppendLine();
				//Log.Information(job.JobNr + " Cust margin %" + priceMargin);
				sb.AppendFormat(fmt, "Cust margin", priceMarginValue).AppendLine();
				//Log.Information(job.JobNr + " Cust margin $" + priceMarginValue);

				priceAdjusted = priceBase + priceMarginValue;
				price = priceAdjusted;
			}
			job.PriceBase = String.Format("{0:C0}", priceBase);
			job.PriceMargin = priceMargin;
			job.PriceMarginValue = String.Format("{0:C0}", priceMarginValue);
			job.ProductPriceCode = productpriceCode;
			var siteLoc = job.Order.Customer != null ? job.Order.Customer.SiteLocation : SiteLocation;
			var printTyp = job.PrintType;

			#region if this is a magazine filter the list of pricepoints based on job.Pages

			// this step could incur an extras charge
			if (job.IsMagazine())
			{
				// ignore extra page price
				//extras = AdjustListForMagazine( job,list );

				if (!job.SelfCovered)
				{
					IPaperSize coversize = null;
					// need to calculate cover cost
					if (size.Name.Equals("A4")) // (job.FinishedSize.PaperSize.Name.Equals("A4"))
					{
						coversize = _jobApp.GetPaperSize("A3");
					}
					else if (size.Name.Is("A5", "A6", "DL")
					//  job.FinishedSize.PaperSize.Name.Is("A5", "A6", "DL")
					//||job.FinishedSize.PaperSize.Name.Equals("A5") ||job.FinishedSize.PaperSize.Name.Equals("A6") ||job.FinishedSize.PaperSize.Name.Equals("DL")
					)


					{
						coversize = _jobApp.GetPaperSize("A4");
					}
					else
					{
						// need quote
						sb.AppendLine("Can not price - Magazine/Magazine Separate - Trim=A4, Fold not A5/A6/DL");
						return 0;
					}

					var template = _jobApp.GetJobTemplate(MagazineCover);
					var numcolour = template.ColourSide(job.FrontPrinting, job.BackPrinting);

					var frontcello = job.FinalFrontCelloglaze;
					var backcello = job.FinalBackCelloglaze;
					if (job.Celloglaze.ToDescription().Contains("SpotUV") && job.IsMagazineSeparate())
					{
						frontcello = JobCelloglazeOptions.Matt;
						backcello = JobCelloglazeOptions.None;
					}


					var coverlist = _pricePointApp.FindPricePoints(siteLoc, printTyp, template, job.StockForCover,
						coversize, numcolour, frontcello, backcello, 0);
					if (coverlist.Count == 0)
					{
						sb.AppendLine("Can not price separate cover");
						sb.AppendLine("No price points setup for cover");
						return 0;
					}
					var coverbase = BasePrice(template, job.StockForCover, coversize, job.Quantity, job.PrintType,
						coverlist, out modQty);
					if (coverbase == 0)
					{
						//Log.Information(job.JobNr + " separate cover requires quote");
						sb.AppendLine("Can not price Separate cover");
						sb.AppendLine("Not enough price points to deduct cover price");
						return 0;
					}

					temp = coverbase + Convert.ToDecimal(_configApp.GetValue(siteLoc == AU ? PriceMagazineCover : NZPriceMagazineCover));
					extras += temp;

					//Log.Information(job.JobNr + " cover $" + coverbase + " + $40");
					sb.AppendFormat(fmt, "Separate Cover Cost", temp).AppendLine();
				}

				//folding cost
				if (job.FoldedSize != null &&
					(job.FoldedSize.PaperSize.Name.Contains("Crash")))
				{
					temp =
						Convert.ToDecimal(_configApp.GetValue(siteLoc == AU ? PriceCrashFoldingBase : NZPriceCrashFoldingBase));
					extras += temp;
					sb.AppendFormat(fmt, "Self Cover Crash fold setup", temp).AppendLine();

					temp = Math.Ceiling((decimal)(job.Quantity / 1000.0)) *
							Convert.ToDecimal(_configApp.GetValue(siteLoc == AU ? PriceCrashFolding : NZPriceCrashFolding));
					extras += temp;
					sb.AppendFormat(fmt, "Self Cover Cover Crash fold/1000", temp).AppendLine();
				}
			}

			// Check if the job is a wiro magazine and wiro info is set
			if (job.Template.Is(WiroMagazines) && job.WiroInfo != null)
			{
				var w = job.WiroInfo;

				// Calculate the cost for the outer front cover if it exists
				if (w.OuterFront != null)
				{
					var outerCover = _wiroValues.OuterCover[w.OuterFront];
					decimal pricePerCover = outerCover?.Price ?? 0.0m;
					extras += pricePerCover * job.Quantity; // Add the cost to the extras
				}

				// Calculate the cost for the outer back cover if it exists
				if (w.OuterBack != null)
				{
					var outerCover = _wiroValues.OuterCover[w.OuterBack]; 
					decimal pricePerCover = outerCover?.Price ?? 0.0m;
					extras += pricePerCover * job.Quantity;
				}

				var innerCoverStocks = (_wiroValues.InnerCover as IEnumerable<dynamic>);

				// Calculate the cost for the inner front cover if it exists
				if (w.InnerFrontStockForCover != null)
				{
					decimal coverPrice = innerCoverStocks.FirstOrDefault(x => x.Name == w.InnerFrontStockForCover.Name)?.UnitPrice[size.Name] ?? 0.0m;
					extras += coverPrice * job.Quantity;

					// Calculate the cost for the inner front cello if it exists
					if (w.InnerFrontCello != null)
					{
						decimal celloPrice = _wiroValues.InnerCoverCelloPrices[w.InnerFrontCello] ?? 0.0m;
						extras += celloPrice * job.Quantity;
					}
				}

				// Calculate the cost for the inner back cover if it exists
				if (w.InnerBackStockForCover != null)
				{
					decimal coverPrice = innerCoverStocks.FirstOrDefault(x => x.Name == w.InnerBackStockForCover.Name)?.UnitPrice[size.Name] ?? 0.0m;
					extras += coverPrice * job.Quantity; 

					// Calculate the cost for the inner back cello if it exists
					if (w.InnerBackCello != null)
					{
						decimal celloPrice = _wiroValues.InnerCoverCelloPrices[w.InnerBackCello] ?? 0.0m;
						extras += celloPrice * job.Quantity; 
					}
				}

				var singleJobsDetails = _packApp.GetSingleJobThicknessWeight(job);
				var depth = singleJobsDetails.depthOf1Job;
				var wiroCoils = (_wiroValues.WiroCoils as IEnumerable<dynamic>);

				// Find the coil with smallest MaxBookThickness that can accommodate depth
				var coil = wiroCoils.FirstOrDefault(x => x.MaxBookThickness >= depth);
				if (coil == null)
				{
					job.WiroInfo.CoilThickness = -1;
					sb.AppendLine("Cannot price - Wiro Coil not found");
					return 0;
				}
				else
				{
					job.WiroInfo.CoilThickness = (decimal)coil.Thickness;
					var coilPrice = (decimal)coil.Price.Value;
					extras += (decimal)coil.Price.Value * job.Quantity; // Add the cost to the extras
					sb.AppendLine($"{job.WiroInfo.CoilThickness}mm Wiro Coil price {coilPrice}");
				}
			}



			#endregion if this is a magazine filter the list of pricepoints based on job.Pages

			// job extras

			#region Job extras

			if (job.NumberOfMagnets > 0)
			{
				temp = modQty *
					   Convert.ToDecimal(
						   _configApp.GetValue(siteLoc == AU
							   ? PriceMagnet
							   : NZPriceMagnet));
				extras += temp * job.NumberOfMagnets;
				sb.AppendFormat(fmt, job.NumberOfMagnets + " Magnet ", temp).AppendLine();
			}

			if (job.SendSamples)
			{
				temp = Convert.ToDecimal(_configApp.GetValue(PriceSamples));
				extras += temp;
				sb.AppendFormat(fmt, "Samples ", temp).AppendLine();
			}

			var modQtyPerThousand = Math.Ceiling((decimal)(modQty / 1000.0));
			var priceRound = Convert.ToDecimal(_configApp.GetValue(siteLoc == AU
						? PriceRoundOption : NZPriceRoundOption));
			var priceRoundDieCut = Convert.ToDecimal(_configApp.GetValue(siteLoc == AU
						? PriceRoundDieCutOption : NZPriceRoundDieCutOption));

			decimal priceHoleDrilling;

			if (job.IsMagazine() || job.Template.Id == (int)MagazineCover)
			{
				priceHoleDrilling = Convert.ToDecimal(_configApp.GetValue(PriceHoleDrillingMagazine));
				if (job.NumberOfHoles > 1)
					for (int c = 2; c <= job.NumberOfHoles; c++)
						priceHoleDrilling += 2;
			}
			else
			{
				priceHoleDrilling = Convert.ToDecimal(_configApp.GetValue(PriceHoleDrilling));
			}

			var priceSizeAdjustment = Convert.ToDecimal(_configApp.GetValue(siteLoc == AU ? PriceBCSizeAdjustment
						: NZPriceBCSizeAdjustment));

			if (job.RoundOption != RoundOption.None)
			{
				temp = modQtyPerThousand * (job.RoundOption == RoundOption.DieCut ? priceRoundDieCut : priceRound);
				extras += temp;
				sb.AppendFormat(fmt, "Round option", temp).AppendLine();
			}

			if (job.HoleDrilling != HoleDrilling.None)
			{
				temp = modQtyPerThousand * priceHoleDrilling;
				extras += temp;
				sb.AppendFormat(fmt, "Hole Drilling", extras).AppendLine();
			}

			//Size Adjust is just for BC
			if (job.IsBusinessCard() && sizeAdjust)
			{
				temp = modQtyPerThousand * priceSizeAdjustment;
				extras += temp;
				sb.AppendFormat(fmt, "BC size adjust", temp).AppendLine();
			}


			if (job.IsNCRBook() && job.NCRInfo.Sheets.Any(x => x.Value.PrintedOnBack))
			{

				temp = modQtyPerThousand * 55.0m; // 55 $ setup per thousand
				extras += temp;
				sb.AppendFormat(fmt, "NCR Book Setup ($55 per thousand) = ", temp).AppendLine();

				temp = modQty * 2.0m; // per book
				extras += temp;

				sb.AppendFormat(fmt, "NCR book (per book $2) = ", temp).AppendLine();
			}


			if (job.IsNCRBook() && job.NCRInfo.Sheets.Any(x => x.Value.PrintedOnBack)) {
			
				temp = modQtyPerThousand * 55.0m; // 55 $ setup per thousand
				extras += temp;
				sb.AppendFormat(fmt, "NCR Book Setup ($55 per thousand) = ", temp).AppendLine();

				temp = modQty * 2.0m; // per book
				extras += temp;

				sb.AppendFormat(fmt, "NCR book (per book $2) = ", temp).AppendLine();
			}

			#endregion Job extras

			#region CR27 For Brochure & Flyers, For Folding, Scoring, Perforating Add Setup and per 1000 extra charges

			if (job.IsBrochure())
			{
				if (job.FinishedSize.PaperSize.Name == "8pp A4" || job.FinishedSize.PaperSize.Name == "8pp DL")
				{
					if (job.FoldedSize != null && job.FoldedSize.PaperSize.Name.Contains("Gate"))
					{
						sb.AppendLine("Can not price - 8pp A4, 8pp DL to Gate folding");
						return 0;
					}
				}

				if (job.FoldedSize != null)
				{
					// "Dont fold" comes as null
					temp =
						Convert.ToDecimal(
							_configApp.GetValue(siteLoc == AU
								? PriceDataFoldingSetup
								: NZPriceDataFoldingSetup));
					extras += temp;
					sb.AppendFormat(fmt, "Brochure  Fold Setup", temp).AppendLine();

					temp = modQtyPerThousand *
						   Convert.ToDecimal(
							   _configApp.GetValue(siteLoc == AU
								   ? PriceDataFoldingPer1000
								   : NZPriceDataFoldingPer1000));
					extras += temp;
					sb.AppendFormat(fmt, "Brochure Fold/1000", temp).AppendLine();
				}

				if (job.Perforating)
				{
					temp = Convert.ToDecimal(_configApp.GetValue(siteLoc == AU ? PriceDataPerforatingSetup : NZPriceDataPerforatingSetup));
					extras += temp;
					sb.AppendFormat(fmt, "Brochure Perforating Setup", temp).AppendLine();

					temp = modQtyPerThousand * Convert.ToDecimal(_configApp.GetValue(siteLoc == AU ? PriceDataPerforatingPer1000 : NZPriceDataPerforatingPer1000));
					extras += temp;
					sb.AppendFormat(fmt, "Brochure Perforating/1000", temp).AppendLine();
				}


			}
			if (job.IsBusinessCard() || job.IsBrochure())
			{
				if (job.Scoring)
				{
					temp =
						Convert.ToDecimal(
							_configApp.GetValue(siteLoc == AU
								? PriceDataScoringSetup
								: NZPriceDataScoringSetup));
					extras += temp;
					sb.AppendFormat(fmt, "Scoring Setup", temp).AppendLine();

					temp = modQtyPerThousand *
						   Convert.ToDecimal(
							   _configApp.GetValue(siteLoc == AU
								   ? PriceDataScoringPer1000
								   : NZPriceDataScoringPer1000));
					extras += temp;
					sb.AppendFormat(fmt, "Scoring/1000", temp).AppendLine();
				}
			}

			/*
			if (job.Template.Id == (int)Brochure)
			{
				var bundlesOf = job.BrochureDistPackInfo?.BundlesOf ?? 0;
				if (bundlesOf > 0)
				{
					var bundles = Math.Ceiling((modQty / (decimal)bundlesOf));
					temp = bundles * 2;
					extras += temp;
					sb.AppendFormat(fmt, bundles + " bundles of " + bundlesOf, temp).AppendLine();
				}
			}*/


			if (job.IsBrochure() &&
				(job.BrochureDistPackInfo?.MailHouse?.Id > 0
				|| job.BrochureDistPackInfo?.PackingInstruction == PackingInstruction.Banding
				|| job.BrochureDistPackInfo?.PackingInstruction == PackingInstruction.ShrinkWrapping
				)
				)
			{
				var jd = _packApp.GetSingleJobThicknessWeight(job);
				var bundlesOf = _packApp.GetInBundlesOf(job, jd, job.BrochureDistPackInfo);
				if (bundlesOf > 0)
				{
					var bundleCount = Math.Ceiling((modQty / (decimal)bundlesOf));
					temp = bundleCount;
					extras += temp;
					sb.AppendFormat(fmt, bundleCount + " bundles of " + bundlesOf, temp).AppendLine();
				}
			}






			if (job.Template.Id == (int)BrochureNDD && job.FoldedSize != null)
			{
				//(Michael Greenan) Because job.price has not actually been populated yet, need to use price variable, or job.PriceBase, must be last extras.
				temp = Convert.ToDecimal(price + extras) * Convert.ToDecimal(0.10);
				extras += temp;
				sb.AppendFormat(fmt, "NDD Folding surcharge ", temp).AppendLine();
			}

			// LORD-1231
			if (job.Celloglaze.ToDescription().Contains("SpotUV")
				&& (job.IsPresentationFolder() || job.IsMagazineSeparate()))
			{

				var spotUVCharge = 300M;  // Start with basic setup charge of $300 SpotUV
				spotUVCharge += 200M;     // Add $200 for the first upto 1000			


				var sheets = modQty;  // if presentation folder

				if (job.IsMagazineSeparate())
					sheets = modQty / 2;

				var countOf1000Sheets = Math.Ceiling(sheets / 1000.0M);

				// for every extra 1000 sheets over first 1000
				for (var i = 2; i <= countOf1000Sheets; i++)
					spotUVCharge += 200; //add $200

				spotUVCharge = spotUVCharge * 1.1M;
				sb.AppendFormat(fmt, "SpotUV charge ", spotUVCharge).AppendLine();
				extras += spotUVCharge * 1.1M;
			}

			price += extras;

			#endregion CR27 For Brochure & Flyers, For Folding, Scoring, Perforating Add Setup and per 1000 extra charges

			//Log.Information("Job {job} final price {price}", job.JobNr, price);
			//sb.AppendFormat(fmt, "Total Final Price", price).AppendLine();

			if (job.Id == 0)
			{
				//Log.Information("{event} {$job} {price}", "PriceCheck", job, price);
			}
			price = Math.Ceiling(price);





			if (job.Template.Is(Magazine, MagazineNDD, MagazineSeparate) && price > 2500)
				return 0;

			if (price > 3000)
				return 0;
			return price;
		}

		//public string FindMYOB(IJob job, int qty)
		//{
		//	var list = _pricePointApp.(job, job.FinishedSize.PaperSize);
		//	var myob = string.Empty;

		//	#region LORD-544

		//	// If the qty of Business cards (Std, SDD, NDD) >=1000 use the 1000 MYOB code.
		//	// Otherwise stepup, e.g.KwikKopy 300 would go to 500 code 200 would go to 250.
		//	if (job.Template.Is(BusinessCard, BusinessCardNdd, BusinessCardSdd) && qty >= 1000)
		//	{
		//		qty = 1000;
		//	}

		//	#endregion LORD-544

		//	if (list.Any(pp => pp.Quantity == qty))
		//	{
		//		myob = list.FirstOrDefault(pp => pp.Quantity == qty)?.MYOB ?? "";
		//	}
		//	else
		//	{
		//		myob = _pricePointApp.FindProductMYOB(job, job.FinishedSize.PaperSize)?.MYOB1 ?? "";
		//	}
		//	return myob;
		//}

		#endregion IPricingApplication Members

		protected decimal BasePrice(IJobTemplate template, IStock stock, IPaperSize size,
			int Quantity, PrintType printType, IList<IPricePoint> list, out int modQty)
		{
			modQty = Quantity;
			//// look for exact match, there may be more than 1 object in the list
			//var point = FindMatchingQuantity(list, Quantity);
			//if (point != null)
			//{
			//	return Round5(point.Price);
			//}

			IJobOptionSpecQuantity specQ = null;
			//foreach (var sto in from st in template.SizeOptions
			//					where st.PaperSize == size
			//					from sto in st.StockOptions//TODO: MG was if (sto.Stock == stock)
			//					where sto.Stock == stock && sto.PrintType == printType
			//					select sto)
			//{
			//	specQ = sto.QuantityOption;
			//}

			specQ = (from st in _jobApp.ListSizeOptions(template)
					 where st.PaperSize.Id == size.Id
					 from sto in st.StockOptions
					 where sto.Stock.Id == stock.Id && sto.PrintType == printType
					 select sto.QuantityOption).FirstOrDefault();

			if (specQ == null)
			{
				return 0;
			}

			// Find the max and min qty allowed by the range for this job
			var allowedQtys = QuantitiesByTemplate.Get((JobTypeOptions)template.Id, specQ);
			var maxQty = allowedQtys.Max();
			var minQty = allowedQtys.Min();

			if (specQ.Change != 0 && specQ.Change2 == 0)
			{
				specQ.Change2 = maxQty;
			}
			else if (specQ.Change == 0 && specQ.Change2 == 0)
			{
				specQ.Change = maxQty;
			}

			// then ignore the pricepoints in DB that are not demanded by the range
			list = list.Where(_ => _.Quantity <= maxQty && _.Quantity >= minQty).ToList();

			return FindPrice(list, Quantity, specQ, out modQty);

			/*

			// only calculate prices from allowable quantities, so quantities do not jump
			// this has been done so system can cope with wrong price data
			var allowableQuantities = QuantitiesByTemplate.Get((JobTypeOptions)template.Id, specQ);
			var q2 = allowableQuantities.FirstOrDefault(x => x >= Quantity);

			var list2 = list.Where(x => allowableQuantities.Contains(x.Quantity)).ToList();

			return FindPrice(list, q2, specQ, out modQty);
			*/
		}

		protected decimal FindPrice(IList<IPricePoint> list, int qty, IJobOptionSpecQuantity specQ, out int modQty)
		{
			modQty = qty;
			try
			{
				//check if enough data
				if (list.Count < 1)
				{
					return 0;
				}

				IPricePoint point = null;

				// else falls between two entries - find the nearest upper and lower pricepoint
				var lower = list[0];
				var upper = list[list.Count - 1];

				foreach (var p in list)
				{
					if (p.Quantity > lower.Quantity && p.Quantity <= qty)
					{
						lower = p;
					}
					if (p.Quantity < upper.Quantity && p.Quantity >= qty)
					{
						upper = p;
					}
				}

				////check if it's valid points
				//if (specQ.Change > 0)
				//{
				//	//possible 2 step
				//	if (lower.Quantity < specQ.Change)
				//	{
				//		//it's step 1
				//		if (upper.Quantity > specQ.Change)
				//		{
				//			return 0;
				//		}
				//	}
				//}


				//check if special case
				if (specQ.Step1 == 0 && qty <= specQ.Change)
				{
					if (lower.Quantity >= qty)
					{
						modQty = lower.Quantity;
						return Round5(lower.Price);
					}
					else if (upper.Quantity >= qty)
					{
						modQty = upper.Quantity;
						return Round5(upper.Price);
					}
					else
					{
						return 0;
					}
				}
				else
				//check if special case
				if (specQ.Step2 == 0 && qty >= specQ.Change)
				{
					if (lower.Quantity >= qty)
					{
						modQty = lower.Quantity;
						return Round5(lower.Price);
					}
					else if (upper.Quantity >= qty)
					{
						modQty = upper.Quantity;
						return Round5(upper.Price);
					}
					else
					{
						return 0;
					}
				}


				//find it's price 1 or price 2
				var adjustedQuantity = FindAdjustedQuantity(qty, specQ, FindMinQuantity(list));
				modQty = adjustedQuantity;

				//var adjustedQuantity = qty;

				point = FindMatchingQuantity(list, adjustedQuantity);
				if (point != null)
				{
					return Round5(point.Price);
				}

				// interpolate the base price (arrange operations to minimize rounding)
				return
					Round5(lower.Price +
						   (upper.Price - lower.Price) * (adjustedQuantity - lower.Quantity) /
						   (1.0M * (upper.Quantity - lower.Quantity)));
			}
			catch (Exception ex)
			{
				var msg = ex.Message;
				return 0;
			}
		}

		private decimal Round5(decimal price)
		{
			// round up to nearest full $5
			//return price = Math.Ceiling( price / ((decimal) 5.00) ) * 5;

			//Round up to the nearest dollar
			return price = Math.Round(price, MidpointRounding.AwayFromZero);
		}

		private int FindAdjustedQuantity(int quantity, IJobOptionSpecQuantity specQ, int minquantity)
		{
			var step = specQ.Step1;
			var basequantity = minquantity;
			if (quantity <= minquantity)
			{
				return minquantity;
			}
			if (specQ.Change > 0)
			{
				if (quantity > specQ.Change)
				{
					step = specQ.Step2;
					basequantity = specQ.Change;
				}
			}

			return basequantity + (int)(Math.Ceiling((quantity - basequantity) * 1.0 / step) * step);
		}

		/// <summary>
		/// Search for exact quantity match in the list.
		/// </summary>
		/// <returns></returns>
		protected IPricePoint FindMatchingQuantity(IList<IPricePoint> list, int qty)
		{
			//foreach (IPricePoint point in list) {
			//    if (point.Quantity == qty) {
			//        return point;
			//    }
			//}
			//return null;

			return list.FirstOrDefault(point => point.Quantity == qty);
		}

		private IDictionary<int, decimal> ExtractPrice(IJobOptionSpecQuantity specQ, IDictionary<int, decimal> prices)
		{
			IDictionary<int, decimal> tmp = new SortedDictionary<int, decimal>();

			Action<int> put = q =>
			{
				if (!tmp.ContainsKey(q)) tmp.Add(q, 0);
			};

			if (!prices.ContainsKey(specQ.Minium))
			{
				//if (!tmp.ContainsKey( specQ.Minium ))
				//    tmp.Add( specQ.Minium, 0 );
				put(specQ.Minium);
			}

			if (specQ.Change > 0 && !prices.ContainsKey(specQ.Change))
			{
				//if (!tmp.ContainsKey( specQ.Change ))
				//    tmp.Add( specQ.Change, 0 );
				put(specQ.Change);
			}

			if (specQ.Change2 > 0 && !prices.ContainsKey(specQ.Change2))
			{
				//if (!tmp.ContainsKey( specQ.Change2 ))
				//    tmp.Add( specQ.Change2, 0 );
				put(specQ.Change2);
			}

			if (specQ.Step1 > 0)
			{
				//todo find if any price point in saved contains step1 data, if not
				//if (!tmp.ContainsKey( specQ.Minium + specQ.Step1 ))
				//    tmp.Add( specQ.Minium + specQ.Step1, 0 );
				put(specQ.Minium + specQ.Step1);
			}

			if (specQ.Step2 > 0)
			{
				//todo find if any price point in saved contains step1 data, if not
				//if (!tmp.ContainsKey( specQ.Change2 + specQ.Step2 ))
				//    tmp.Add( specQ.Change2 + specQ.Step2, 0 );
				put(specQ.Change2 + specQ.Step2);
			}

			foreach (var s in prices)
			{
				if (!tmp.ContainsKey(s.Key))
				{
					tmp.Add(s);
				}
			}

			return tmp;
		}

		private int FindMinQuantity(IList<IPricePoint> priceList)
		{
			var minquantity = 0;
			foreach (var price in priceList)
			{
				if (minquantity == 0 || minquantity > price.Quantity)
				{
					minquantity = price.Quantity;
				}
			}
			return minquantity;
		}

		private int FindMaxQuantity(IList<IPricePoint> priceList, IJobOptionSpecQuantity quantity)
		{
			var maxquantity = 0;
			if (priceList.Count > 0)
			{
				maxquantity = priceList[priceList.Count - 1].Quantity;
			}
			else
			{
				//no price data, generate default quantity list
				if (quantity.Change2 > 0)
				{
					maxquantity = quantity.Change2;
				}
				else if (quantity.Change > 0)
				{
					maxquantity = quantity.Change;
				}
				else if (quantity.Step1 > 0)
				{
					maxquantity = quantity.Step1;
				}
			}

			return maxquantity;
		}

		private string formatJobLog(IJob job)
		{
			var sb = new StringBuilder("Job");
			sb.Append(job.JobNr).Append(":");
			sb.Append(job.Template.Name).Append(",");
			sb.Append(job.FinishedSize.PaperSize.Name).Append(",");
			sb.Append(job.FinalStock.Name).Append(",");
			sb.Append(Enum.GetName(Type.GetType("lep.job.JobPrintOptions"), job.FrontPrinting)).Append("-");
			sb.Append(Enum.GetName(Type.GetType("lep.job.JobCelloglazeOptions"), job.FinalFrontCelloglaze)).Append(",");
			sb.Append(Enum.GetName(Type.GetType("lep.job.JobPrintOptions"), job.BackPrinting)).Append("-");
			sb.Append(Enum.GetName(Type.GetType("lep.job.JobCelloglazeOptions"), job.FinalBackCelloglaze)).Append(",");
			sb.Append(job.Quantity);
			if (job.IsMagazine())
			{
				sb.Append(",").Append(job.Pages).Append(" pages");
				if (job.IsMagazineSeparate())
				{
					sb.Append(",");
					if (job.StockForCover != null)
					{
						sb.Append(job.StockForCover.Name);
					}
					else
					{
						sb.Append(" stock-unknown");
					}
					sb.Append(" cover");
				}
			}
			return sb.ToString();
		}


		public virtual SiteLocation SiteLocation { set; private get; }

		public virtual IPricePointApplication PricePointApplication
		{
			set { _pricePointApp = value; }
		}

		public virtual IJobApplication JobApplication
		{
			set { _jobApp = value; }
		}

	}
}
