using Newtonsoft.Json;

namespace lep.freight.impl
{
	using lep.courier;
	using lep.job;
	using System;
	using System.Collections.Generic;

	[Serializable]
	public class PackDetail : IPackDetail
	{
		public PackDetail()
		{
			FGCourier = new CourierType();
			PMCourier = new CourierType();
		}

		public virtual decimal? Price { get; set; }
		public virtual bool IsCustom { get; set; }
		public virtual bool IsCustomPackages { get; set; }

		public virtual string PackLog(Facility? facility)
		{
			var r = GetPackages(facility);
			var s = r.ToString();
			return s;
		}

		public virtual CourierType FGCourier { get; set; }
		public virtual string FGPackingSpecification { get; set; }
		public virtual bool IsFGCourierCustom { get; set; }

		public virtual CourierType PMCourier { get; set; }
		public virtual string PMPackingSpecification { get; set; }
		public virtual bool IsPMCourierCustom { get; set; }

		public virtual ListOfPackages FGPackageJson { get; set; } = new ListOfPackages();
		public virtual ListOfPackages PMPackageJson { get; set; } = new ListOfPackages();

		public virtual IList<Package> GetPackages(Facility? facility)
		{
			// if facility specified then return that facilities packages
			if (facility.HasValue)
			{
				return facility == Facility.FG ? FGPackageJson : PMPackageJson;
			}

			// else return both

			var tmp = new ListOfPackages();
			if (FGPackageJson != null)
				tmp.AddRange(FGPackageJson);

			if (PMPackageJson != null)
				tmp.AddRange(PMPackageJson);
			return tmp;
		}

		public virtual void SetPackages(IList<Package> items, Facility facility)
		{
			if (items == null)
			{
				items = new List<Package>();
			};

			if (facility == Facility.FG)
			{
				FGPackageJson = new ListOfPackages();
				FGPackageJson.AddRange(items);
			}
			else
			{
				PMPackageJson = new ListOfPackages();
				PMPackageJson.AddRange(items);
			}
		}

		public override bool Equals(object obj)
		{
			if (obj is PackDetail other)
			{
				decimal thisPrice = 0;

				if (this.Price != null)
					thisPrice = Decimal.Round(this.Price.Value, 2);

				decimal otherPrice = 0;

				if (other.Price != null)
					otherPrice = Decimal.Round(other.Price.Value, 2);

				bool v = IsCustom == other.IsCustom &&
					   IsCustomPackages == other.IsCustomPackages &&
					   FGCourier.Equals(other.FGCourier) &&
					   PMCourier.Equals(other.PMCourier) &&
					   FGPackingSpecification == other.FGPackingSpecification &&
					   PMPackingSpecification == other.PMPackingSpecification &&
					   IsFGCourierCustom == other.IsFGCourierCustom &&
					   IsPMCourierCustom == other.IsPMCourierCustom &&
						(thisPrice == otherPrice);

				var v1 = ((FGPackageJson == null && other.FGPackageJson == null) || (FGPackageJson.Equals(other.FGPackageJson)));
				var v2 = ((PMPackageJson == null && other.PMPackageJson == null) || (PMPackageJson.Equals(other.PMPackageJson)));
				return v

;
			}
			return false;
		}

		public override string ToString()
		{
			return JsonConvert.SerializeObject(this, Formatting.Indented) + "\n";
		}
	}
}
