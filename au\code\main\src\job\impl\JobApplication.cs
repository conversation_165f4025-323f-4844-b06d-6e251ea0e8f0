using lep.configuration;
using lep.email;
using lep.extensionmethods;
using lep.job.csv;
using lep.job.printing;
using lep.run;
using lep.security;
using lep.user;

using Serilog;
using lumen.csv;

//using Microsoft.WindowsAPICodePack.Shell;
using NHibernate;
using NHibernate.Transform;
using NHibernate.Criterion;
using NHibernate.Linq;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.Drawing.Printing;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Xml.Linq;
using System.Xml.XPath;
using static lep.job.Facility;
using static lep.job.JobCelloglazeOptions;
using static lep.job.JobTypeOptions;
using static lep.job.JobStatusOptions;
using static lep.job.PrintType;
using static NHibernate.Criterion.Restrictions;
//using Microsoft.Extensions.FileProviders;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;
using Ghostscript.NET.Rasterizer;
using Ghostscript.NET;

namespace lep.job.impl
{
	/// <summary>
	///
	/// </summary>
	public class JobApplication : BaseApplication, IJobApplication
	{
		// private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);
		//private static readonly ILog Log = LogManager.GetLogger("lep.job.impl.JobApplication.Import");

		private string tickImagePath = "~/app_themes/admin/images/tick.gif";

		// public LabelPrinterApplication LabelPrinter
		// { get; set; }

		public JobApplication(ISession sf, ISecurityApplication _securityApp,
			IConfigurationApplication configApp, LepArtworkScriptFiles lepArtworkScriptFiles, IEmailApplication mailapp) : base(sf, _securityApp)
		{
			_configApp = configApp;
			LepArtworkScriptFiles = lepArtworkScriptFiles;
			_emailApp = mailapp;
		}
		private IEmailApplication _emailApp;
		private LepArtworkScriptFiles LepArtworkScriptFiles { get; set; }
		private IConfigurationApplication _configApp { set; get; }

		public bool IsFacilityAvailable(Facility facility, IJob job)
		{
			// Start off by assuming that the job can be produced in facility
			bool result = true;

			// Forest glen can do all jobs
			if (facility == FG)
			{
				//both the stock and template has to be marked at producable at FG the tables
				result = result && job.Template.FG_Production && job.FinalStock.FG_Production;
			}
			else if (facility == PM)
			{
				// Redirect all NSW Post Code Magazines to FG Production Facility
				if (job.IsMagazine() && job.Order.DeliveryAddress.State == "NSW")
				{
					return false;
				}

				if (job.IsWideFormat())
				{
					return false;
				}


				//if (job.Template.Is(Envelope1PmsBlack, Envelope2Pms, EnvelopeCmyk))
				//{
				//	return false;
				//}

				// if PM the stock and template has to be marked at producable in the tables
				result = result && job.Template.PM_Production && job.FinalStock.PM_Production;

				if (job.PrintType == D)
				{
					result = result && true;
				}

				if (job.IsBusinessCard() && job.FinalFrontCelloglaze.Is(SpotUV, Foil, EmbossFoil, SpotUVFrontMattFront))
				{
					return false;
				}

				// if PM and SpotUV, Foil, EmbossFoil then cant be produced
				if (job.FinalFrontCelloglaze.Is(Foil, EmbossFoil)) //SpotUV, Foil, EmbossFoil, SpotUVFrontMattFront
				{
					result = false;
				}

			}

			return result;
		}

		public IJob GetJob(int Id)
		{
			//AssertPermission("job.read");
			return Session.Get<IJob>(Id);
		}

		public IJob Refresh(IJob job)
		{
			var id = job.Id;
			Session.Evict(job);
			return Session.Get<IJob>(id);
		}

		public IPaperSize GetPaperSize(int id)
		{
			return Session.Get<IPaperSize>(id);
		}

		public IPaperSize GetPaperSize(string name)
		{
			return Session.CreateCriteria(typeof(IPaperSize))
				.Add(Eq("Name", name))
				.SetCacheable(true)
				.UniqueResult<IPaperSize>();
		}

		public IStock GetStock(int id)
		{
			return Get<IStock>(id);
		}

		public IStock GetStock(string name)
		{
			return Session.CreateCriteria(typeof(IStock))
				.Add(Eq("Name", name))
				.AddOrder(Order.Asc("Id"))
				.SetCacheable(true).List<IStock>().FirstOrDefault();
			//.UniqueResult<IStock>();
		}

		public IBindingOption GetBindingOption(int id)
		{
			return Get<IBindingOption>(id);
		}

		public IBindingOption GetBindingOption(string name)
		{
			return Session.CreateCriteria(typeof(IBindingOption))
				.Add(Eq("Name", name))
				.SetCacheable(true)
				.UniqueResult<IBindingOption>();
		}

		public IJobOptionSpecStock GetSpecStock(int id)
		{
			return Get<IJobOptionSpecStock>(id);
		}

		public void Save(IJobTemplate template)
		{
			Save<IJobTemplate>(template);
		}

		public void Save(IJob job)
		{
			//AssertPermission("job.update");
			Save<IJob>(job);
		}

		public IJob SaveAndRefreshJob(IJob job)
		{
			//AssertPermission("job.update");
			Session.SaveOrUpdate(job);
			Session.Flush();
			Session.Refresh(job);
			return job;
		}

		public void CreateArtworkScript(IJob job)
		{
			try
			{
				var dir = LepGlobal.Instance.ArtworkDirectory(job, false);
				if (!dir.Exists)
				{
					dir.Create();
					dir.CreateSubdirectory("supply_file");
					dir.CreateSubdirectory("ExtraFiles");
				}

				var url = LepGlobal.Instance.AbsolutePathURL + "/setstatus.aspx";

				string script = null;
				string preflightdone = null;
				//string awaitingapprove = null;
				StreamReader scriptReader = null;
				StreamWriter writer = null;
				string pingurl = null;
				FileInfo f = null;

				// windows
				if (LepArtworkScriptFiles.JobWinScript.Exists)
				{
					scriptReader = LepArtworkScriptFiles.JobWinScript.OpenText();
					script = scriptReader.ReadToEnd();
					var dirFromCust = LepGlobal.Instance.ArtworkDirectory(job, true);
					if (!dirFromCust.Exists)
					{
						dirFromCust.Create();
					}


					preflightdone = $"{dirFromCust.FullName}/._preflightdone.vbs";
					//awaitingapprove = $"{dir.FullName}/._waitingapprove.vbs";
					pingurl = @"{0}?id={1}&status={2}";

					f = new FileInfo(preflightdone);
					writer = f.CreateText();
					var data = script.Replace("[[URL]]", string.Format(pingurl, url, job.Id, "preflightdone"));
					writer.Write(data.Replace("[[ScriptFileName]]", "._preflightdone.vbs"));
					writer.Close();

					//f = new FileInfo(awaitingapprove);
					//writer = f.CreateText();
					//data = script.Replace("[[URL]]", string.Format(pingurl, url, job.Id, "customerapprove"));
					//writer.Write(data.Replace("[[ScriptFileName]]", "._waitingapprove.vbs"));
					//writer.Close();

					scriptReader.Close();

					try
					{
						var src = Path.Combine(LepGlobal.Instance.DataDirectory.FullName, "backupRegectedArt.ps1");
						var dst = Path.Combine(dir.FullName, "backupRegectedArt.ps1");
						if (!File.Exists(dst))
							File.Copy(src, dst);
					}
					catch (Exception ex)
					{
						Log.Error($"Error Creating  backupRegectedArt.ps1" + ex.Message);
					}


					//try
					//{

					//	string src = Path.Combine(LepGlobal.Instance.DataDirectory.FullName, "copyFileOutOfJobsFolder.ps1");
					//	string dst = Path.Combine(dir.FullName, "copyFileOutOfJobsFolder.ps1");
					//	if (!File.Exists(dst))
					//		File.Copy(src, dst);
					//}
					//catch (Exception ex)
					//{
					//	Log.Error($"Error Creating copyFileOutOfJobsFolder.ps1 " + ex.Message);
					//}


				}
				/*
				if (LepArtworkScriptFiles.LegacyMacWorkflow)
				{
					// mac
					if (!LepArtworkScriptFiles.JobMacPreflightScript.Exists || !LepArtworkScriptFiles.PreflightIcon.Exists || !LepArtworkScriptFiles.WaitingApproveIcon.Exists ||
						!LepArtworkScriptFiles.JobMacWaitingApproveScript.Exists)
					{
						return;
					}
					pingurl = @"{0}?id={1}&amp;status={2}";

					scriptReader = LepArtworkScriptFiles.JobMacPreflightScript.OpenText();
					script = scriptReader.ReadToEnd();
					preflightdone = string.Format("{0}/preflightdone.wflow", dir.FullName);
					var scriptdata = script.Replace("[[OrderNumber]]", job.Order.OrderNr);
					scriptdata = scriptdata.Replace("[[OrderDate]]", String.Format("{0:yyyyMMdd}", job.Order.DateCreated));
					scriptdata = scriptdata.Replace("[[JobNumber]]", job.JobFolderName);

					f = new FileInfo(preflightdone);
					writer = f.CreateText();
					writer.Write(scriptdata.Replace("[[URL]]", String.Format(pingurl, url, job.Id, "preflightdone")));
					writer.Close();
					LepArtworkScriptFiles.PreflightIcon.CopyTo(string.Format("{0}/._preflightdone.wflow", dir.FullName), true);
					scriptReader.Close();

					//scriptReader = LepArtworkScriptFiles.JobMacWaitingApproveScript.OpenText();
					//script = scriptReader.ReadToEnd();
					//awaitingapprove = string.Format("{0}/waitingapprove.wflow", dir.FullName);
					//scriptdata = script.Replace("[[OrderNumber]]", job.Order.OrderNr);
					//scriptdata = scriptdata.Replace("[[OrderDate]]", String.Format("{0:yyyyMMdd}", job.Order.DateCreated));
					//scriptdata = scriptdata.Replace("[[JobNumber]]", job.JobFolderName);

					//f = new FileInfo(awaitingapprove);
					//writer = f.CreateText();
					//writer.Write(scriptdata.Replace("[[URL]]", String.Format(pingurl, url, job.Id, "customerapprove")));
					//writer.Close();
					//LepArtworkScriptFiles.WaitingApproveIcon.CopyTo(string.Format("{0}/._waitingapprove.wflow", dir.FullName), true);
					//scriptReader.Close();

					if (!LepArtworkScriptFiles.JobRejectScript.Exists || !LepArtworkScriptFiles.RejectIcon.Exists)
					{
						return;
					}
					var reject = string.Format("{0}/jobreject.html", dir.FullName);
					url = LepGlobal.Instance.AbsolutePathURL + "/staff/order/job-details.aspx?id=" + job.Id.ToString();
					scriptReader = LepArtworkScriptFiles.JobRejectScript.OpenText();
					script = scriptReader.ReadToEnd();
					f = new FileInfo(reject);
					writer = f.CreateText();
					writer.Write(script.Replace("[[URL]]", url));
					writer.Close();
					scriptReader.Close();
					LepArtworkScriptFiles.RejectIcon.CopyTo(string.Format("{0}/._jobreject.html", dir.FullName), true);
				}
				else
				{
					preflightdone = string.Format("{0}/preflightdone.webloc", dir.FullName);
					CreateMacScript(string.Format("lep:preflightdone:{0}", job.Id)).SaveWithOutBOM(preflightdone);
					new FileInfo(preflightdone).CopyMacIconFrom(LepArtworkScriptFiles.PreflightIcon);
				}
			*/
			}
			catch (Exception ex)
			{
				Log.Error(ex.Message);
			}
		}

		public void Save(IJobOptionSpecSize jobOptionSpecSize)
		{
			//AssertPermission("job.update");
			Save<IJobOptionSpecSize>(jobOptionSpecSize);
		}

		public void Save(IJobOptionSpecStock jobOptionSpecStock)
		{
			//AssertPermission("job.update");
			Save<IJobOptionSpecStock>(jobOptionSpecStock);
		}

		public IJobTemplate GetJobTemplate(int id)
		{
			return Session.Get<IJobTemplate>(id);
		}

		public IJobTemplate GetJobTemplate(JobTypeOptions id)
		{
			return Session.Get<IJobTemplate>((int)id);
		}

		public IJobTemplate GetJobTemplate(string name)
		{
			return Session.CreateCriteria(typeof(IJobTemplate))
				.Add(Eq("Name", name))
				.UniqueResult<IJobTemplate>();
		}

		public IList<IJobTemplate> ListJobTemplates()
		{
			IList<IJobTemplate> tmp = new List<IJobTemplate>();
			tmp.Add(Session.Get<IJobTemplate>(Convert.ToInt32(BusinessCard)));
			tmp.Add(Session.Get<IJobTemplate>(Convert.ToInt32(BusinessCardNdd)));
			tmp.Add(Session.Get<IJobTemplate>(Convert.ToInt32(BusinessCardSdd)));
			tmp.Add(Session.Get<IJobTemplate>(Convert.ToInt32(Letterhead)));
			tmp.Add(Session.Get<IJobTemplate>(Convert.ToInt32(LetterheadNDD)));
			tmp.Add(Session.Get<IJobTemplate>(Convert.ToInt32(LetterheadSDD)));
			tmp.Add(Session.Get<IJobTemplate>(Convert.ToInt32(DL)));
			tmp.Add(Session.Get<IJobTemplate>(Convert.ToInt32(DLSpecial)));
			tmp.Add(Session.Get<IJobTemplate>(Convert.ToInt32(Brochure)));
			tmp.Add(Session.Get<IJobTemplate>(Convert.ToInt32(BrochureSpecial)));
			tmp.Add(Session.Get<IJobTemplate>(Convert.ToInt32(BrochureNDD)));
			tmp.Add(Session.Get<IJobTemplate>(Convert.ToInt32(BrochureSDD)));
			tmp.Add(Session.Get<IJobTemplate>(Convert.ToInt32(Magazine)));
			tmp.Add(Session.Get<IJobTemplate>(Convert.ToInt32(MagazineSeparate)));
			tmp.Add(Session.Get<IJobTemplate>(Convert.ToInt32(MagazineNDD)));


			tmp.Add(Session.Get<IJobTemplate>(Convert.ToInt32(PresentationFolder)));

			tmp.Add(Session.Get<IJobTemplate>(Convert.ToInt32(PresentationFolderNDD)));
			tmp.Add(Session.Get<IJobTemplate>(Convert.ToInt32(Postcard)));
			tmp.Add(Session.Get<IJobTemplate>(Convert.ToInt32(Poster)));
			tmp.Add(Session.Get<IJobTemplate>(Convert.ToInt32(Notepads)));
			tmp.Add(Session.Get<IJobTemplate>(Convert.ToInt32(GolfScoreCards)));
			tmp.Add(Session.Get<IJobTemplate>(Convert.ToInt32(FridgeMagnet)));
			tmp.Add(Session.Get<IJobTemplate>(Convert.ToInt32(Custom)));
			return tmp;
		}

		public IList<IJobOptionSpecSize> ListSizeOptions(IJobTemplate jobTemplate)
		{
			return Session.CreateCriteria(typeof(IJobOptionSpecSize))
				.Add(Eq("JobTemplate", jobTemplate))
				.SetCacheable(true)
				.SetReadOnly(true)
				.List<IJobOptionSpecSize>();
		}


		public IList<IJobTemplate> ListAllTemplates()
		{
			return Session.CreateCriteria(typeof(IJobTemplate))
				.SetCacheable(true)
				.AddOrder(Order.Asc("Name"))
				.SetReadOnly(true)
				.List<IJobTemplate>();
		}

		public IList<IPaperSize> ListPaperSize()
		{
			return Session.CreateCriteria(typeof(IPaperSize))
				.SetCacheable(true)
				.AddOrder(Order.Asc("Name"))
				.SetReadOnly(true)
				.List<IPaperSize>();
		}

		public IList<IStock> ListStock()
		{
			return Session.CreateCriteria(typeof(IStock))
				.SetCacheable(true)
				.AddOrder(Order.Asc("Name"))
				.SetReadOnly(true)
				.List<IStock>();
		}

		public IList<Stock> ListStockForRuns()
		{
		return Session.CreateSQLQuery("select Id, Name, GSM from Stock where UsedForRun = 'Y' order by Name")
			.SetResultTransformer(Transformers.AliasToBean(typeof(lep.job.impl.Stock)))
			.SetCacheable(true)
			.SetReadOnly(true).List<Stock>();
		}

		public IList<IBindingOptionLookup> FindBindingOptions(int jobOptionId, int paperSizeId, int stockId,
			PrintType printType, int pages)
		{
			jobOptionId = CustomJobType.Original(jobOptionId);

			//TODO: MikeGreenan Don't add paper size to criteria just yet - data has yet to come from production/marketing
			var criteria = Session.CreateCriteria(typeof(IBindingOptionLookup));
			criteria.Add(Eq("JobOptionId", jobOptionId));
			criteria.Add(Eq("StockId", stockId));
			//Default PrintType to Offset when both
			if (printType == B)
			{
				printType = O;
			}
			criteria.Add(Eq("PrintType", printType));
			criteria.Add(Le("MinTextPP", pages));
			criteria.Add(Ge("MaxTextPP", pages));
			criteria.AddOrder(Order.Asc("BindingOptionId"));
			criteria.SetReadOnly(true);
			criteria.SetCacheable(true);
			return criteria.List<IBindingOptionLookup>();
		}

		public IList<IJob> FindReadyJobs(Facility facility, IRunSearchCriteria search, Order ordering)
		{
			var criteria = Session.CreateCriteria(typeof(IJob), "j")
				//.Add(In("j.Status", new List<JobStatusOptions>() { JobStatusOptions.PreflightDone, JobStatusOptions.DPCPreProduction }))
				.Add(Eq("j.Status", JobStatusOptions.PreflightDone))
				//.Add(Restrictions.Eq("j.Enable", true))
				.Add(Eq("j.Facility", facility))
				.CreateCriteria("j.Order", "o")
				.CreateAlias("o.Customer", "c")
				.CreateAlias("j.Stock", "s")
				.CreateAlias("j.FinishedSize", "f")
				.CreateAlias("j.FinishedSize.PaperSize", "p")
				.Add(Eq("o.Status", OrderStatusOptions.Ready));

			var consideredForGanging = Or(
						Eq("j.PrintType", O),
						And(
							Eq("j.PrintType", D),
							Or(InG("s.GSM", new[] { 310, 360, 420 }), InG("s.Id", new[] { 29, 31 }))
							)
					);
			criteria.Add(consideredForGanging);

			//criteria.Add(Eq("j.PrintType", O));



			//LORD-971: show Digitals as well as Offsets
			//criteria.Add(Or(Eq("j.PrintType", O), Eq("j.PrintType", D)));


			if (search != null)
			{
				if (!String.IsNullOrEmpty(search.OrderNr))
				{
					var order = 0;
					int.TryParse(search.OrderNr, out order);
					criteria.Add(Eq("o.Id", order));
				}
				if (!String.IsNullOrEmpty(search.JobNr))
				{
					var job = 0;
					int.TryParse(search.JobNr, out job);
					criteria.Add(Eq("j.Id", job));
				}
				if (!String.IsNullOrEmpty(search.Customer))
				{
					criteria.Add(Like("c.Name", search.Customer, MatchMode.Anywhere));
				}
				if (search.Cello.HasValue)
				{
					var cellos = CelloUtils.ToJobCelloGlaze(search.Cello.Value);
					criteria.Add(Eq("j.FrontCelloglaze", cellos[0]));
					if (cellos[0] != Foil)
						criteria.Add(Eq("j.BackCelloglaze", cellos[1]));
				}

				if (search.Stock != null)
				{
					criteria.Add(Eq("j.Stock", search.Stock));
				}
				if (search.Size != null)
				{
					criteria.Add(Eq("j.FinishedSize.PaperSize", search.Size));
				}
				if (search.IsUrgent)
				{
					criteria.Add(Eq("j.Urgent", true));
				}
				if (search.IsOnHold)
				{
					criteria.Add(Eq("j.ProofStatus", JobProofStatus.OnHold));
				}

				if (search.Side.HasValue)
				{
					var bc1colorDis = new Disjunction();
					bc1colorDis.Add(Eq("j.FrontPrinting", JobPrintOptions.Unprinted));
					bc1colorDis.Add(Eq("j.BackPrinting", JobPrintOptions.Unprinted));
					bc1colorDis.Add(Eq("j.BackPrinting", JobPrintOptions.BW));

					var nonbc1colorDis = Or(Eq("j.FrontPrinting", JobPrintOptions.Unprinted),
						Eq("j.BackPrinting", JobPrintOptions.Unprinted));

					IList<IJobTemplate> bcTemplate = new List<IJobTemplate>()
					{
						GetJobTemplate((int) BusinessCard),
						GetJobTemplate((int) BusinessCardNdd),
						GetJobTemplate((int) BusinessCardSdd),
						GetJobTemplate((int) Postcard)
					};

					var oneColor =
						Or(
							And(InG<IJobTemplate>("j.Template", bcTemplate), bc1colorDis),
							And(
								Not(InG<IJobTemplate>("j.Template", bcTemplate)),
								nonbc1colorDis));

					if (search.Side.Value == 1)
					{
						criteria.Add(oneColor);
					}
					else
					{
						criteria.Add(Not(oneColor));
					}
				}
			}

			if (ordering != null)
			{
				criteria.AddOrder(ordering);
			}
			else
			{
				criteria.AddOrder(Order.Desc("j.Urgent")).AddOrder(Order.Asc("o.SubmissionDate"));
			}
			return criteria.List<IJob>();
		}

		public ICriteria FindReadyJobs2(Facility facility, IRunSearchCriteria search, Order ordering)
		{
			var criteria = Session.CreateCriteria(typeof(IJob), "j")
				//.Add(Eq("j.Status", JobStatusOptions.PreflightDone))
				.Add(In("j.Status",  new List<JobStatusOptions>() {
					JobStatusOptions.PreflightDone })) //, JobStatusOptions.DPCPreProduction
				//.Add(Restrictions.Eq("j.Enable", true))
				.Add(Eq("j.Facility", facility))
				.CreateCriteria("j.Order", "o")
				.CreateCriteria("j.Template", "t")

				.CreateAlias("o.Customer", "c")
				.CreateAlias("j.Stock", "s")
				.CreateAlias("j.FinishedSize", "f")
				.CreateAlias("j.FinishedSize.PaperSize", "p")
				.Add(Eq("o.Status", OrderStatusOptions.Ready));

			// Handle JobTypes filter (Category)
			if (search.JobTypes != null && search.JobTypes.Count > 0)
			{
				criteria.Add(In("t.Id", search.JobTypes.ToArray()));
			}

			// Handle JobType filter (Template)
			if (search.JobType.HasValue)
			{
				criteria.Add(Eq("t.Id", search.JobType.Value));
			}
			//criteria.Add(Eq("j.PrintType", O));
			//criteria.Add(Eq("j.PrintType", O));

			//var consideredForGanging = Or(
			//			Eq("j.PrintType", O),
			//			And(
			//				Eq("j.PrintType", D),
			//				Or(InG("s.GSM", new[] { 310, 360, 420 }), InG("s.Id", new[] { 29, 31 }))
			//				)
			//		);


			//criteria.Add(consideredForGanging);
			//LORD-971: show Digitals as well as Offsets
			//criteria.Add(Or(Eq("j.PrintType", O), Eq("j.PrintType", D)));

			if (search != null)
			{
				if (!String.IsNullOrEmpty(search.OrderNr))
				{
					var order = 0;
					int.TryParse(search.OrderNr, out order);
					criteria.Add(Eq("o.Id", order));
				}
				if (!String.IsNullOrEmpty(search.JobNr))
				{
					var job = 0;
					int.TryParse(search.JobNr, out job);
					criteria.Add(Eq("j.Id", job));
				}
				if (!String.IsNullOrEmpty(search.Customer))
				{
					criteria.Add(Like("c.Name", search.Customer, MatchMode.Start));
				}
				if (search.Cello.HasValue)
				{
					var cellos = CelloUtils.ToJobCelloGlaze(search.Cello.Value);
					criteria.Add(Eq("j.FrontCelloglaze", cellos[0]));
					if (cellos[0] != Foil)
						criteria.Add(Eq("j.BackCelloglaze", cellos[1]));
				}
				if (search.Stock != null)
				{
					criteria.Add(Eq("j.Stock", search.Stock));
				}

				if (search.StockKind != null)
				{
					criteria.Add(Like("s.Name", $"%{search.StockKind}%"));
				}

				if (search.Size != null)
				{
					criteria.Add(Eq("j.FinishedSize.PaperSize", search.Size));
				}
				if (search.IsUrgent)
				{
					criteria.Add(Eq("j.Urgent", true));
				}
				if (search.IsOnHold)
				{
					criteria.Add(Eq("j.ProofStatus", JobProofStatus.OnHold));
				}

				if (search.Side.HasValue)
				{
					var bc1colorDis = new Disjunction();
					bc1colorDis.Add(Eq("j.FrontPrinting", JobPrintOptions.Unprinted));
					bc1colorDis.Add(Eq("j.BackPrinting", JobPrintOptions.Unprinted));
					bc1colorDis.Add(Eq("j.BackPrinting", JobPrintOptions.BW));

					var nonbc1colorDis = Or(Eq("j.FrontPrinting", JobPrintOptions.Unprinted),
						Eq("j.BackPrinting", JobPrintOptions.Unprinted));

					IList<IJobTemplate> bcTemplate = new List<IJobTemplate>()
					{
						GetJobTemplate((int) BusinessCard),
						GetJobTemplate((int) BusinessCardNdd),
						GetJobTemplate((int) BusinessCardSdd),
						GetJobTemplate((int) Postcard)
					};

					var oneColor =
						Or(
							And(InG<IJobTemplate>("j.Template", bcTemplate), bc1colorDis),
							And(Not(InG<IJobTemplate>("j.Template", bcTemplate)), nonbc1colorDis));

					if (search.Side.Value == 1)
					{
						criteria.Add(oneColor);
					}
					else
					{
						criteria.Add(Not(oneColor));
					}
				}


				if (search.RunSearchOption != RunSearchOptions.None)
				{
					if (search.RunSearchOption.Is(RunSearchOptions.BC))
					{
						criteria.Add(In("t.Id", new[] { 1, 26, 27, 11 }));
					}
					else if (search.RunSearchOption.Is(RunSearchOptions.BC310))
					{
						criteria.Add(In("t.Id", new[] { 1, 26, 27, 11 }));
						criteria.Add(In("s.Id", new[] { 17 }));
					}
					else if (search.RunSearchOption.Is(RunSearchOptions.BC360))
					{
						criteria.Add(In("t.Id", new[] { 1, 26, 27, 11 }));
						criteria.Add(In("s.Id", new[] { 93 }));
					}
					else if (search.RunSearchOption.Is(RunSearchOptions.BC420))
					{
						criteria.Add(In("t.Id", new[] { 1, 26, 27, 11 }));
						criteria.Add(In("s.Id", new[] { 91 }));
					}
					else if (search.RunSearchOption.Is(RunSearchOptions.NonBC))
					{
						criteria.Add(Not(In("t.Id", new[] { 1, 26, 27, 11 })));
					}
					else if (search.RunSearchOption.Is(RunSearchOptions.BCLoyalty))
					{
						criteria.Add(In("t.Id", new[] { 1, 26, 27, 11 }));
						criteria.Add(In("s.Id", new[] { 29 }));
					}
					else if (search.RunSearchOption.Is(RunSearchOptions.BC350Recycled))
					{
						criteria.Add(In("t.Id", new[] { 1, 26, 27, 11 }));
						criteria.Add(In("s.Id", new[] { 31 }));
					}
				}



			}

			if (ordering != null)
			{
				criteria.AddOrder(ordering);
			}
			else
			{
				criteria.AddOrder(Order.Desc("j.Urgent")).AddOrder(Order.Asc("o.SubmissionDate"));
			}

			return criteria;
			// some optimisation
			//var ids = criteria.SetProjection(Projections.Property("Id")).List<int>().ToArray<int>();
			//var criteria2 = Session.CreateCriteria(typeof(IJob), "j").Add(Restrictions.In("j.Id", ids));
			//return criteria2;
		}

		public void CopyArtwork(IJob job)
		{
			var dir = LepGlobal.Instance.ArtworkDirectory(job, true);
			if (!dir.Exists)
			{
				dir.Create();
			}

			foreach (var art in job.Artworks)
			{
				if (!string.IsNullOrEmpty(art.Supplied))
				{
					FileStream fs = null;
					try
					{
						var fileloc = string.Format("{0}/{1}", dir.FullName, art.Supplied);
						if (File.Exists(fileloc))
						{
							art.Ready = art.Supplied;
							art.Preview = String.Format("{0}.{1}", art.Position, "PNG");
							fs = new FileStream(fileloc, FileMode.Open);
							SaveReadyArtwork(art, fs);
						}
					}
					catch
					{
					}
					finally
					{
						if (fs != null)
						{
							fs.Close();
						}
					}
				}
			}
			Save(job);
		}

		public void CopyReOrderedArtwork(IJob originalJob, IJob newJob)
		{
			//TODO: MikeGreenan (2014/05/13) Eventually, over time, will be able to use one method for copying all PDF's (as customers can now only submit PDF artwork files), but untill then just use two methods

			DirectoryInfo sourcedir;
			DirectoryInfo sourcereadydir;
			//Is source archived, if so set archived directory for customer artwork and art ready
			//if (sourcejob.Order.Status == OrderStatusOptions.Archived)
			//{
			//	sourcedir = ConfigurationApplication.OldArtworkDirectory(sourcejob, true);
			//	sourcereadydir = ConfigurationApplication.OldArtworkDirectory(sourcejob, false);
			//}
			//else
			//{

			//}

			sourcedir = LepGlobal.Instance.ArtworkDirectory(originalJob, true);
			sourcereadydir = LepGlobal.Instance.ArtworkDirectory(originalJob, false);
			//Only if the Source dir actually exists.
			if (sourcedir.Exists)
			{
				var destinationdir = LepGlobal.Instance.ArtworkDirectory(newJob, true);
				var destinationreadydir = LepGlobal.Instance.ArtworkDirectory(newJob, false);
				if (!destinationdir.Exists)
				{
					destinationdir.Create();
				}

				var files = sourcedir.GetFiles();
				var filesreadysource = sourcereadydir.GetFiles();

				foreach (var file in files)
				{
					if (!File.Exists(Path.Combine(destinationdir.FullName, file.Name)))
					{
						file.CopyTo(Path.Combine(destinationdir.FullName, file.Name));
					}
				}
				foreach (var fileready in filesreadysource)
				{
					if (fileready.Name.StartsWith("."))
						continue;

					if (fileready.Extension.ToUpper() == ".PDF" || fileready.Extension.ToUpper() == ".TIF")
					{
						if (!File.Exists(Path.Combine(destinationreadydir.FullName, fileready.Name)))
						{
							fileready.CopyTo(Path.Combine(destinationreadydir.FullName, fileready.Name), true);
						}
					}
				}
			}
		}

		public bool IsArtworkValid(IJob job, bool briefCheckOnly)
		{
			if (job.Order.FileRemoved || job.Artworks.Count == 0)
			{
				return true;
			}

			if (briefCheckOnly &&
				(job.Status != JobStatusOptions.Submitted || job.ArtworkStatus == ArtworkStatusOption.LATER))
			{
				return true;
			}
			//TODO MikeGreenan - remove altogether and let return True

			try
			{
				var dir = LepGlobal.Instance.ArtworkDirectory(job, true);
				var staffdir = LepGlobal.Instance.ArtworkDirectory(job, false);

				foreach (var art in job.Artworks)
				{
					if (!String.IsNullOrEmpty(art.Supplied) && !String.IsNullOrEmpty(art.SuppliedCheckSum))
					{
						string suppliedFile = string.Format("{0}/{1}", dir.FullName, art.Supplied);
						if (!File.Exists(suppliedFile))
						{
							Log.Error("{jobId} MissingFile supplied " + suppliedFile, job.Id);
							return false;
						}
						using (var stream = File.OpenRead(suppliedFile))
						{
							if (art.SuppliedCheckSum != Convert.ToBase64String(SHA256.Create().ComputeHash(stream)))
							{
								Log.Error("{jobId} Missmatch Checksum ", job.Id);
								return false;
							}
						}
					}

					if (!briefCheckOnly)
					{
						if (!String.IsNullOrEmpty(art.Preview) && !String.IsNullOrEmpty(art.PreviewdCheckSum))
						{
							try
							{
								using (
									var stream = File.OpenRead(String.Format("{0}/preview_{1}", staffdir.FullName, art.Preview)))
								{
									if (art.PreviewdCheckSum != Convert.ToBase64String(SHA256.Create().ComputeHash(stream)))
									{
										return false;
									}
								}
							}
							catch (Exception ex)
							{
								Log.Warning("IsArtworkValid (IJob job, bool briefCheckOnly): " + ex.Message);
							}
						}

						if (!String.IsNullOrEmpty(art.Ready) && !String.IsNullOrEmpty(art.ReadyCheckSum))
						{
							string readyFile = string.Format("{0}/{1}", staffdir.FullName, art.Ready);
							if (!File.Exists(readyFile))
							{
								Log.Error("{jobId} MissingFile ready ", job.Id);
								return false;
							}
							using (var stream = File.OpenRead(readyFile))
							{
								if (art.ReadyCheckSum != Convert.ToBase64String(SHA256.Create().ComputeHash(stream)))
								{
									return false;
								}
							}
						}
					}
				}

				return true;
			}
			catch (Exception ex)
			{
				Log.Error(ex.Message);
			}

			return false;
		}


		public void SaveArtwork(IArtwork art, FileInfo input)
		{
			using (Stream stream = input.OpenRead())
			{
				SaveArtwork(art, stream);
				stream.Close();
			}
		}


		public void SaveArtwork(IArtwork art, Stream input)
		{

			var dirFromCustomer = LepGlobal.Instance.ArtworkDirectory(art.Job, true);
			if (!dirFromCustomer.Exists)
			{
				dirFromCustomer.Create();
			}
			var fileName = Path.Combine(dirFromCustomer.FullName, art.Supplied);

			try
			{

				using (var dfs = new FileStream(fileName, FileMode.Create))
				{
					input.CopyTo(dfs);
				}
			}
			catch (Exception ex)
			{
				Log.Error($"Error copying   to {fileName}");
			}

			//CopyStream(input, dfs);
			//var thumbnail = string.Format("{0}/thumbnail_{1}", dirFromCustomer.FullName, art.Supplied);
			//if (fileName.EndsWith(".pdf", StringComparison.CurrentCultureIgnoreCase))
			//{
			//	ConvertPDFPage1ToPng(fileName, thumbnail);
			//}

			//using (var stream = File.OpenRead(string.Format("{0}/{1}", dirFromCustomer.FullName, art.Supplied)))
			//{
			//	art.SuppliedCheckSum = Convert.ToBase64String(SHA256.Create().ComputeHash(stream));
			//}

			Save(art);

		}

		public bool ReadyArtExists(IJob job)
		{

			if (job.Artworks.Count == 0)
			{
				return false;
			}

			try
			{
				var dir = LepGlobal.Instance.ArtworkDirectory(job, true);
				var staffdir = LepGlobal.Instance.ArtworkDirectory(job, false);

				foreach (var art in job.Artworks)
				{
					if (!String.IsNullOrEmpty(art.Ready) && !String.IsNullOrEmpty(art.ReadyCheckSum))
					{
						string readyFile = string.Format("{0}/{1}", staffdir.FullName, art.Ready);
						if (!File.Exists(readyFile))
						{
							Log.Warning("{jobId} MissingFile ready ", job.Id);
							return false;
						}
					}

				}

				return true;
			}
			catch (Exception ex)
			{
				Log.Error(ex.Message);
			}

			return true;
		}

		public void CopyArtworks(IJob originalJob, IJob newJob)
		{
			var msg = "";
			if (!IsArtworkValid(originalJob, briefCheckOnly: false))
			{
				Log.Warning("MissingFiles: Not all files found for job {jobId}", originalJob.Id);
			}
			try
			{
				var osupply = LepGlobal.Instance.ArtworkDirectory(originalJob, true);
				var oready = LepGlobal.Instance.ArtworkDirectory(originalJob, false);
				var nsupply = LepGlobal.Instance.ArtworkDirectory(newJob, true);
				var nready = LepGlobal.Instance.ArtworkDirectory(newJob, false);

				// Create destination directories if they don't exist
				if (!nsupply.Exists)
				{
					nsupply.Create();
				}
				if (!nready.Exists)
				{
					nready.Create();
				}

				string sf, df;

				foreach (var oart in originalJob.Artworks)
				{
					var nart = newJob.AddArtwork(oart.Position);
					nart.Type = oart.Type;

					if (!string.IsNullOrEmpty(oart.Supplied))
					{
						SetSupplyArt(newJob, nart, oart.Supplied);

						msg = $"1 {oart.Supplied}  ->  {nart.Supplied}";

						var f = osupply.GetFiles(oart.Supplied);
						if (f.Length > 0)
						{
							sf = f[0].FullName;

							Log.Debug($"Trying to read {f[0].FullName}");
							SaveArtwork(nart, f[0]);
						}

					}
					if (!string.IsNullOrEmpty(oart.Ready))
					{
						SetReadyArt(newJob, nart, oart.Ready);
						msg = $"2 {oart.Ready}  ->  {nart.Ready}";
						var f = oready.GetFiles(oart.Ready);
						if (f.Length > 0)
						{
							Stream s = f[0].OpenRead();
							SaveReadyArtwork(nart, s);
							s.Close();
						}

						// Copy thumbnails and previews with proper naming
						if (!string.IsNullOrEmpty(oart.Preview))
						{
							// Copy preview files
							var previewFiles = oready.GetFiles($"preview_{oart.Preview}");
							foreach (var previewFile in previewFiles)
							{
								try
								{
									var destPreviewPath = Path.Combine(nready.FullName, $"preview_{nart.Preview}");
									File.Copy(previewFile.FullName, destPreviewPath, true);
									Log.Debug($"Copied preview: {previewFile.Name} -> {destPreviewPath}");
								}
								catch (Exception ex)
								{
									Log.Error($"Error copying preview file: {ex.Message}");
								}
							}

							// Copy thumbnail files
							var thumbnailFiles = oready.GetFiles($"thumbnail_{oart.Preview}");
							foreach (var thumbnailFile in thumbnailFiles)
							{
								try
								{
									var destThumbnailPath = Path.Combine(nready.FullName, $"thumbnail_{nart.Preview}");
									File.Copy(thumbnailFile.FullName, destThumbnailPath, true);
									Log.Debug($"Copied thumbnail: {thumbnailFile.Name} -> {destThumbnailPath}");
								}
								catch (Exception ex)
								{
									Log.Error($"Error copying thumbnail file: {ex.Message}");
								}
							}
						}
					}
				}

				// Copy PNG thumbnails from LepGlobal.GetThumbs
				try
				{
					// Get the thumbnails from the original job using LepGlobal.GetThumbs
					var originalThumbs = LepGlobal.Instance.GetThumbs(originalJob);
					if (originalThumbs != null && originalThumbs.Count > 0)
					{
						Log.Debug($"Found {originalThumbs.Count} thumbnails from GetThumbs for job {originalJob.Id}");

						// Create the destination path for the new job
						var newThumbsPath = LepGlobal.Instance.ArtworkPath(newJob.Order, newJob, true, "");
						var newThumbsDir = new DirectoryInfo(newThumbsPath);
						if (!newThumbsDir.Exists)
						{
							newThumbsDir.Create();
						}

						// Copy each thumbnail with the new job ID in the filename
						foreach (var thumb in originalThumbs)
						{
							try
							{
								// Replace the old job ID with the new job ID in the filename
								string newThumbName = thumb.Name.Replace(originalJob.Id.ToString(), newJob.Id.ToString());
								string destThumbPath = Path.Combine(newThumbsDir.FullName, newThumbName);

								// Copy the thumbnail file
								File.Copy(thumb.FullName, destThumbPath, true);
								Log.Debug($"Copied GetThumbs thumbnail: {thumb.Name} -> {destThumbPath}");
							}
							catch (Exception ex)
							{
								Log.Error($"Error copying GetThumbs thumbnail file: {ex.Message}");
							}
						}
					}
				}
				catch (Exception ex)
				{
					Log.Error($"Error copying GetThumbs thumbnails: {ex.Message}");
				}

				//CopyReOrderedArtwork(originalJob, newJob);
			}
			catch (Exception ex)
			{
				Log.Error(ex.Message, ex);
				Log.Error(msg);
			}

			MergeFrontAndBackArtwork(newJob);
		}

		public void TechCheckArtwork(IArtwork art)
		{
			//TODO: Mike Greenan

			//	if (File.Exists(@"C:\Program Files\\Enfocus\\Enfocus PitStop Server 12\\PitStopServerCLI.exe"))
			//	{
			//		DirectoryInfo dir = ConfigurationApplication.ArtworkDirectory(art.Job, true);

			//		System.Diagnostics.Process p;
			//		p = new System.Diagnostics.Process();
			//		p.StartInfo.UserName = "michael.greenan";
			//		var s = new SecureString();
			//		s.AppendChar('1');
			//		s.AppendChar('9');
			//		s.AppendChar('6');
			//		s.AppendChar('6');
			//		s.AppendChar('_');
			//		s.AppendChar('M');
			//		s.AppendChar('G');
			//		s.AppendChar('B');
			//		s.AppendChar('_');
			//		s.AppendChar('V');
			//		s.AppendChar('8');
			//		p.StartInfo.Password = s;
			//		p.StartInfo.Domain = "LEP";

			//		p.StartInfo.FileName = @"""C:\Program Files\\Enfocus\\Enfocus PitStop Server 12\\PitStopServerCLI.exe""";

			//		String namefile = Path.GetFileNameWithoutExtension(art.Supplied);
			//		String configfile = string.Format("{0}\\{1}_configuration.xml", dir.FullName, namefile);
			//		String taskreportfile = string.Format( "{0}\\{1}_taskreport.xml", dir.FullName, namefile );
			//		String inputfile = string.Format( "{0}\\{1}", dir.FullName, art.Supplied);
			//		String mutatorfile = "D:\\1.ppp";
			//		String outputfile = string.Format("{0}\\{1}_out.pdf", dir.FullName, namefile);
			//		String pdfreportfile = string.Format("{0}\\{1}_report.pdf", dir.FullName, namefile);
			//		String xmlreportfile = string.Format("{0}\\{1}._report.xml", dir.FullName, namefile);

			//		XmlWriterSettings settings = new XmlWriterSettings();
			//		settings.Indent = true;
			//		settings.IndentChars = "\t";
			//		settings.NewLineOnAttributes = true;

			//		//Create the config XML file, then use the config file in arguments.
			//		using (XmlWriter writer = XmlWriter.Create( configfile, settings )) {
			//			writer.WriteStartDocument( false );
			//			writer.WriteComment( "This xml file is the PitStop Server config document." );
			//			//Start with the root element
			//			writer.WriteStartElement( "Configuration", "http://www.enfocus.com/2011/PitStopServerCLI_Configuration.xsd" );
			//				writer.WriteStartElement( "Versioning" );
			//					writer.WriteElementString( "Version", "5" );
			//				writer.WriteEndElement();//Versioning
			//				writer.WriteStartElement( "TaskReport" );
			//					writer.WriteElementString( "TaskReportPath", taskreportfile);
			//					writer.WriteElementString( "LogProcessResults", "true" );
			//					writer.WriteElementString( "LogErrors", "true" );
			//				writer.WriteEndElement();//TaskReport
			//				writer.WriteStartElement( "Process" );
			//					writer.WriteStartElement( "InputPDF" );
			//						writer.WriteElementString( "InputPath", inputfile );
			//					writer.WriteEndElement();//InputPDF

			//				writer.WriteStartElement( "Reports" );
			//					writer.WriteStartElement( "ReportPDF" );
			//						writer.WriteElementString( "ReportPath", pdfreportfile );
			//					writer.WriteEndElement();
			//					writer.WriteStartElement( "ReportXML" );
			//						writer.WriteElementString( "ReportPath", xmlreportfile );
			//					writer.WriteEndElement();
			//				writer.WriteEndElement();//Reports

			//				writer.WriteStartElement( "Mutators" );
			//					writer.WriteElementString( "PreflightProfile", mutatorfile );
			//				writer.WriteEndElement();//Mutators

			//				writer.WriteElementString( "MeasurementUnit", "Millimeter" );

			//			//UserInfo??

			//			writer.WriteEndElement();//Process
			//			writer.WriteEndElement();//Configuration.
			//			writer.WriteEndDocument();
			//			writer.Flush();
			//		}

			//		//p.StartInfo.WorkingDirectory = System.IO.Path.GetDirectoryName( p.StartInfo.FileName );
			//		//p.StartInfo.WindowStyle = System.Diagnostics.ProcessWindowStyle.Maximized;
			//		//p.StartInfo.Arguments = " -input " + inputfile + " -mutator " + profile + " -reportPDF " + pdfreportfile + " -reportXML " + xmlreportfile;

			//		p.StartInfo.Arguments = " -config " + configfile;

			//		p.StartInfo.RedirectStandardOutput = true;
			//		p.StartInfo.RedirectStandardError = true;

			//		p.StartInfo.UseShellExecute = false;
			//		try
			//		{
			//			p.Start();
			//			StreamReader outputWriter = p.StandardOutput;
			//			String errorReader = p.StandardError.ReadToEnd();
			//			String line = outputWriter.ReadLine();
			//			while (line != null)
			//			{
			//				Debug.WriteLine(line);
			//				line = outputWriter.ReadLine();
			//			}

			//			p.WaitForExit();
			//		}
			//		catch (Exception exxx)
			//		{
			//			Debug.WriteLine(exxx.Message);
			//		}
			//		//string output = p.StandardOutput.ReadToEnd();
			//		Debug.WriteLine("*** PitStop finish ***");
			//	}
		}

		public void SaveReadyArtwork(IArtwork art, Stream input)
		{
			var dir = LepGlobal.Instance.ArtworkDirectory(art.Job, false);
			if (!dir.Exists)
			{
				dir.Create();
			}

			var file = string.Format("{0}/{1}", dir.FullName, art.Ready);
			var preview = string.Format("{0}/preview_{1}", dir.FullName, art.Preview);
			var thumbnail = string.Format("{0}/thumbnail_{1}", dir.FullName, art.Preview);
			try
			{
				using (var stream = new FileStream(file, FileMode.Create))
				{
					//CopyStream(input, stream);
					input.CopyTo(stream);
				}
			}
			catch (Exception ex)
			{
				Log.Error($"SaveReadyArtwork -> file");
			}


			// if Job is digital then copy to Hot Folder as well
			//if (art.Job.IsDigital) {
			//    DirectoryInfo folderHPIndigo = ConfigurationApplication.ArtworkDirectoryDigitalHotFolder();
			//    if (!folderHPIndigo.Exists) {
			//        folderHPIndigo.Create();
			//    }
			//    Regex r = new Regex( @"[^\w]", RegexOptions.IgnoreCase | RegexOptions.CultureInvariant | RegexOptions.Compiled );
			//    string fileNameHPIndigo = string.Format( "{0}O{1}_J{2}____{3}__{4}__Qty{5}__{6}", folderHPIndigo.FullName, art.Job.Order.OrderNr, art.Job.JobNr, r.Replace( art.Job.Stock.Name, "" ), r.Replace( art.Job.FinishedSize.PaperSize.Name, "" ), art.Job.Quantity, art.Ready );
			//    using (var stream2 = new FileStream( fileNameHPIndigo, FileMode.Create )) {
			//        CopyStream( File.OpenRead( file ), stream2 );
			//    }

			//    // Print DPC Label.
			//    // LabelPrinter.Print( LabelType.DPCProcessing, this);
			//}

			//if (art.Job.IsWideFormat()) {
			//	DirectoryInfo folderHPIndigo = ConfigurationApplication.ArtworkDirectoryDigitalHotFolder();
			//	if (!folderHPIndigo.Exists)
			//	{
			//		folderHPIndigo.Create();
			//	}
			//	Regex r = new Regex(@"[^\w]", RegexOptions.IgnoreCase | RegexOptions.CultureInvariant | RegexOptions.Compiled);
			//	string fileNameHPIndigo = string.Format("{0}O{1}_J{2}____{3}__{4}__Qty{5}__{6}", folderHPIndigo.FullName, art.Job.Order.OrderNr, art.Job.JobNr, r.Replace(art.Job.Stock.Name, ""), r.Replace(art.Job.FinishedSize.PaperSize.Name, ""), art.Job.Quantity, art.Ready);
			//	using (var stream2 = new FileStream(fileNameHPIndigo, FileMode.Create))
			//	{
			//		CopyStream(File.OpenRead(file), stream2);
			//	}

			//	// Print DPC Label.
			//	LabelPrinter.Print( LabelType. , this);
			//}

			if (file.EndsWith(".tif", StringComparison.CurrentCultureIgnoreCase) || file.EndsWith(".tiff", StringComparison.CurrentCultureIgnoreCase))
			{
				using (var stream = File.OpenRead(file))
				{
					ConvertTiff2Png(stream, preview);
				}
			}

			//if (file.EndsWith(".pdf", StringComparison.CurrentCultureIgnoreCase))
			//{
			//	using (var stream = File.OpenRead(file))
			//	{
			//		ConvertPDFPage1ToPng(file, thumbnail);
			//	}
			//}

			//using (var ms = File.OpenRead(file))
			//{
			//	art.ReadyCheckSum = Convert.ToBase64String(SHA256.Create().ComputeHash(ms));
			//}

			//try
			//{
			//	using (var ms = File.OpenRead(preview))
			//	{
			//		art.PreviewdCheckSum = Convert.ToBase64String(SHA256.Create().ComputeHash(ms));
			//	}
			//}
			//catch (Exception ex)
			//{
			//	Log.Warning("SaveReadyArtwork (IArtwork art, Stream input)" + ex.Message);
			//}

			Save(art);
		}

		public void ArchieveArtwork(IJob job)
		{
			var dir = LepGlobal.Instance.ArtworkDirectory(job, false);

			if (!dir.Exists)
			{
				dir.Create();
			}

			var subDir = new DirectoryInfo(dir.FullName + "/archive");
			if (!subDir.Exists)
			{
				subDir = dir.CreateSubdirectory("archive");
			}

			dir = LepGlobal.Instance.ArtworkDirectory(job, true);
			if (!dir.Exists)
			{
				dir.Create();
			}

			try
			{
				foreach (var f in dir.GetFiles("*.pdf", SearchOption.AllDirectories))
				{
					f.CopyTo(subDir.FullName + "/" + f.Name);
				}
			}
			catch (Exception ex) { }
		}

		public IList<FileInfo> GetListArtwork(IJob job)
		{
			IList<FileInfo> result = new List<FileInfo>();
			try
			{
				var dir = LepGlobal.Instance.ArtworkDirectory(job, false);
				foreach (var artwork in job.Artworks)
				{
					if (!String.IsNullOrEmpty(artwork.Ready))
					{
						var filename = string.Format("{0}/{1}", dir.FullName, artwork.Ready);
						result.Add(new FileInfo(filename));
					}
				}
			}
			catch (Exception ex)
			{
				Log.Error("Error in get artwork files :" + ex.Message, ex);
			}

			return result;
		}

		public void PrintJob(IJob job)
		{
			try
			{
				var auto = Convert.ToBoolean(_configApp.GetValue(Configuration.AutoPrintQuotes));
				if (auto)
				{
					using (var doc = new JobPrintDocument(job))
					{
						var ps = new PrinterSettings();
						PrintUtils.ConfigurePrinterSettings(
							_configApp.GetValue(Configuration.QuotesPrinterName), ref ps);
						doc.PrinterSettings = ps;
						doc.Print();
					}
				}
			}
			catch (Exception ex)
			{
				Log.Error("Error in get artwork files :" + ex.Message, ex);
			}
		}

		public void PrintJobSheet(IList<IJob> jobs)
		{
			using (var pr = new JobSheetPrintDocument(jobs, tickImagePath, _configApp))
			{
				var ps = new PrinterSettings();
				PrintUtils.ConfigurePrinterSettings(
					_configApp.GetValue(Configuration.FG_JobSheetPrinterName), ref ps);

				pr.PrinterSettings = ps;
				pr.SetupPrintProperties();
				pr.Print();

				if (jobs[0].Facility.HasValue && jobs[0].Facility.Value == PM)
				{
					PrintUtils.ConfigurePrinterSettings(
						_configApp.GetValue(Configuration.PM_JobSheetPrinterName), ref ps);
					pr.PrinterSettings = ps;
					pr.SetupPrintProperties();
					pr.Print();
				}
			}
		}

		public void PrintJobSheetAsNonBC(IList<IJob> jobs)
		{
			using (var pr = new JobSheetPrintDocument(jobs, tickImagePath, _configApp))
			{
				pr.PrintBCasNonBC = true;
				var ps = new PrinterSettings();
				if (jobs[0].Facility.HasValue && jobs[0].Facility.Value == PM)
				{
					PrintUtils.ConfigurePrinterSettings(
						_configApp.GetValue(Configuration.PM_JobSheetPrinterName), ref ps);
				}
				else
				{
					PrintUtils.ConfigurePrinterSettings(
						_configApp.GetValue(Configuration.FG_JobSheetPrinterName), ref ps);
				}
				pr.PrinterSettings = ps;
				pr.SetupPrintProperties();
				pr.Print();
			}
		}

		public void PrintJobRunSheet(IRun run)
		{
			using (var pr = new JobSheetPrintDocument(run.Jobs, tickImagePath, _configApp))
			{
				var ps = new PrinterSettings();
				if (run.Status != RunStatusOptions.ApprovedForPlating)
				{
					var printerName = _configApp.GetValue(Configuration.FG_JobSheetPrinterName);
					// if (LepGlobal.Instance.TestBox)
					// {
					// 	printerName = "Microsoft Print to PDF";
					// }
					PrintUtils.ConfigurePrinterSettings(printerName, ref ps);

					pr.PrinterSettings = ps;
					pr.SetupPrintProperties();
					pr.Print();
				}
			}
			if (run.Facility == PM)
			{
				using (var pm = new JobSheetPrintDocument(run.Jobs, tickImagePath, _configApp))
				{
					var ps_pm = new PrinterSettings();
					//&& run.Status == RunStatusOptions.ApprovedForPlating) {
					PrintUtils.ConfigurePrinterSettings(
						_configApp.GetValue(Configuration.PM_JobSheetPrinterName), ref ps_pm);
					pm.PrinterSettings = ps_pm;
					pm.SetupPrintProperties();
					pm.Print();
				}
			}
		}

		public bool SetSupplyArt(IJob job)
		{
			var dir =
				new DirectoryInfo(String.Format("{0}\\supply_file",
					LepGlobal.Instance.ArtworkDirectory(job, false).FullName));
			if (!dir.Exists)
			{
				dir.Create();
			}

			var supplyFiles = dir.GetFiles();

			IDictionary<string, string> requiredFile = new Dictionary<string, string>();
			IDictionary<string, FileInfo> passedFile = new Dictionary<string, FileInfo>();

			if (!job.IsBusinessCard())
			{
				requiredFile.Add("_multi", "multiart");
			}

			foreach (var required in job.GetRequiredPosition())
			{
				if (required == "front")
				{
					if (job.BackPrinting != JobPrintOptions.Unprinted)
					{
						requiredFile.Add("_ft", required);
					}
					else
					{
						requiredFile.Add("_fto", required);
					}
				}
				else if (required == "back")
				{
					if (job.FrontPrinting != JobPrintOptions.Unprinted)
					{
						requiredFile.Add("_bk", required);
					}
					else
					{
						requiredFile.Add("_bko", required);
					}
				}
				else
				{
					requiredFile.Add("_" + required, required);
				}
			}

			Log.Debug("Start Set Artwork:" + job.JobNr);
			foreach (var s in requiredFile.Keys)
			{
				Log.Debug(String.Format("Require File: {0} {1}", requiredFile[s], s));
			}

			foreach (var f in dir.GetFiles())
			{
				var filename = f.Name;
				if (filename.LastIndexOf('.') != -1)
				{
					filename = filename.Substring(0, filename.LastIndexOf('.'));
				}
				var match = "";
				foreach (var required in requiredFile.Keys)
				{
					if (filename.ToLower().EndsWith(required, StringComparison.CurrentCultureIgnoreCase))
					{
						match = required;
						break;
					}
				}
				if (!String.IsNullOrEmpty(match))
				{
					passedFile.Add(requiredFile[match], f);
				}
				else
				{
					Log.Debug("Misc file: " + f.Name);
				}
			}

			var artworkverify = false;
			if (passedFile.ContainsKey("multiart"))
			{
				Log.Debug("Folder contains multi file");
				artworkverify = true;
			}
			else if (job.IsBusinessCard())
			{
				Log.Debug(String.Format("Business card verify passfile:{0}  requirefile:{1}", passedFile.Count,
					requiredFile.Count));
				artworkverify = passedFile.Count == requiredFile.Count;
			}
			else
			{
				if (passedFile.Count == job.GetRequiredPosition().Count)
				{
					Log.Debug("Required file count = Passed file count");
					artworkverify = true;
				}
				else
				{
					artworkverify = job.GetRequiredPosition().Count == passedFile.Count;
				}
			}

			Log.Debug("End Set Artwork:" + artworkverify.ToString());
			if (artworkverify)
			{
				foreach (var key in passedFile.Keys)
				{
					var artwork = job.GetArtwork(key);
					if (artwork == null)
					{
						artwork = job.AddArtwork(key);
					}
					artwork.Supplied = passedFile[key].Name;
					SaveArtwork(artwork, passedFile[key]);
				}
			}

			return artworkverify;
		}

		public void TrySetPreflightDone(IJob _job, string status, IUser user)
		{
			if (string.IsNullOrEmpty(status))
			{
				throw new Exception("status required");
			}

			if (status != "customerapprove" && status != "preflightdone")
			{
				throw new Exception("invalid status");
			}

			if (string.IsNullOrEmpty(_job.Price))
			{
				throw new Exception("job has not been quoted");
			}

			if (!SetArtworks(_job))
			{
				throw new Exception("required artwork file not found, or are incorrectly named.");
			}

			if (!_job.IsArtworkValidForPreflight())
			{
				throw new Exception("artwork not valid for preflight");
			}

			_job.JobSaveState(user, status == "customerapprove");
			if (_job.Status == JobStatusOptions.PreflightDone && (_job.IsNCRBook() || _job.IsEnvelope() || _job.IsWideFormat()))
			{
				_emailApp.SendRaisePurchaseOrderForJobToAcounts(_job);
			}

			Save(_job);
		}

		public bool SetArtworks(IJob job)
		{
			var dir = LepGlobal.Instance.ArtworkDirectory(job, true);
			if (!dir.Exists)
			{
				dir.Create();
			}

			var previewFiles = dir.GetFiles().Where(_ =>
			{
				if (_.Name.StartsWith(".")) return false;
				var ext = Path.GetExtension(_.FullName);
				return ext != ".vbs" && ext != ".webloc" && ext != ".png";
			}
			);

			IDictionary<string, string> requiredFile = new Dictionary<string, string>();
			IDictionary<string, FileInfo> passedFile = new Dictionary<string, FileInfo>();

			requiredFile.Add("_multi", "multiart");
			var requiredposses = job.GetRequiredPosition();
			foreach (var required in requiredposses)
			{
				if (required == "front")
				{
					if (job.BackPrinting != JobPrintOptions.Unprinted)
					{
						requiredFile.Add("_ft", required);
					}
					else
					{
						requiredFile.Add("_fto", required);
					}
				}
				else if (required == "back")
				{
					if (job.FrontPrinting != JobPrintOptions.Unprinted)
					{
						requiredFile.Add("_bk", required);
					}
					else
					{
						requiredFile.Add("_bko", required);
					}
				}
				else
				{
					requiredFile.Add("_" + required, required);
				}
			}

			Log.Debug("Start Set Artwork:" + job.JobNr);
			foreach (var s in requiredFile.Keys)
			{
				Log.Debug(String.Format("Require File: {0} {1}", requiredFile[s], s));
			}

			foreach (var f in previewFiles)
			{
				var filename = f.Name;
				if (filename.LastIndexOf('.') != -1)
				{
					filename = filename.Substring(0, filename.LastIndexOf('.'));
				}
				var match = "";
				foreach (var required in requiredFile.Keys)
				{
					if (filename.ToLower().EndsWith(required) || filename.ToLower().EndsWith(required.Replace(" ", "").ToLower()))
					{
						match = required;
						break;
					}
				}
				if (!String.IsNullOrEmpty(match))
				{
					var stream = f.OpenRead();
					var type = LepArtworkScriptFiles.FileDetector.GetFileType(stream);
					Log.Debug(String.Format("Match file: {0} {1}", f.Name, type.ToString()));
					stream.Close();
					if (job.IsBusinessCard())
					{
						if (match == "_multi" && type == ArtworkTypeOptions.PDF)
						{
							if (!passedFile.ContainsKey(requiredFile[match]))
								passedFile.Add(requiredFile[match], f);
						}
						else
						{
							if (job.IsDigital())
							{
								if (type == ArtworkTypeOptions.TIFF || type == ArtworkTypeOptions.PDF)
								{
									if (!passedFile.ContainsKey(requiredFile[match]))
										passedFile.Add(requiredFile[match], f);

								}
							}
							else if (type == ArtworkTypeOptions.TIFF || type == ArtworkTypeOptions.PDF)
							{
								if (!passedFile.ContainsKey(requiredFile[match]))
									passedFile.Add(requiredFile[match], f);

							}
						}
					}
					else
					{
						if (match == "_multi")
						{
							if (type == ArtworkTypeOptions.PDF)
							{
								if (!passedFile.ContainsKey(requiredFile[match]))
									passedFile.Add(requiredFile[match], f);
							}
						}
						else if (type == ArtworkTypeOptions.TIFF || type == ArtworkTypeOptions.JPG ||
								 type == ArtworkTypeOptions.PDF || type == ArtworkTypeOptions.EPS)
						{
							if (!passedFile.ContainsKey(requiredFile[match]))
								passedFile.Add(requiredFile[match], f);
						}
					}
				}
				else
				{
					Log.Debug("Misc file: " + f.Name);
				}
			}

			var artworkverify = false;
			if (passedFile.ContainsKey("multiart"))
			{
				Log.Debug("Folder contains multi file");
				artworkverify = true;
			}
			else if (job.IsBusinessCard())
			{
				Log.Debug($"Business card verify passfile:{passedFile.Count}  requirefile:{requiredFile.Count}");
				requiredFile.Remove("_multi");
				artworkverify = passedFile.Count == requiredFile.Count;
			}
			else if (job.Template.Is(Magazine, MagazineSeparate, A4CalendarSelfCover, A4CalendarSeparateCover, PresentationFolder, PresentationFolderNDD))
			{
				artworkverify = passedFile.Count > 0;
			}
			else
			{
				if (passedFile.Count == job.GetRequiredPosition().Count)
				{
					Log.Debug("Required file count = Passed file count");
					artworkverify = true;
				}
				else
				{
					// fail find files, try match from customer file.
					Log.Debug("Fail to find file, try match from customer file");
					passedFile = new Dictionary<string, FileInfo>();
					foreach (var position in job.GetRequiredPosition())
					{
						var artwork = job.GetArtwork(position);
						if (artwork != null && !String.IsNullOrEmpty(artwork.Supplied))
						{
							var files = dir.GetFiles(Path.GetFileNameWithoutExtension(artwork.Supplied) + ".*");
							if (files.Length > 0)
							{
								var stream = files[0].OpenRead();
								var type = LepArtworkScriptFiles.FileDetector.GetFileType(stream);
								stream.Close();
								Log.Debug($"Match file {files[0].Name} {type.ToString()}");
								if (type == ArtworkTypeOptions.TIFF || type == ArtworkTypeOptions.EPS ||
									type == ArtworkTypeOptions.PDF || type == ArtworkTypeOptions.JPG)
								{
									if (!passedFile.ContainsKey(position))
										passedFile.Add(position, files[0]);
								}
							}
						}
					}
					artworkverify = job.GetRequiredPosition().Count == passedFile.Count;
				}
			}

			Log.Debug("End Set Artwork:" + artworkverify.ToString());
			if (artworkverify)
			{
				if (passedFile.Keys.Contains("multiart"))
				{
					passedFile.Remove("front");
					passedFile.Remove("back");
				}


				foreach (var key in passedFile.Keys)
				{
					var artwork = job.GetArtwork(key);
					if (artwork == null)
					{
						artwork = job.AddArtwork(key);
					}
					artwork.Ready = passedFile[key].Name;
					artwork.Preview = artwork.Ready;
					var preview = $"{dir.FullName}/preview_{artwork.Preview}";
					var thumbnail = $"{dir.FullName}/thumbnail_{artwork.Preview}";
					var file = $"{dir.FullName}/{artwork.Ready}";

					CopyFileToParentFolder(file);

					if (artwork.Ready.EndsWith(".tiff", StringComparison.CurrentCultureIgnoreCase) ||
						artwork.Ready.EndsWith(".tif", StringComparison.CurrentCultureIgnoreCase)
					)
					{
						using (var stream = passedFile[key].OpenRead())
						{
							ConvertTiff2Png(stream, preview);
						}
					}

					//if (file.EndsWith(".pdf", StringComparison.CurrentCultureIgnoreCase) == true)
					//{
					//	using (var stream = File.OpenRead(file))
					//	{
					//		ConvertPDFPage1ToPng(file, thumbnail);
					//	}
					//}

					//using (var ms = passedFile[key].OpenRead())
					//{
					//	artwork.ReadyCheckSum = Convert.ToBase64String(SHA256.Create().ComputeHash(ms));
					//}

					//try
					//{
					//	using (var ms = File.OpenRead($"{dir.FullName}/preview_{artwork.Preview}"))
					//	{
					//		artwork.PreviewdCheckSum = Convert.ToBase64String(SHA256.Create().ComputeHash(ms));
					//	}
					//}
					//catch (Exception ex)
					//{
					//	//log.Warn("bool SetArtworks (IJob job) : " + ex.Message);
					//}
				}
			}

			return artworkverify;
		}
		public static void CopyFileToParentFolder(string filePath)
		{
			if (string.IsNullOrEmpty(filePath))
			{
				throw new ArgumentException("File path cannot be null or empty.", nameof(filePath));
			}

			if (!File.Exists(filePath))
			{
				throw new FileNotFoundException("File not found.", filePath);
			}

			var parentDirectory = Directory.GetParent(filePath)?.Parent?.FullName;
			if (parentDirectory == null)
			{
				throw new InvalidOperationException("Parent directory not found.");
			}

			var fileName = Path.GetFileName(filePath);
			var destinationPath = Path.Combine(parentDirectory, fileName);

			File.Copy(filePath, destinationPath, true);

			try
			{
				File.Delete(filePath);
			}
			catch (Exception _)
			{

			}
		}
		public string RenderStaff(IJob job)
		{
			return job.Render(true);
		}

		public IList<int> GetColourOptions(IJobOptionSpecStock stock)
		{
			//Dictionary<int, string> colourName = new Dictionary<int, string>();
			//colourName.Add( 0, "None" );
			//colourName.Add( 1, "One Side" );
			//colourName.Add( 2, "Both sides" );

			IList<int> colourOptions = new List<int>();

			if (stock.JobOptionSpecSize != null && stock.JobOptionSpecSize.JobTemplate != null)
			{
				var jobTYpe = stock.JobOptionSpecSize.JobTemplate;
				foreach (var front in stock.FrontPrintOptions)
				{
					foreach (var back in stock.BackPrintOptions)
					{
						var colourPage = jobTYpe.ColourSide(front, back);
						if (!colourOptions.Contains(colourPage))
						{
							colourOptions.Add(colourPage);
						}
					}
				}
			}
			if (!colourOptions.Any())
			{
				colourOptions.Add(0);
			}
			return colourOptions;
		}

		public string GetColourOptionScript(IJobOptionSpecStock stock)
		{
			var colourName = new Dictionary<int, string>();
			colourName.Add(0, "None");
			colourName.Add(1, "One Side");
			colourName.Add(2, "Both sides");

			IList<int> colourOptions = new List<int>();
			if (stock.JobOptionSpecSize != null && stock.JobOptionSpecSize.JobTemplate != null)
			{
				var jobTYpe = stock.JobOptionSpecSize.JobTemplate;
				foreach (var front in stock.FrontPrintOptions)
				{
					foreach (var back in stock.BackPrintOptions)
					{
						var colourPage = jobTYpe.ColourSide(front, back);
						if (!colourOptions.Contains(colourPage))
						{
							colourOptions.Add(colourPage);
						}
					}
				}
			}

			var sep = " ";
			var sb = new StringBuilder();
			foreach (var pageNum in colourOptions)
			{
				sb.Append(sep)
					.Append("{ id: ").Append(pageNum.ToString())
					.Append(", name: '").Append(colourName[pageNum])
					.Append("' }");
				sep = ", ";
			}
			sb.Append(" ");
			return sb.ToString();
		}

		// cello display

		public IList<CelloOption> SortCelloOption(IList<CelloOption> cellos)
		{
			var newCellos = new List<CelloOption>(cellos);
			newCellos.Sort(delegate (CelloOption a, CelloOption b)
			{
				if (b.CelloBack != None || a.CelloBack != None)
				{
					return a.CelloBack.CompareTo(b.CelloBack);
				}
				else if (b.CelloFront != None || a.CelloFront != None)
				{
					return a.CelloFront.CompareTo(b.CelloFront);
				}
				return 0;
			});
			return newCellos;
		}

		public IList<KeyValuePair<string, string>> GetCelloOptions(int specStockId, bool isNormal = true)
		{
			var specStock = Session.Get<IJobOptionSpecStock>(specStockId);

			var result = new List<KeyValuePair<string, string>>();

			var celloList = specStock.CelloOptions.Select(x => new CelloOption(x.CelloFront, x.CelloBack)).ToList();
			var sortedCelloList = SortCelloOption(celloList);

			foreach (var celloOption in sortedCelloList)
			{
				result.Add(new KeyValuePair<string, string>(
					((int)celloOption.CelloFront).ToString() +
					((int)celloOption.CelloBack).ToString()
					, CelloUtils.GetCelloName(celloOption.CelloFront, celloOption.CelloBack, isNormal)
				));
			}

			return result;
		}

		public bool DoStuffOnFinalPreflight(IJob j)
		{
			if (!j.IsDigital())
				return false;

			return true;
		}

		//public void Save (JobDespatchUpdate update)
		//{
		//    Save<JobDespatchUpdate>(update);
		//}

		private XDocument CreateMacScript(string url)
		{
			var doc =
				XDocument.Parse(
					"<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE plist PUBLIC \"-//Apple//DTD PLIST 1.0//EN\" \"http://www.apple.com/DTDs/PropertyList-1.0.dtd\"><plist version=\"1.0\"><dict><key>URL</key><string/></dict></plist>");
			var el = doc.XPathSelectElement("/plist/dict/string");
			el.Value = url;

			return doc;
		}

		//public JobDespatchUpdate GetJobDespatchUpdate (int id)
		//{
		//    return Get<JobDespatchUpdate>(id);
		//}

		private void CopyStream(Stream input, Stream output)
		{
			var bin = new BufferedStream(input, 20480);
			var bout = new BufferedStream(output, 20480);
			try
			{
				while (true)
				{
					var buffer = new byte[1024];
					var count = bin.Read(buffer, 0, 1024);
					if (count == 0)
					{
						break;
					}
					bout.Write(buffer, 0, count);
				}
			}
			finally
			{
				bout.Close();
			}
		}

		private void ConvertTiff2Png(Stream input, string outfile)
		{

			try
			{
				var src = Image.FromStream(input);
				var ratio = 1.0 * src.Height / (1.0 * src.Width);
				var width = 640;
				var height = (int)(640 * ratio);
				if (ratio > 1.0)
				{
					width = (int)(480 / ratio);
					height = 480;
				}

				var bitmap = new Bitmap(width, height);
				//Bitmap bitmap = new Bitmap( width, height, src.PixelFormat );

				var g = Graphics.FromImage(bitmap);
				g.SmoothingMode = SmoothingMode.HighSpeed;
				g.InterpolationMode = InterpolationMode.Bilinear;
				g.DrawImage(src, 0, 0, 1 + bitmap.Width, 1 + bitmap.Height);

				Stream output = new FileStream(outfile, FileMode.Create);
				try
				{
					bitmap.Save(output, ImageFormat.Png);
				}
				finally
				{
					output.Close();
				}
			}
			catch (Exception e)
			{
				Log.Error("Unable to create preview image: " + outfile + "\n" + e.Message);
			}
		}

		/*
		private void ConvertPDFPage1ToPng(string srcFile, string destFile)
		{

			try
			{
				var dpi = 100;
				destFile = Path.ChangeExtension(destFile, ".{0}.PNG");
				Log.Debug(String.Format("About to inspect pdf page 1 of file {0}  and save to {1}", srcFile, destFile));

				var tmpSrcFile = srcFile;
				var tmpDesFile = destFile;

				using (var rasterizer = new GhostscriptRasterizer())
				{
					var gsdllPath = LepGlobal.Instance.GhostscriptDllPath;
					//Selects the version of dll
					var dll = Environment.Is64BitProcess ? "gsdll64.dll" : "gsdll32.dll";
					var versionInfo = new GhostscriptVersionInfo(new Version(0, 0, 0), Path.Combine(gsdllPath , dll), string.Empty, GhostscriptLicense.GPL);
					rasterizer.Open(srcFile, versionInfo, true);


					var count = Math.Min(rasterizer.PageCount, 2);
					for (var i = 1; i <= count; i++)
					{

						//Constructs output image name
						var pageFilePath = string.Format(destFile, i);

						//Checks if file already exists
						if (File.Exists(pageFilePath))
							File.Delete(pageFilePath);

						//Save the image in .png format
						var img = rasterizer.GetPage(dpi, i);
						img = ResizeImage2(img, 400, 400);
						//img.Save(pageFilePath, ImageFormat.Jpeg);

						using (Bitmap tempImage = new Bitmap(img))
						{
							tempImage.Save(pageFilePath, ImageFormat.Jpeg);
						}
					}
					rasterizer.Close();
				}

			}
			catch (Exception e)
			{
				Log.Error("ThumbError: " + srcFile + "   =  " + e.Message);
			}
		}
		*/



		//private void ConvertPDFPage1ToPng(string srcFile, string destFile)
		//{
		//	destFile = Path.ChangeExtension(destFile, ".%d.PNG");
		//	Log.Debug(String.Format("About to inspect pdf page 1 of file {0}  and save to {1}", srcFile, destFile));

		//	var tmpSrcFile = srcFile;
		//	var tmpDesFile = destFile;

		//	try
		//	{
		//		var executable = LepGlobal.Instance.GhostScriptPath;
		//		var strCommandParameters = $@" -dBATCH -dSAFER -dNOPAUSE -sDEVICE=jpeg -dFirstPage=1 -dLastPage=2 -sOutputFile=""{destFile}"" ""{srcFile}""";
		//		var p = new System.Diagnostics.Process();
		//		p.StartInfo = new ProcessStartInfo()
		//		{
		//			FileName = executable,
		//			WorkingDirectory = Path.GetDirectoryName(executable),
		//			Arguments = strCommandParameters,
		//			UseShellExecute = true,
		//			CreateNoWindow = true,
		//			RedirectStandardOutput = false,
		//		};
		//		p.Start();
		//		if (!p.WaitForExit(15000))
		//		{
		//			p.Kill();
		//			Log.Error("LongThumb: " + srcFile);
		//		}
		//	}
		//	catch (Exception e)
		//	{
		//		Log.Error("ThumbError: " + srcFile + "   =  " + e.Message);
		//	}
		//}


		public static Image ResizeImage2(Image image, int maxWidth, int maxHeight)
		{
			// Get the image's original width and height
			int originalWidth = image.Width;
			int originalHeight = image.Height;

			// To preserve the aspect ratio
			float ratioX = (float)maxWidth / (float)originalWidth;
			float ratioY = (float)maxHeight / (float)originalHeight;
			float ratio = Math.Min(ratioX, ratioY);

			float sourceRatio = (float)originalWidth / originalHeight;

			// New width and height based on aspect ratio
			int newWidth = (int)(originalWidth * ratio);
			int newHeight = (int)(originalHeight * ratio);

			// Convert other formats (including CMYK) to RGB.
			Bitmap newImage = new Bitmap(newWidth, newHeight, PixelFormat.Format24bppRgb);

			// Draws the image in the specified size with quality mode set to HighQuality
			using (Graphics graphics = Graphics.FromImage(newImage))
			{
				graphics.CompositingQuality = CompositingQuality.HighQuality;
				graphics.InterpolationMode = InterpolationMode.HighQualityBicubic;
				graphics.SmoothingMode = SmoothingMode.HighQuality;
				graphics.DrawImage(image, 0, 0, newWidth, newHeight);
			}

			return newImage;
		}

		//private void ConvertPDFPage1ToPng0(string srcFile, string destFile)
		//{
		//	return;

		//	try
		//	{
		//		destFile = Path.ChangeExtension(destFile, ".%d.PNG");
		//		Log.Debug(String.Format("About to inspect pdf page 1 of file {0}  and save to {1}", srcFile, destFile));

		//		var tmpSrcFile = srcFile;
		//		var tmpDesFile = destFile;

		//		//var settings = new GhostscriptSharp.GhostscriptSettings()
		//		//{
		//		//	Device = GhostscriptSharp.Settings.GhostscriptDevices.png16m,

		//		//	Resolution = new System.Drawing.Size(96, 96),
		//		//	Size = new GhostscriptSharp.Settings.GhostscriptPageSize() { Manual = new System.Drawing.Size(300, 300) }
		//		//};
		//		//var page = new GhostscriptSharp.Settings.GhostscriptPages() { Start = 1, End = 2, AllPages = false };

		//		//settings.Page.AllPages = false;
		//		//settings.Page.Start = 1;
		//		//settings.Page.End = 2;

		//		//GhostscriptSharp.GhostscriptWrapper.GeneratePageThumbs(tmpSrcFile, tmpDesFile, 1, 2, 100, 100);

		//		////GhostscriptSharp.GhostscriptWrapper.GenerateOutput(tmpSrcFile, tmpDesFile, settings);
		//		try
		//			{

		//				GhostscriptSharp.GhostscriptWrapper.GeneratePageThumbs(tmpSrcFile, tmpDesFile, 1, 2, 100, 100);
		//			}
		//			catch (Exception e)
		//			{
		//				Log.Error("0 Unable to create page1 thumbnail: " + srcFile + "   =  " + e.Message);
		//			}

		//		//Thread myThread = new Thread(() =>
		//		//{

		//		//});
		//		//myThread.Start();


		//		//Task.Run(() => GhostscriptSharp.GhostscriptWrapper.GeneratePageThumbs(tmpSrcFile, tmpDesFile, 1, 2, 100, 100));

		//	}
		//	catch (Exception e)
		//	{
		//		Log.Error("1 Unable to create page1 thumbnail: " + srcFile + "   =  " + e.Message);
		//	}
		//}

		/*
		private void ConvertPDFPage1ToPng(string srcFile, string destFile)
		{
			try
			{

				Image RenderPage(PDFLibNet.PDFWrapper doc, int page)
				{
					doc.CurrentPage = page + 1;
					doc.CurrentX = 0;
					doc.CurrentY = 0;

					doc.RenderPage(IntPtr.Zero);

					// create an image to draw the page into
					var buffer = new Bitmap(doc.PageWidth, doc.PageHeight);
					doc.ClientBounds = new Rectangle(0, 0, doc.PageWidth, doc.PageHeight);
					using (var g = Graphics.FromImage(buffer))
					{
						var hdc = g.GetHdc();
						try
						{
							doc.DrawPageHDC(hdc);
						}
						finally
						{
							g.ReleaseHdc();
						}
					}
					return buffer;
				}

				Bitmap ResizeImage(Image source, Size size)
				{
					var scale = Math.Min(size.Width / (double)source.Width, size.Height / (double)source.Height);
					var bmp = new Bitmap((int)(source.Width * scale), (int)(source.Height * scale));

					using (var graph = Graphics.FromImage(bmp))
					{
						graph.InterpolationMode = InterpolationMode.HighQualityBicubic;
						graph.CompositingQuality = CompositingQuality.HighQuality;
						graph.SmoothingMode = SmoothingMode.AntiAlias;

						graph.DrawImage(source, 0, 0, bmp.Width, bmp.Height);
					}
					return bmp;
				}

				destFile = Path.ChangeExtension(destFile, ".{0}.PNG");
				Log.Debug(String.Format("About to inspect pdf page 1 of file {0}  and save to {1}", srcFile, destFile));

				var tmpSrcFile = srcFile;
				var tmpDesFile = destFile;

				PDFLibNet.PDFWrapper _pdfDoc = new PDFLibNet.PDFWrapper();
				_pdfDoc.LoadPDF(srcFile);

				for (int i = 0; i < _pdfDoc.PageCount; i++)
				{
					Image img = RenderPage(_pdfDoc, i);
					Image img2 = ResizeImage(img, new Size(_pdfDoc.PageWidth, _pdfDoc.PageHeight));
					img2.Save(string.Format(destFile, (i + 1)), ImageFormat.Jpeg);

					img.Dispose();
					img2.Dispose();

					if (i == 1)
						break;
				}

				_pdfDoc.Dispose();
			}
			catch (Exception e)
			{
				Log.Error("Unable to create page1 thumbnail: " + srcFile + "   =  " + e.Message);
			}


		}
		*/
		//public IList<int> GetDispatchedJobId ()
		//{
		//    return Session.CreateCriteria(typeof(JobDespatchUpdate))
		//        .Add(Restrictions.IsNull("Response"))
		//        .SetProjection(Projections.Property("JobId"))
		//        .List<int>();
		//}

		#region job spec import

		public string ImportJobOption()
		{
			var result = "";
			TextReader reader = null;
			var parser = new JobOptionCsvReader();

			//var ts = Session.CreateCriteria(typeof(IJobTemplate),"t")
			//			.CreateAlias("t.SizeOptions", "size").SetFetchMode("t.SizeOptions", FetchMode.Eager)
			//			.CreateAlias("size.StockOptions", "stock").SetFetchMode("size.StockOptions", FetchMode.Eager)
			//			.AddOrder(Order.Asc("Name"))
			//			.List<IJobTemplate>();

			Session.SetBatchSize(1000);
			Session.FlushMode = FlushMode.Manual;
			//
			//parser.Templates = ListAllTemplates();

			var clearSQL = @"
            BEGIN TRANSACTION;
            TRUNCATE TABLE [dbo].[JobOptionSpecCello];
            TRUNCATE TABLE [dbo].[JobOptionSpecFold];
            TRUNCATE TABLE [dbo].[JobOptionSpecBackPrint];
            TRUNCATE TABLE [dbo].[JobOptionSpecFrontPrint];
            TRUNCATE TABLE [dbo].[JobOptionSpecSize];
            TRUNCATE TABLE [dbo].[JobOptionSpecStock];
            COMMIT TRANSACTION;
            ";
			Session.CreateSQLQuery(clearSQL).ExecuteUpdate();
			//parser.Templates = (from template in Session.Query<IJobTemplate>().FetchMany(x => x.SizeOptions)
			//					select template).ToList();
			parser.Templates = ListAllTemplates();

			try
			{
				var messages = new List<string>();
				var cachesb = new StringBuilder();
				RemoveAllJobOptionSpec();
				var folds = GetFolds(messages);
				var range = GetRange(messages);
				var print = GetPrint(messages);
				reader = LepGlobal.Instance.ImportSpecType.OpenText();
				parser.SpecFolds = folds;
				parser.SpecPrint = print;
				parser.SpecRange = range;
				parser.CacheScript = cachesb;
				parser.JobApplication = this;

				Session.SetBatchSize(100);

				//foreach (var temp in parser.Templates) {
				//    temp.SizeOptions.Clear();
				//  }

				//
				Session.FlushMode = FlushMode.Commit;
				RunWithTransaction(Session, delegate
				{
					try
					{
						parser.Parse(reader);

						foreach (var template in parser.Templates)
						{
							Session.SaveOrUpdate(template);
							//break;
						}
					}
					catch (CsvParseException ex)
					{
						LogError(parser, messages);
						parser.NumRowAdded = 0;
						// throw ex;
					}
					catch (CsvException ex)
					{
						LogError(parser, messages);
						parser.NumRowAdded = 0;
						//  throw ex;
					}
					catch (Exception ex)
					{
						//Catch any other exceptions
						Log.Error(ex.Message, ex);
						parser.NumRowAdded = 0;
						//  throw ex;
					}
				});
				//
				Session.FlushMode = FlushMode.Auto;

				var resultLine = "Import completed.";
				var errorLine = String.Format("Errors  : {0}", messages.Count.ToString());
				Log.Debug(resultLine);
				Log.Debug(errorLine);
				result += resultLine + "\r\n";
				result += errorLine + "\r\n";
				foreach (var msg in messages)
				{
					result += msg + "\r\n";
				}
				//GenerateCacheOptionFile(cachesb);
			}
			catch (Exception ex)
			{
				Log.Error("Error in Import Job Option  :" + ex.Message, ex);
				result = "Failed reading import files." + ex.Message;
				if (parser.Errors != null && parser.Errors.Any())
					result += "\n" + parser.Errors.Select(x => x.Message).Aggregate((m, n) => m + "\n" + n);
			}
			finally
			{
				if (reader != null)
				{
					reader.Close();
				}
				foreach (var template in parser.Templates)
				{
					Session.Evict(template);
				}

				var sessionFactory = Session.SessionFactory;
				sessionFactory.EvictQueries();
				foreach (var collectionMetadata in sessionFactory.GetAllCollectionMetadata())
					sessionFactory.EvictCollection(collectionMetadata.Key);
				foreach (var classMetadata in sessionFactory.GetAllClassMetadata())
					sessionFactory.EvictEntity(classMetadata.Key);
			}
			return result;
		}

		private void RemoveAllJobOptionSpec()
		{
			var list = Session.CreateCriteria(typeof(IJobOptionSpecSize))
				.List<IJobOptionSpecSize>();
			foreach (var jobOptionSpecSize in list)
			{
				Delete<IJobOptionSpecSize>(jobOptionSpecSize);
			}

			var list1 = Session.CreateCriteria(typeof(IJobOptionSpecStock))
				.List<IJobOptionSpecStock>();
			foreach (var jobOptionSpecStock in list1)
			{
				var i = jobOptionSpecStock.BackPrintOptions.Count;
				i = jobOptionSpecStock.FrontPrintOptions.Count;
				i = jobOptionSpecStock.CelloOptions.Count;
				i = jobOptionSpecStock.FoldOptions.Count;
				Delete<IJobOptionSpecStock>(jobOptionSpecStock);
			}
		}

		private IDictionary<int, IList<IPaperSize>> GetFolds(List<string> messages)
		{
			TextReader reader = LepGlobal.Instance.ImportSpecFolds.OpenText();
			var parser = new FoldOptionsCsvReader();
			parser.JobApplication = this;
			try
			{
				parser.Parse(reader);
			}
			catch (CsvException)
			{
				LogError(parser, messages);
			}
			catch (Exception ex)
			{
				//Catch any other exceptions
				Log.Error(ex.Message, ex);
			}
			finally
			{
				reader.Close();
			}
			return parser.Result;
		}

		private IDictionary<int, IJobOptionSpecQuantity> GetRange(List<string> messages)
		{
			TextReader reader = LepGlobal.Instance.ImportSpecRange.OpenText();
			var parser = new QuantityOptionCsvReader();
			parser.JobApplication = this;
			try
			{
				parser.Parse(reader);
			}
			catch (CsvException)
			{
				LogError(parser, messages);
			}
			catch (Exception ex)
			{
				//Catch any other exceptions
				Log.Error(ex.Message, ex);
			}
			finally
			{
				reader.Close();
			}
			return parser.Result;
		}

		private IDictionary<int, IJobOptionSpecStock> GetPrint(List<string> messages)
		{
			TextReader reader = LepGlobal.Instance.ImportSpecPrint.OpenText();
			var parser = new JobOptionSpecStockCsvReader();
			parser.JobApplication = this;
			try
			{
				parser.Parse(reader);
			}
			catch (CsvException)
			{
				LogError(parser, messages);
			}
			catch (Exception ex)
			{
				//Catch any other exceptions
				Log.Error(ex.Message, ex);
			}
			finally
			{
				reader.Close();
			}
			return parser.Result;
		}

		public bool CheckJobTypeExist(string name)
		{
			var criteria = Session.CreateCriteria(typeof(IJobTemplate))
				.Add(Eq("Name", name))
				.SetProjection(Projections.RowCount());
			return (int)criteria.List()[0] != 0;
		}

		public bool CheckStockExist(string name)
		{
			var criteria = Session.CreateCriteria(typeof(IStock))
				.Add(Eq("Name", name))
				.SetProjection(Projections.RowCount());
			return (int)criteria.List()[0] != 0;
		}

		public bool CheckPageSizeExist(string name)
		{
			var criteria = Session.CreateCriteria(typeof(IPaperSize))
				.Add(Eq("Name", name))
				.SetProjection(Projections.RowCount());
			return (int)criteria.List()[0] != 0;
		}

		private void GenerateCacheOptionFile(StringBuilder cacheSb)
		{
			//cacheSb.Append("var papersizes = { ");
			//var sizeData = " {0} : {{ name: '{1}', height: {2}, width: {3} }}";
			//var sizesSb = new StringBuilder();
			//foreach (var si in ListPaperSize()) {
			//    if (sizesSb.Length > 0) {
			//        sizesSb.Append(", ");
			//    }
			//    sizesSb.AppendLine(String.Format(sizeData, si.Id, si.Name, si.Size.Height, si.Size.Width));
			//}
			//cacheSb.Append(sizesSb.ToString());
			//cacheSb.Append("};");

			//cacheSb.Append("var SpecType = { ");
			//sizesSb = new StringBuilder();
			//foreach (var j in ListAllTemplates()) {
			//    if (sizesSb.Length > 0) {
			//        sizesSb.Append(", ");
			//    }
			//    var sizeLst = String.Empty;
			//    if (j.Id == Convert.ToInt32(Custom)) {
			//        sizeLst = String.Join(",", ListPaperSize().Select(s => s.Id.ToString()).ToArray());
			//    } else {
			//        sizeLst = String.Join(",", j.SizeOptions.Select(s => s.PaperSize.Id.ToString()).ToArray());
			//    }
			//    sizesSb.AppendLine(String.Format(" {0} : [{1}]", j.Id, sizeLst));
			//}
			//cacheSb.Append(sizesSb.ToString());
			//cacheSb.Append("};");

			////if (!ConfigurationApplication.CacheDirectory.Exists) {
			////    ConfigurationApplication.CacheDirectory.Create();
			////}
			//var previousFile = ConfigurationApplication.GetValue(Configuration.JobOptionCacheScript);
			//var currentFile = String.Format("joboptioncache-{0}.js", DateTime.Now.Ticks);

			//FileStream fs = null;
			//StreamWriter sw = null;
			//try {
			//    var f = new FileInfo(String.Format("{0}/{1}", ConfigurationApplication.CacheDirectory, currentFile));
			//    fs = f.Open(FileMode.Create);
			//    sw = new StreamWriter(fs);
			//    sw.Write(cacheSb.ToString());
			//    ConfigurationApplication.SetValue(Configuration.JobOptionCacheScript, currentFile);
			//    if (!String.IsNullOrEmpty(previousFile)) {
			//        var previous =
			//            new FileInfo(String.Format("{0}/{1}", ConfigurationApplication.CacheDirectory, previousFile));
			//        if (previous.Exists) {
			//            previous.Delete();
			//        }
			//    }
			//} finally {
			//    if (sw != null) {
			//        sw.Close();
			//    }
			//    if (fs != null) {
			//        fs.Close();
			//    }
			//}
		}

		////CR14
		///// <summary>
		///// get jobs folder under "Orders" and get the size plus
		///// if job in run, then for all runs gets the size of the jobs folder under Run Folder
		/////
		///// </summary>
		///// <param name="job"></param>
		///// <returns></returns>
		//public long JobSpaceConsumed( IJob job )
		//{
		//    long totalSize = 0;
		//    long size = 0;
		//    // get jobs root folder under "Orders" and get the size
		//    DirectoryInfo diInOrderDirectory = ConfigurationApplication.ArtworkDirectory( job, false);
		//    size = diInOrderDirectory.Size();
		//    totalSize += size;

		//    if (size > 0) {
		//        Log.Debug String.Format( "folder {0} size  {1}", diInOrderDirectory.FullName, size ) );
		//    }

		//    // if job in run, then for all runs get the used space (maybe only the last run is needed, consuly IH)
		//    if (job.Status > JobStatusOptions.InRun) {
		//        foreach (var r in job.Runs) {
		//            DirectoryInfo diInRunDirectory = ConfigurationApplication.ArtworkDirectory( r, job );
		//            size  = diInOrderDirectory.Size();
		//            totalSize += size;
		//            if (size > 0) {
		//                Log.Debug String.Format( "folder {0}    size  {1}", diInOrderDirectory.FullName, size ) );
		//            }
		//        }
		//    }

		//    if (size > 0) {
		//        Log.Debug String.Format( "JOB {0}    size  {1}", job.JobNr, totalSize ) );
		//    }

		//    return totalSize;
		//}

		////CR14
		///// <summary>
		///// Deletes a jobs folder under order and run and returns space reclaimed
		///// </summary>
		///// <param name="job"></param>
		///// <returns></returns>
		//public long JobDeleteFiles( IJob job )
		//{
		//    long totalSize = 0;
		//    long size = 0;
		//    // get jobs root folder under "Orders" and get the size
		//    DirectoryInfo diInOrderDirectory = ConfigurationApplication.ArtworkDirectory( job, false );
		//    size = diInOrderDirectory.Size();
		//    totalSize += size;

		//    if (size > 0) {
		//        if (diInOrderDirectory.Exists && false) {
		//            diInOrderDirectory.Delete( true );
		//        }
		//        Log.Debug String.Format( "Deleted folder {0} size  {1}", diInOrderDirectory.FullName, size ) );
		//    }

		//    // if job in run, then for all runs get the used space (maybe only the last run is needed, consuly IH)
		//    if (job.Status > JobStatusOptions.InRun) {
		//        foreach (var r in job.Runs) {
		//            DirectoryInfo diInRunDirectory = ConfigurationApplication.ArtworkDirectory( r, job );
		//            size = diInOrderDirectory.Size();
		//            totalSize += size;
		//            if (size > 0) {
		//                if (diInRunDirectory.Exists && false){
		//                    diInRunDirectory.Delete( true );
		//                }
		//                Log.Debug String.Format( "Deleted folder {0}    size  {1}", diInOrderDirectory.FullName, size ) );
		//            }
		//        }
		//    }

		//    //if (size > 0) {
		//        Log.Debug String.Format( "JOB {0}  reclaimed  {1}", job.JobNr, totalSize ) );
		//    //}

		//    return totalSize;
		//}

		private void LogError(BaseCsvReader parser, List<string> messages)
		{
			if (parser.Errors != null && parser.Errors.Count > 0)
			{
				//CSV parsing errors
				foreach (var rowError in parser.Errors)
				{
					var details = "";
					if (rowError.InnerException != null)
					{
						details = " (" + rowError.InnerException.Message + ")";
					}
					Log.Debug(rowError.Message + details);
					messages.Add(rowError.Message + details);
				}
			}
		}

		#endregion job spec import

		public string PositionToDisplayLabel(string str)
		{
			string display;
			switch (str)
			{
				case "front_outside":
					display = "Outside front";
					break;

				case "back_outside":
					display = "Outside back";
					break;

				case "front_inside":
					display = "Inside front";
					break;

				case "back_inside":
					display = "Inside back";
					break;

				case "front":
					display = "Front";
					break;

				case "back":
					display = "Back";
					break;

				case "diecut":
					display = "Custom die cut";
					break;

				default:
					display = $"Page {int.Parse(str)}";
					break;
			}
			return display;
		}

		public void SetReadyArt(IJob job, IArtwork art, string filename)
		{
			//string name = filename;
			//string extension = "";
			//if (filename.LastIndexOf('.') != -1) {
			//    name = filename.Substring(0, filename.LastIndexOf('.'));
			//    extension = filename.Substring(filename.LastIndexOf('.') + 1);
			//}
			string name = Path.GetFileNameWithoutExtension(filename);
			name = job.Id + "_" + name;
			name = name.Replace("_multi", "").Replace("_ft", "").Replace("_bk", "");
			string extension = Path.GetExtension(filename).Replace(".", "");
			art.Ready = FileNameModify(PositionRename(job, name, extension, art.Position, false));
			art.Preview = FileNameModify(PositionRename(job, name, "PNG", art.Position, false));
		}

		public void SetSupplyArt(IJob job, IArtwork art, string filename)
		{
			//string name = filename;
			//string extension = "";
			//if (filename.LastIndexOf('.') != -1) {
			//    name = filename.Substring(0, filename.LastIndexOf('.'));
			//    extension = filename.Substring(filename.LastIndexOf('.') + 1);
			//}

			string name = Path.GetFileNameWithoutExtension(filename);

			name = job.Id + "_" + name;
			name = name.Replace("_multi", "").Replace("_ft", "").Replace("_bk", "");
			string extension = Path.GetExtension(filename).Replace(".", "");

			switch (extension.ToUpper())
			{
				case "TIFF":
					art.Type = ArtworkTypeOptions.TIFF;
					break;

				case "TIF":
					art.Type = ArtworkTypeOptions.TIFF;
					break;

				case "JPG":
					art.Type = ArtworkTypeOptions.JPG;
					break;

				case "PDF":
					art.Type = ArtworkTypeOptions.PDF;
					break;

				case "EPS":
					art.Type = ArtworkTypeOptions.EPS;
					break;

				case "CDR":
					art.Type = ArtworkTypeOptions.Corel;
					break;

				default:
					art.Type = ArtworkTypeOptions.Misc;
					break;
			}

			if (extension.ToUpper() == "ZIP")
			{
				art.Supplied = filename;
			}
			else
			{
				art.Supplied = FileNameModify(PositionRename(job, name, extension, art.Position, true));
			}
		}

		public void RemoveArtwork(IJob job, IArtwork art)
		{
			ArchieveArtwork(job);
			if (!string.IsNullOrEmpty(art?.Supplied)) // delete old front/back/cover/text in the same pos
			{
				var fn = Path.GetFileNameWithoutExtension(art.Supplied);
				var jobFolderFromCust = LepGlobal.Instance.ArtworkDirectory(job, true);
				var listClear = jobFolderFromCust.GetFiles($"*{fn}*.*", SearchOption.AllDirectories).ToList();
				listClear.ForEach(_ => _.Delete());
				if (art.Supplied.Contains("_ft.") || art.Supplied.Contains("_bk."))
				{
					listClear = jobFolderFromCust.GetFiles($"{job.Id}_multi.*", SearchOption.AllDirectories).ToList();
					listClear.ForEach(_ => _.Delete());
				}
			}

			job.Artworks.Remove(art);
		}


		private string PositionRename(IJob job, string file, string extension, string position, bool customForce)
		{
			file = Regex.Replace(file, @"[\.]+", "_", RegexOptions.Compiled);

			switch (position)
			{
				case "front":
					if (job.BackPrinting != JobPrintOptions.Unprinted)
					{
						position = "ft";
					}
					else
					{
						position = "fto";
					}
					break;

				case "back":
					if (job.FrontPrinting != JobPrintOptions.Unprinted)
					{
						position = "bk";
					}
					else
					{
						position = "bko";
					}
					break;

				case "diecut":
					position = "die";
					break;

				case "multiart":
					if (customForce)
					{
						if (job.FrontPrinting == JobPrintOptions.Unprinted && job.BackPrinting != JobPrintOptions.Unprinted)
						{
							position = "bko";
						}
						else if (job.FrontPrinting != JobPrintOptions.Unprinted && job.BackPrinting == JobPrintOptions.Unprinted)
						{
							position = "fto";
						}
						else
						{
							position = "multi";
						}
					}
					else
					{
						position = "multi";
					}
					break;

				default:
					if (position.Length == 1)
					{
						position = "0" + position;
					}
					break;
			}

			if (string.IsNullOrEmpty(extension))
			{
				return $"{file}_{position}";
			}
			else
			{
				return $"{file}_{position}.{extension}";
			}
		}

		private string FileNameModify(string filename)
		{
			filename = Regex.Replace(filename, @"[^A-Za-z_\d\-\(\)\.]", "").Trim();
			if (filename.StartsWith("."))
			{
				filename = filename.Remove(0, 1);
			}
			//if (filename.Length > 20)
			//{
			//	if (filename.LastIndexOf('_') > -1)
			//	{
			//		int extensionLength = filename.Length - filename.LastIndexOf('_');
			//		string truncatename = filename.Substring(0, 20 - extensionLength);
			//		return truncatename + filename.Substring(filename.LastIndexOf('_'));
			//	}
			//}
			return filename;
		}

		public IList<MailHouse> GetBrohureMailHouses()
		{
			return Session.CreateCriteria(typeof(MailHouse)).List<MailHouse>();
		}

		public void MergeFrontAndBackArtwork(IJob job)
		{
			#region LORD-924	Merge customer art files into one multi page file per job
			var n_ft = job.GetArtwork("front")?.Supplied;
			var n_bk = job.GetArtwork("back")?.Supplied;

			if (!string.IsNullOrEmpty(n_ft) && !string.IsNullOrEmpty(n_bk))
			{
				try
				{
					var executable = LepGlobal.Instance.PdfTkPath;
					var dir = LepGlobal.Instance.ArtworkDirectory(job, true);
					var f = dir.FullName;
					var cn = $"{job.Id}_multi.pdf";

					var strCommandParameters = $@" ""{f}\{n_ft}"" ""{f}\{n_bk}"" cat output ""{f}\{cn}""";

					var p = new System.Diagnostics.Process();
					p.StartInfo = new ProcessStartInfo()
					{
						FileName = executable,
						WorkingDirectory = Path.GetDirectoryName(executable),
						Arguments = strCommandParameters,
						UseShellExecute = true,
						CreateNoWindow = true,
						RedirectStandardOutput = false,
					};
					p.Start();
					p.WaitForExit();


					//try
					//{
					//	File.Delete($"{f}\\{n_ft}");
					//	File.Delete($"{f}\\{n_bk}");
					//}
					//catch { }

				}
				catch (Exception ex)
				{
					Log.Error("Error combining pdf");
				}
			}

			#endregion

			#region LORD-984 Merge ready tiff files if they exist

			var n_ft_tiff = job.GetArtwork("front")?.Ready;
			var n_bk_tiff = job.GetArtwork("back")?.Ready;

			if (!string.IsNullOrEmpty(n_ft_tiff) && !n_ft_tiff.Contains("_ft.tif")) n_ft_tiff = "";
			if (!string.IsNullOrEmpty(n_bk_tiff) && !n_bk_tiff.Contains("_bk.tif")) n_bk_tiff = "";


			if (!string.IsNullOrEmpty(n_ft_tiff) && !string.IsNullOrEmpty(n_ft_tiff))
			{
				try
				{
					var executable = LepGlobal.Instance.LibTiffPath;
					var dir = LepGlobal.Instance.ArtworkDirectory(job, false);
					var f = dir.FullName;

					// convert front tiff to pdf
					var strCommandParameters = $@" ""{f}\{n_ft_tiff}"" -z -o ""{f}\_1_.pdf""";
					var p = new System.Diagnostics.Process();
					p.StartInfo = new ProcessStartInfo()
					{
						FileName = executable,
						WorkingDirectory = Path.GetDirectoryName(executable),
						Arguments = strCommandParameters,
						UseShellExecute = true,
						CreateNoWindow = true,
						RedirectStandardOutput = false,
					};
					p.Start();
					p.WaitForExit();

					// convert back tiff to pdf
					strCommandParameters = $@" ""{f}\{n_bk_tiff}"" -z -o ""{f}\_2_.pdf""";
					var p2 = new System.Diagnostics.Process();
					p2.StartInfo = new ProcessStartInfo()
					{
						FileName = executable,
						WorkingDirectory = Path.GetDirectoryName(executable),
						Arguments = strCommandParameters,
						UseShellExecute = true,
						CreateNoWindow = true,
						RedirectStandardOutput = false,
					};
					p2.Start();
					p2.WaitForExit();
				}
				catch (Exception ex)
				{
					Log.Error("Error convert tiff to pdf");
				}
				try
				{
					var executable = LepGlobal.Instance.PdfTkPath;
					var dir = LepGlobal.Instance.ArtworkDirectory(job, false);
					var f = dir.FullName;
					var cn = $"{job.Id}_multi.pdf";

					var strCommandParameters = $@" ""{f}\_1_.pdf"" ""{f}\_2_.pdf"" cat output ""{f}\{cn}""";
					var p = new System.Diagnostics.Process();
					p.StartInfo = new ProcessStartInfo()
					{
						FileName = executable,
						WorkingDirectory = Path.GetDirectoryName(executable),
						Arguments = strCommandParameters,
						UseShellExecute = true,
						CreateNoWindow = true,
						RedirectStandardOutput = false,
					};
					p.Start();
					p.WaitForExit();

					File.Delete($@"{f}\_1_.pdf");
					File.Delete($@"{f}\_2_.pdf");
				}
				catch (Exception ex)
				{
					Log.Error("Error combining pdf");
				}
			}


			#endregion
		}


	}
}
