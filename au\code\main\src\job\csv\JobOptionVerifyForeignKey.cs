using lumen.csv;
using System;
using System.Collections.Generic;
using System.Linq;

namespace lep.job.csv
{
    public class JobOptionVerifyForeignKey : BaseHandler
    {
        private Dictionary<string, int> fieldMap;
        private IJobApplication jobApp;
        private int rowNumber = 0;
        private bool seenFirstRow = false;
        private IDictionary<int, IList<IPaperSize>> specFolds;
        private IDictionary<int, IJobOptionSpecStock> specPrint;
        private IDictionary<int, IJobOptionSpecQuantity> specRange;
        private IList<IJobTemplate> templates;

        public IDictionary<int, IJobOptionSpecStock> SpecPrint
        {
            set { specPrint = value; }
        }

        public IDictionary<int, IJobOptionSpecQuantity> SpecRange
        {
            set { specRange = value; }
        }

        public IDictionary<int, IList<IPaperSize>> SpecFolds
        {
            set { specFolds = value; }
        }

        public IJobApplication JobApplication
        {
            set { jobApp = value; }
        }

        public IList<IJobTemplate> Templates
        {
            set { templates = value; }
        }

        public override void StartDocument()
        {
            fieldMap = new Dictionary<string, int>();
        }

        public override void EndDocument()
        {
        }

        public override void RowData(string[] values)
        {
            if (!seenFirstRow)
            {
                seenFirstRow = true;
                //Map column names to column numbers
                fieldMap = new Dictionary<string, int>(values.Length);
                for (var i = 0; i < values.Length; i++)
                {
                    fieldMap[values[i].ToLower()] = i;
                }
            }
            else
            {
                rowNumber++;
				var jt = values[fieldMap["job-type"]];
				var exist = templates.Any(tmp => tmp.Name == jt );
                
                if (!exist)
                {
                    throw new CsvParseException(
                        string.Format("Spec Type File Input row {0} , '{1}', is an invalid job type", rowNumber,
                            values[fieldMap["job-type"]]));
                }
                // todo: ih these 2 check causing lots of sql query, change to something else later

                if (!jobApp.CheckPageSizeExist(values[fieldMap["size"]]))
                {
                    throw new CsvParseException(
                        string.Format("Spec Type File Input row {0} ,'{1}', is an invalid page size", rowNumber,
                            values[fieldMap["size"]]));
                }
                if (!jobApp.CheckStockExist(values[fieldMap["stock"]]))
                {
                    throw new CsvParseException(string.Format("Spec Type File Input row {0} ,'{1}' is an invalid stock",
                        rowNumber, values[fieldMap["stock"]]));
                }

                if ("-" != values[fieldMap["fid"]])
                {
                    var fid = Convert.ToInt32(values[fieldMap["fid"]]);
                    if (!specFolds.ContainsKey(fid))
                    {
                        throw new CsvParseException(
                            string.Format("Spec Type File Input row {0} fid ,{1}, is not existing", rowNumber,
                                values[fieldMap["fid"]]));
                    }
                }
                if ("-" != values[fieldMap["rid"]])
                {
                    var rid = Convert.ToInt32(values[fieldMap["rid"]]);
                    if (!specRange.ContainsKey(rid))
                    {
                        throw new CsvParseException(
                            string.Format("Spec Type File Input row {0} rid ,{1}, is not existing", rowNumber,
                                values[fieldMap["rid"]]));
                    }
                }
                if ("-" != values[fieldMap["pid"]])
                {
                    var pid = Convert.ToInt32(values[fieldMap["pid"]]);
                    if (!specPrint.ContainsKey(pid))
                    {
                        throw new CsvParseException(
                            string.Format("Spec Type File Input row {0} pid ,{1}, is not existing", rowNumber,
                                values[fieldMap["pid"]]));
                    }
                }
            }
            base.RowData(values);
        }
    }
}
