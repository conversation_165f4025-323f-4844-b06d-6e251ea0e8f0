using lep.configuration;
using lep.job;
using System;
using System.Drawing;
using System.Drawing.Printing;

namespace lep.despatch.impl.label
{
    /// <summary>
    /// Unified Further Processing Label that can handle all label types using the data extractor
    /// </summary>
    public class UnifiedFurtherProcessingLabel : BaseFurtherProcessingLabel
    {
        private FurtherProcessingLabelData _labelData;
        private LabelType _labelType;

        #region Constructors

        public UnifiedFurtherProcessingLabel(LabelType labelType = LabelType.FurtherProcessing)
        {
            _labelType = labelType;
        }

        public UnifiedFurtherProcessingLabel(IJob job, LabelType labelType, string printerAndTray, IConfigurationApplication configurationApplication, string filename)
        {
            _labelType = labelType;
            this.Job = job;
            this.PrinterAndTray = printerAndTray;
            this.ConfigurationApplication = configurationApplication;
            this.PrintFileName = filename;
        }

        #endregion Constructors

        #region Properties

        public LabelType LabelType
        {
            get => _labelType;
            set
            {
                _labelType = value;
                if (Job != null)
                {
                    FormatPrintContent(); // Refresh data when label type changes
                }
            }
        }

        public FurtherProcessingLabelData LabelData => _labelData;

        #endregion Properties

        #region Protected Methods

        protected override void OnPrintPage(PrintPageEventArgs e)
        {
            base.OnPrintPage(e);
            var g = e.Graphics;

            // Set graphics unit to millimeters
            g.PageUnit = GraphicsUnit.Millimeter;

            SetupGraphics(g);

            pageNumber++;
            if (pageNumber == 1)
            {
                #region title

                var title = _labelData?.Title ?? "Further Processing";
                g.DrawString(title, titleFont, Brushes.Black, rTitle, titleAlignment);

                #endregion title

                #region print processing instruction in top part

                var strMiddle = _labelData?.ProcessingText1 ?? "";
                var hMiddle = (int)g.MeasureString(strMiddle, defaultFont).Height;
                var rMiddle = new Rectangle(left, top + hTitle + 10, width, (270 - top - hTitle - 10));
                g.DrawString(strMiddle, defaultFont, Brushes.Black, rMiddle, middleFormat);

                // For DPC and WideFormat, also draw ProcessingText2 if available
                if (!string.IsNullOrEmpty(_labelData?.ProcessingText2))
                {
                    var strRight = _labelData.ProcessingText2;
                    var hRight = (int)g.MeasureString(strRight, defaultFont).Height;
                    
                    RectangleF rright;
                    if (_labelType == LabelType.DPCProcessing)
                    {
                        rright = new RectangleF(50f, 28f, 49f, 50f); // Right side processing text for DPC
                    }
                    else
                    {
                        rright = new Rectangle(width / 2, top + hTitle + 50, width, 200); // WideFormat positioning
                    }
                    
                    g.DrawString(strRight, defaultFont, Brushes.Black, rright, rightFormat);
                }

                #endregion print processing instruction in top part

                #region draw basic info like cust, job, order

                var basicJobInformation = _labelData?.BasicInformationText ?? "";
                var hBasicInfo = (int)g.MeasureString(basicJobInformation, defaultFont).Height;
                g.DrawString(basicJobInformation, defaultFont, Brushes.Black, rBasicInfoTxt, middleFormat);

                #endregion draw basic info like cust, job, order

                // Draw common elements
                if (_labelType == LabelType.FurtherProcessing)
                {
                    DrawRoundedCorndersBox(g); // Only for standard further processing
                }
                
                DrawEDD(g);
                DrawThumbnail(g);
                DrawBarcode(g);
            }

            DrawSpecialInstructions(e, g);

            if (pageNumber == 2)
            {
                e.HasMorePages = false;
            }
        }

        #endregion Protected Methods

        #region Public Methods

        public override void FormatPrintContent()
        {
            try
            {
                if (Job == null) return;

                // Use the data extractor to get all job information
                _labelData = FurtherProcessingLabelDataExtractor.ExtractJobData(Job, _labelType);

                // Update the base class string builders for compatibility with existing drawing methods
                basicInformation.Clear();
                basicInformation.Append(_labelData.BasicInformationText);

                processingText.Clear();
                processingText.Append(_labelData.ProcessingText1);

                processingText1.Clear();
                processingText1.Append(_labelData.ProcessingText1);

                processingText2.Clear();
                processingText2.Append(_labelData.ProcessingText2);

                strInstructions = _labelData.SpecialInstructionsText;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error formatting print content for job {JobId}", Job?.Id);
                throw;
            }
        }

        /// <summary>
        /// Get specific formatted section text
        /// </summary>
        /// <param name="section">Section name (BasicInformation, ProcessingText1, ProcessingText2, SpecialInstructions)</param>
        /// <returns>Formatted text for the section</returns>
        public string GetFormattedSection(string section)
        {
            if (_labelData == null || Job == null)
                return string.Empty;

            return FurtherProcessingLabelDataExtractor.GetFormattedSection(Job, _labelType, section);
        }

        /// <summary>
        /// Create a unified label for any label type
        /// </summary>
        /// <param name="job">The job to create label for</param>
        /// <param name="labelType">Type of label to create</param>
        /// <param name="printerAndTray">Printer and tray configuration</param>
        /// <param name="configurationApplication">Configuration application</param>
        /// <param name="filename">Output filename</param>
        /// <returns>Configured label ready for printing</returns>
        public static UnifiedFurtherProcessingLabel CreateLabel(
            IJob job, 
            LabelType labelType, 
            string printerAndTray, 
            IConfigurationApplication configurationApplication, 
            string filename)
        {
            return new UnifiedFurtherProcessingLabel(job, labelType, printerAndTray, configurationApplication, filename);
        }

        #endregion Public Methods
    }

    /// <summary>
    /// Factory class for creating different types of further processing labels
    /// </summary>
    public static class FurtherProcessingLabelFactory
    {
        /// <summary>
        /// Create a Further Processing label
        /// </summary>
        public static UnifiedFurtherProcessingLabel CreateFurtherProcessingLabel(
            IJob job, string printerAndTray, IConfigurationApplication configurationApplication, string filename)
        {
            return UnifiedFurtherProcessingLabel.CreateLabel(job, LabelType.FurtherProcessing, printerAndTray, configurationApplication, filename);
        }

        /// <summary>
        /// Create a DPC Processing label
        /// </summary>
        public static UnifiedFurtherProcessingLabel CreateDPCProcessingLabel(
            IJob job, string printerAndTray, IConfigurationApplication configurationApplication, string filename)
        {
            return UnifiedFurtherProcessingLabel.CreateLabel(job, LabelType.DPCProcessing, printerAndTray, configurationApplication, filename);
        }

        /// <summary>
        /// Create a Wide Format Processing label
        /// </summary>
        public static UnifiedFurtherProcessingLabel CreateWideFormatProcessingLabel(
            IJob job, string printerAndTray, IConfigurationApplication configurationApplication, string filename)
        {
            return UnifiedFurtherProcessingLabel.CreateLabel(job, LabelType.WideFormatProcessing, printerAndTray, configurationApplication, filename);
        }

        /// <summary>
        /// Create a Further Processing List label
        /// </summary>
        public static UnifiedFurtherProcessingLabel CreateFurtherProcessingListLabel(
            IJob job, string printerAndTray, IConfigurationApplication configurationApplication, string filename)
        {
            return UnifiedFurtherProcessingLabel.CreateLabel(job, LabelType.FurtherProcessingList, printerAndTray, configurationApplication, filename);
        }

        /// <summary>
        /// Create a label based on job characteristics
        /// </summary>
        public static UnifiedFurtherProcessingLabel CreateAutoLabel(
            IJob job, string printerAndTray, IConfigurationApplication configurationApplication, string filename)
        {
            // Auto-determine label type based on job characteristics
            LabelType labelType = LabelType.FurtherProcessing; // Default

            if (job.IsDigital())
            {
                labelType = LabelType.DPCProcessing;
            }
            else if (job.Template.Name.Contains("Wide Format"))
            {
                labelType = LabelType.WideFormatProcessing;
            }

            return UnifiedFurtherProcessingLabel.CreateLabel(job, labelType, printerAndTray, configurationApplication, filename);
        }
    }
}
