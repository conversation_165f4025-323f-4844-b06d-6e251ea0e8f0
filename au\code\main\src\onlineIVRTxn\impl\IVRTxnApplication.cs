﻿using lep.configuration;
using lep.email;
using lep.security;

using Serilog;
using NHibernate;
using System.Collections.Generic;
using System.Diagnostics;
using System.Reflection;

namespace lep.onlineIVRTxn.impl
{
	public class IVRTxnApplication : BaseApplication, IIVRTxnApplication
	{
		//#region Constants

		//private const string STR_OnlinePaymentsCSVFileName = "OnlinePayments_{0:yyyyMMdd}.csv";

		//#endregion
 

		public IVRTxnApplication(ISession sf, ISecurityApplication _securityApp) : base(sf, _securityApp)
		{
		}

 

		#region Readonly

		// private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		#endregion Readonly

		#region Fields

		private IConfigurationApplication configurationApp;

		private IEmailApplication emailApp;

		//private string merchantAccessCode;

		//private string merchantID;

		//private string merchantTransactionRef;

		#endregion Fields

		#region Properties

		public IConfigurationApplication ConfigurationApplication
		{
			get { return configurationApp; }
			set {
				Debug.Assert(value != null, "ConfigurationApplication is null.");
				configurationApp = value;
			}
		}

		public IEmailApplication EmailApplication
		{
			get { return emailApp; }
			set {
				Debug.Assert(value != null, "EmailApplication is null.");
				emailApp = value;
			}
		}

		//public string MerchantAccessCode
		//{
		//    get { return merchantAccessCode; }
		//    set
		//    {
		//        Debug.Assert( !System.String.IsNullOrEmpty( value ), "MerchantAccessCode is null or empty." );
		//        merchantAccessCode = value;
		//    }
		//}

		//public string MerchantID
		//{
		//    get { return merchantID; }
		//    set
		//    {
		//        Debug.Assert( !System.String.IsNullOrEmpty( value ), "MerchantID is null or empty." );
		//        merchantID = value;
		//    }
		//}

		//public string MerchantTransactionRef
		//{
		//    get { return merchantTransactionRef; }
		//    set
		//    {
		//        Debug.Assert( !System.String.IsNullOrEmpty( value ), "MerchantTransactionRef is null or empty." );
		//        merchantTransactionRef = value;
		//    }
		//}

		#endregion Properties

		#region Public Methods

		//public void DumpTodaysOnlineTransactions()
		//{
		//    string path = configurationApp.GetValue( Configuration.NetworkPathPaymentReportsCsv );

		//    string fileName = String.Format( STR_OnlinePaymentsCSVFileName, DateTime.Today );

		//    RunInTransaction( () => {
		//        FileStream fs = new FileStream( path + fileName, FileMode.Create );
		//        TextWriter writer = new StreamWriter( fs, new UTF8Encoding() );
		//        CsvSerialiser serial = new CsvSerialiser();
		//        serial.Writer = writer;
		//        serial.AlwaysQuote = true;
		//        serial.RowData( new string[] { "PaymentDate", "OrderNo", "Customer", "MYOB-Cust#", "Price", "ReceiptNo", "TransactionNo", "TxnReponseCode", "VpcMessage" } );
		//        foreach (IOnlineTxn tx in GetTodaysOnlineTransactions())
		//            serial.RowData( new string[] { tx.PaymentDate.ToString(), tx.Order.OrderNr, tx.Order.Customer.Name, tx.Order.Customer.MYOB, tx.Order.Price.ToString(), tx.VpcReceiptNo, tx.VpcTransactionNo, tx.VpcTxnResponseCode, tx.VpcMessage } );
		//        writer.Flush();
		//        fs.Close();
		//    } );
		//    Log.Information "Online Payment Transactions CRON Job updated the file : " + path + fileName );
		//}

		public IList<IOnlineIVRTxn> GetAllPayments()
		{
			return Session.CreateCriteria(typeof(IOnlineIVRTxn)).List<IOnlineIVRTxn>();
		}

		public void Save(IOnlineIVRTxn tx)
		{
			base.Save<IOnlineIVRTxn>(tx);
			//emailApp.SendOnlinePaymentNotification( tx );
		}

		#endregion Public Methods

		#region Private Methods

		//private IList<IOnlineTxn> GetTodaysOnlineTransactions()
		//{
		//    return Session.CreateCriteria( typeof( IOnlineTxn ), "tx" )
		//           .Add( Expression.Ge( "PaymentDate", System.DateTime.Today ) )
		//           .List<IOnlineTxn>();
		//}

		#endregion Private Methods
	}
}
