using lep.job.impl;
using System.Collections.Generic;

namespace lep.job
{
	public interface IJobOptionSpecStock
    {
        int Id { get; set; }
        IJobOptionSpecSize JobOptionSpecSize { get; set; }
        IStock Stock { get; set; }
		bool Magnet { get; }
		int MinMagnet { get; set; }
        IList<JobPrintOptions> FrontPrintOptions { get; set; }
        IList<JobPrintOptions> BackPrintOptions { get; set; }
        IList<CelloOption> CelloOptions { get; set; }

        IList<KeyValuePair<string, string>> CelloOptions2 { get; set; }

        IList<IPaperSize> FoldOptions { get; set; }
        IJobOptionSpecQuantity QuantityOption { get; set; }
        PrintType PrintType { get; set; }

        IList<int> QuantityOptions { get; set; }
    }
}