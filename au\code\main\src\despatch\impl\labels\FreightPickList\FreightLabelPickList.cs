using lep.configuration;
using lep.order;
using lep.run;

using Serilog;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Printing;
using System.Reflection;

namespace lep.despatch.impl.label
{
	public class FreightPickList : PrintDocument
	{
		#region Constructors

		public FreightPickList()
		{
		}

		#endregion Constructors

		public IConfigurationApplication ConfigurationApplication { get; set; }

		#region Fields

		protected float bottomMargin;

		protected List<Align> columnAlignments = new List<Align>()
		{
			Align.left,
			Align.left,
			Align.center,
			Align.left,
			Align.left,
			Align.left,
			Align.center,
			Align.center
		};

		protected List<String> columnHeaders = new List<String>()
		{
			"Order#",
			"Job#",
			"Jobs",
			"Customer",
			"Job Name",
			"Courier",
			"Pkg Type",
			"Label Qty",
		};

		protected int columnPoint;

		// Maintain a generic list to hold start/stop points for the column printing
		// This will be used for wrapping in situations where the DataGridView will not fit on a single page
		protected List<int[]> columnPoints = new List<int[]>();

		protected List<float> columnPointsWidth = new List<float>();

		protected List<float> columnsWidth = new List<float>();

		protected int currentRow;

		protected float currentY;

		protected Font defaultCellStyleFont = new Font("Tahoma", 12, FontStyle.Regular, GraphicsUnit.Point);

		protected Font defaultHeaderStyleFont;

		protected float dtWidth;

		private List<FreightPickListDTO> freightPickListData = new List<FreightPickListDTO>();

		protected float leftMargin;

		protected float pageHeight;

		protected int pageNumber = 0;

		protected float pageWidth;

		private PropertyInfo[] pi;

		protected float rightMargin;

		protected float rowHeaderHeight;

		protected List<float> rowsHeight = new List<float>();

		private IRun run;

		private Type t;

		protected float topMargin;

		//// private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		#endregion Fields

		#region Properties

		public string PrinterAndTray { get; set; }

		public String PrintFileName { get; set; }

		public IRun Run
		{
			get { return run; }

			set {
				run = value;
				freightPickListData = FormatPrintData(run);
			}
		}

		#endregion Properties

		#region Public Methods

		public bool DrawDataGridView(Graphics g)
		{
			try
			{
				Calculate(g);
				DrawHeader(g);
				var continueToNextPage = DrawRows(g);
				return continueToNextPage;
			}
			catch (Exception ex)
			{
				Log.Error(ex.Message, ex);
				throw;
			}
		}

		public void SetupPrintProperties()
		{
			var ps = new PrinterSettings();
			PrintUtils.ConfigurePrinterSettings(PrinterAndTray, ref ps);

			ps.PrintToFile = ps.PrinterName == "Microsoft XPS Document Writer";
			if (ps.PrintToFile)
			{
				ps.PrintFileName = PrintFileName;
			}
			ps.DefaultPageSettings.Landscape = true;

			for (var i = 0; i <= ps.PaperSizes.Count - 1; i++)
			{
				if (ps.PaperSizes[i].RawKind == (int)PaperKind.A4)
				{
					ps.DefaultPageSettings.PaperSize = ps.PaperSizes[i];
					break;
				}
			}

			ps.DefaultPageSettings.Margins = new Margins(40, 40, 40, 40);
			ps.DefaultPageSettings.Landscape = true;

			DefaultPageSettings.PrinterSettings = ps;
			PrinterSettings = ps;
			// Claculating the PageWidth and the pageHeight
			if (!DefaultPageSettings.Landscape)
			{
				pageWidth = PrinterSettings.DefaultPageSettings.PaperSize.Width;
				pageHeight = PrinterSettings.DefaultPageSettings.PaperSize.Height;
			}
			else
			{
				pageHeight = PrinterSettings.DefaultPageSettings.PaperSize.Width;
				pageWidth = PrinterSettings.DefaultPageSettings.PaperSize.Height;
			}

			// Claculating the page margins
			leftMargin = DefaultPageSettings.Margins.Left - (int)DefaultPageSettings.HardMarginX;
			topMargin = DefaultPageSettings.Margins.Top - (int)DefaultPageSettings.HardMarginY;
			rightMargin = DefaultPageSettings.Margins.Right + (int)DefaultPageSettings.HardMarginX;
			bottomMargin = DefaultPageSettings.Margins.Bottom + (int)DefaultPageSettings.HardMarginY;

			// First, the current row to be printed is the first row
			currentRow = 0;
		}

		#endregion Public Methods

		#region Protected Methods

		// The function that calculate the height of each row (including the header row),
		// the width of each column (according to the longest text in all its cells including the header cell),
		// and the whole DataGridView width
		protected void Calculate(Graphics g)
		{
			try
			{
				// Just calculate once
				if (pageNumber == 0)
				{
					var tmpSize = new SizeF();
					Font tmpFont;
					tmpFont = defaultCellStyleFont;
					float tmpWidth;

					t = freightPickListData[0].GetType();
					pi = t.GetProperties();
					rowHeaderHeight = dtWidth = 0;

					for (var i = 0; i < columnHeaders.Count; i++)
					{
						tmpSize = g.MeasureString(columnHeaders[i], tmpFont);
						tmpWidth = tmpSize.Width;
						rowHeaderHeight = Math.Max(rowHeaderHeight, tmpSize.Height);

						for (var j = 0; j < freightPickListData.Count; j++)
						{
							tmpSize = g.MeasureString("Anything", tmpFont);
							rowsHeight.Add(tmpSize.Height);
							var s = columnHeaders[i];

							tmpSize = g.MeasureString(s, tmpFont);
							if (tmpSize.Width > tmpWidth)
								tmpWidth = tmpSize.Width;
						}
						//if (dt.Columns[i].Visible)
						dtWidth += tmpWidth;
						columnsWidth.Add(tmpWidth);
					}

					var totalWidth = 0.0F;
					for (var i = 0; i < columnsWidth.Count; i++)
						totalWidth += columnsWidth[i];

					if (totalWidth < pageWidth)
					{
						var increase = (pageWidth - leftMargin - rightMargin - totalWidth) / 5;

						//for (int i = 3; i < columnsWidth.Count-2; i++)
						columnsWidth[0] += increase;
						columnsWidth[1] += increase;
						columnsWidth[3] += increase;
						columnsWidth[4] += increase;
						columnsWidth[5] += increase;
					}

					rowsHeight.Clear();
					for (var j = 0; j < freightPickListData.Count; j++)
					{
						float tmpHeight = 0;

						for (var i = 0; i < pi.Length; i++)
						{
							var p = pi[i];
							var s = (string)p.GetValue(freightPickListData[j], null);
							tmpSize = g.MeasureString(s, tmpFont);
							if (tmpSize.Height > tmpHeight)
								tmpHeight = tmpSize.Height;
						}

						rowsHeight.Add(tmpHeight);
					}
					// Define the start/stop column points based on the page width and the DataGridView Width
					// We will use this to determine the columns which are drawn on each page and how wrapping will be handled
					// By default, the wrapping will occurr such that the maximum number of columns for a page will be determine
					int k;

					var mStartPoint = 0;
					for (k = 0; k < pi.Length; k++)
						//if (dt.Columns[k].Visible) {
						mStartPoint = k;
					//break;
					//}

					var mEndPoint = pi.Length;
					for (k = pi.Length - 1; k >= 0; k--)
						//if (dt.Columns[k].Visible) {
						mEndPoint = k + 1;
					//	break;
					//}

					var mTempWidth = dtWidth;
					var mTempPrintArea = (float)pageWidth - (float)leftMargin - (float)rightMargin;

					// We only care about handling where the total datagridview width is bigger then the print area
					if (dtWidth > mTempPrintArea)
					{
						mTempWidth = 0.0F;
						for (k = 0; k < pi.Length; k++)
						{
							//if (dt.Columns[k].Visible) {
							mTempWidth += columnsWidth[k];
							// If the width is bigger than the page area, then define a new column print range
							if (mTempWidth > mTempPrintArea)
							{
								mTempWidth -= columnsWidth[k];
								columnPoints.Add(new int[] { mStartPoint, mEndPoint });
								columnPointsWidth.Add(mTempWidth);
								mStartPoint = k;
								mTempWidth = columnsWidth[k];
							}
							//}
							// Our end point is actually one index above the current index
							mEndPoint = k + 1;
						}
					}
					// Add the last set of columns
					columnPoints.Add(new int[] { mStartPoint, mEndPoint });
					columnPointsWidth.Add(mTempWidth);
					columnPoint = 0;
				}
			}
			catch (Exception ex)
			{
				Log.Error(ex, ex.Message);
				throw;
			}
		}

		protected void DrawHeader(Graphics g)
		{
			currentY = (float)topMargin;
			DrawOrderNoCustNameSkid(g);

			pageNumber++;

			var currentX = (float)leftMargin;
			var theLinePen = new Pen(Brushes.Gray, 1);
			var headerFont = new Font(defaultCellStyleFont, FontStyle.Bold);

			// Calculating and drawing the HeaderBounds
			var HeaderBounds = new RectangleF(currentX, currentY, columnPointsWidth[columnPoint], rowHeaderHeight);

			// Setting the format that will be used to print each cell of the header row
			var cellFormat = new StringFormat
			{
				Trimming = StringTrimming.Word,
				FormatFlags = StringFormatFlags.NoWrap | StringFormatFlags.LineLimit | StringFormatFlags.NoClip
			};

			// Printing each visible cell of the header row
			RectangleF cellBounds;
			float columnWidth;
			for (var i = 0; i < columnHeaders.Count; i++)
			{
				var p = pi[i];

				columnWidth = columnsWidth[i];
				if (columnAlignments[i] == Align.right)
					cellFormat.Alignment = StringAlignment.Far;
				else if (columnAlignments[i] == Align.center)
					cellFormat.Alignment = StringAlignment.Center;
				else
					cellFormat.Alignment = StringAlignment.Near;

				cellBounds = new RectangleF(currentX, currentY, columnWidth, rowHeaderHeight);

				// Printing the cell text
				g.DrawString(columnHeaders[i], headerFont, Brushes.Brown, cellBounds, cellFormat);

				g.DrawRectangle(theLinePen, currentX, currentY, columnWidth, rowHeaderHeight);

				currentX += columnWidth;
			}

			currentY += rowHeaderHeight;
		}

		// The function that print a bunch of rows that fit in one page
		// When it returns true, meaning that there are more rows still not printed, so another PagePrint action is required
		// When it returns false, meaning that all rows are printed (the CureentRow parameter reaches the last row of the DataGridView control) and no further PagePrint action is required
		protected bool DrawRows(Graphics g)
		{
			var LinePen = new Pen(Color.Gray, 1);

			// The style paramters that will be used to print each cell
			Font rowFont;

			// Setting the format that will be used to print each cell
			var cellFormat = new StringFormat();
			cellFormat.Trimming = StringTrimming.Word;
			cellFormat.FormatFlags = StringFormatFlags.LineLimit; //  StringFormatFlags.NoWrap |

			// Printing each visible cell
			RectangleF rowBounds;
			float currentX;
			float columnWidth;
			while (currentRow < freightPickListData.Count)
			{
				rowFont = defaultCellStyleFont;
				currentX = (float)leftMargin;
				rowBounds = new RectangleF(currentX, currentY, columnPointsWidth[columnPoint], rowsHeight[currentRow]);

				for (var currentCell = 0; currentCell < pi.Length; currentCell++)
				{
					var p = pi[currentCell];

					// Check the CurrentCell alignment and apply it to the CellFormat
					if (columnAlignments[currentCell] == Align.right)
						cellFormat.Alignment = StringAlignment.Far;
					else if (columnAlignments[currentCell] == Align.center)
						cellFormat.Alignment = StringAlignment.Center;
					else
						cellFormat.Alignment = StringAlignment.Near;

					columnWidth = columnsWidth[currentCell];
					var CellBounds = new RectangleF(currentX, currentY, columnWidth, rowsHeight[currentRow]);

					// Printing the cell text
					var cellContent = (string)p.GetValue(freightPickListData[currentRow], null);

					g.DrawString(cellContent, rowFont, Brushes.Black, CellBounds, cellFormat);

					// Drawing the cell bounds
					g.DrawRectangle(LinePen, currentX, currentY, columnWidth, rowsHeight[currentRow]);
					//g.DrawLine( LinePen, CurrentX, currentY, CurrentX, currentY + rowsHeight[currentRow] );

					currentX += columnWidth;
				}
				currentY += rowsHeight[currentRow];

				// Checking if the currentY is exceeds the page boundries
				// If so then exit the function and returning true meaning another PagePrint action is required
				if ((int)currentY > pageHeight - topMargin - bottomMargin)
				{
					currentRow++;
					return true;
				}

				currentRow++;
			}

			columnPoint++; // Continue to print the next group of columns

			if (columnPoint == columnPoints.Count)
			{
				// Which means all columns are printed
				columnPoint = 0;
				return false;
			}
			else
				return true;
		}

		protected override void OnPrintPage(PrintPageEventArgs e)
		{
			base.OnPrintPage(e);
			var more = DrawDataGridView(e.Graphics);
			if (more == true)
				e.HasMorePages = true;
		}

		#endregion Protected Methods

		#region Private Methods

		private void DrawOrderNoCustNameSkid(Graphics g)
		{
			// LEFT Bit first
			var height = 0f;
			using (var font = new Font("Arial", 20, FontStyle.Bold, GraphicsUnit.Point))
			{
				string text;
				text = String.Format("Freight Label Pick List - Run Number: {0}", Run.RunNr);

				height = g.MeasureString(text, font).Height;
				var rect = new RectangleF(leftMargin, currentY, pageWidth - rightMargin - leftMargin, height);

				//g.FillRectangle( Brushes.Gainsboro, secondRowLeftRect );
				g.DrawString(text, font, new SolidBrush(Color.Blue), rect);
			}

			currentY += height;
		}

		private List<FreightPickListDTO> FormatPrintData(IRun r)
		{
			/* todo: iwen
            var fpl = (from o in r.FreightReadyOrders
                select new FreightPickListDTO()
                {
                    OrderNr = o.OrderNr,
                    JobNr = string.Join("\n", o.ActiveJobs.OrderBy(j => j.Id).Select(x => x.JobNr).ToArray()),
                    Jobs = o.ActiveJobs.Where(y => y.Runs.Count > 0 && y.Runs[0].Id == r.Id).Count().ToString(),
                    Customer = o.Customer.Name.Substring(0, Math.Min(o.Customer.Name.Length, 30)),
                    Job =
                        string.Join("\n",
                            o.ActiveJobs.OrderBy(j => j.Id)
                                .Select(x => x.Name.Substring(0, Math.Min(x.Name.Length, 30)))
                                .ToArray()),
                    Courier = o.Courier.ToString(),
                    PkgType =
                        string.Join("\n",
                            o.ActiveJobs.OrderBy(j => j.Id).Select(x => x.Freight.CartonType.ToString()).ToArray()),
                    LabelQty = packageLabelQty(o)
                }).ToList();

            return fpl;*/
			return null;
		}

		private string packageLabelQty(IOrder o)
		{
			IDictionary<int, int> sumList = new Dictionary<int, int>();

			/*todo:iwen
            foreach (var j in o.ActiveJobs)
            {
                sumList.Add(j.Id, 0);
            }

            foreach (var p in o.PackDetail.Packages)
            {
                foreach (var data in p.Content)
                {
                    if (sumList.ContainsKey(data.Key.Id))
                    {
                        sumList[data.Key.Id] += p.Quantity;
                    }
                }
            }

            if (sumList.Count > 0)
            {
                return
                    sumList.OrderBy(d => d.Key)
                        .Select(d => d.Value.ToString())
                        .Aggregate((a, b) => String.Concat(a, "\n", b)) + "\n";
            }*/
			return string.Empty;
		}

		#endregion Private Methods
	}
}
