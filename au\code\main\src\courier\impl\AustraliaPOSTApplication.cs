using System;
using System.Collections.Generic;
using System.Collections;
using System.Text;
using System.Text.RegularExpressions;
using System.IO;
using Spring.Objects.Factory;
using NHibernate.Criterion;
using NHibernate;
using System.Linq;

using lep.courier.csv;
using lep.configuration;
using lep.order;
using lep.freight;
using lep.freight.impl;
using lep.job;
/*


namespace lep.courier.impl
{

	/// <summary>
    /// 
    /// </summary>
	public class AustraliaPOSTApplication: BaseCourierApplication, ICourierApplication
    {
		private static readonly Common.Logging.ILog log = Common.Logging.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

		public AustraliaPOSTApplication() : base(CourierType.AustraliaPOST)
        {
		}

        public bool CalculatePrice( Facility facility, IList<IPackage> packages, string postcode, decimal totalWeight, bool hasSkid, StringBuilder log, out decimal price)
		{
			price = 0;
			log.AppendLine( "\n\nAustraliaPOST calculation" );

			if (hasSkid) {
				log.AppendLine( "skid delivery not available" );
				return false;
			}

			decimal smallMax = Convert.ToDecimal(ConfigurationApplication.GetValue( Configuration.AustraliaPOSTSmallWeight ));
			decimal largeMax = Convert.ToDecimal(ConfigurationApplication.GetValue( Configuration.AustraliaPOSTLargeWeight ));
			decimal smallPrice = Convert.ToDecimal(ConfigurationApplication.GetValue( Configuration.AustraliaPOSTSmallPrice ));
			decimal largePrice = Convert.ToDecimal( ConfigurationApplication.GetValue( Configuration.AustraliaPOSTLargePrice ) );

            if (!PackInBag(packages, largeMax, smallMax, smallPrice, largePrice, log, out price))
            {
				return false;
			}
	
			decimal surcharge = GetCourierSurCharge();
			log.AppendFormat( "total price: {0} * {1}(surcharge) = ${2}\n",AuditDecimal(price),surcharge,AuditDecimal(price * surcharge) );
			price = price * surcharge;

			return true;
		}

		private struct TmpPackage
		{
			public decimal Weight { get; set; }
			public int Qty { get; set; }
		}
	}
}



*/