namespace lep.job.impl
{
	public class PressDetail : IPressDetail
    {
        public PressDetail()
        {
        }

        public PressDetail(string stock, int sect, string size, string method, int qty)
        {
            Stock = stock;
            Sect = sect;
            Size = size;
            Method = method;
            Qty = qty;
        }

        public virtual string Stock { get; set; }

        public virtual int Sect { get; set; }

        public virtual string Size { get; set; }

        public virtual string Method { get; set; }

        public virtual int Qty { get; set; }
    }
}