using System.Collections.Generic;

namespace lep.job.csv
{
    public class FoldOptionsCsvReader : BaseCsvReader
    {
        private FoldOptionsImportHandler importHandler;

        private FoldOptionsVerifyForeignKey verifyForeignKeyHandler;
        private FoldOptionsVerifySchemaHandler verifyHandler;

        public FoldOptionsCsvReader() : base()
        {
            verifyHandler = new FoldOptionsVerifySchemaHandler();
            verifyForeignKeyHandler = new FoldOptionsVerifyForeignKey();
            importHandler = new FoldOptionsImportHandler();

            skipHandler.Handler = verifyHandler;
            verifyHandler.Handler = verifyForeignKeyHandler;
            verifyForeignKeyHandler.Handler = importHandler;
        }

        public IDictionary<int, IList<IPaperSize>> Result { get; private set; }

        public override void StartDocument()
        {
            importHandler.JobApplication = jobApp;
            verifyForeignKeyHandler.JobApplication = jobApp;
            base.StartDocument();
        }

        public override void EndDocument()
        {
            Result = importHandler.Result;
            base.EndDocument();
        }
    }
}