using lep.configuration;
using lep.freight;
using lep.job;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Printing;
using System.Linq;

namespace lep.despatch.impl.label
{
    public class CartonLabel : PrintDocument, IDespatchLabel
    {
        #region Constructors

        public CartonLabel()
        {
        }

        #endregion Constructors

        #region Protected Methods

        protected override void OnPrintPage(PrintPageEventArgs e)
        {
            base.OnPrintPage(e);
            var g = e.Graphics;

            if (_CurrentPage < _Labels.Count)
            {
                g.DrawString(_Labels[_CurrentPage], _Font, Brushes.Black, new Rectangle(3, 3, 250, 150), format);
                _CurrentPage++;

                if (_CurrentPage < _Labels.Count)
                    e.HasMorePages = true;
                else
                    e.HasMorePages = false;
            }
        }

        #endregion Protected Methods

        #region Constants

        private const int INT_FontSize = 12;

        private const string STR_FontName = "Courier New";

        #endregion Constants

        #region Fields

        private int _CurrentPage = 0;

        private Font _Font = new Font(STR_FontName, INT_FontSize, FontStyle.Bold);

        private List<string> _Labels = new List<string>();

        //  private PrinterSettings _printerSettings;

        // private int _TotalPages = 0;

        private StringFormat format = new StringFormat
        {
            Trimming = StringTrimming.None
        };

        private IJob job;

        #endregion Fields

        #region Properties

        public IConfigurationApplication ConfigurationApplication { get; set; }

        public IJob Job
        {
            get { return job; }

            set {
                job = value;
                FormatPrintData();
            }
        }

		private int splitIndex = -1;
		public int SplitIndex { 
			get { return splitIndex; } 
			set {
				splitIndex = value;
				FormatPrintData();
			} 
		}


        public string PrinterAndTray { get; set; }

        public String PrintFileName { get; set; }

        #endregion Properties

        #region Public Methods

        public void FormatPrintData()
        {
			_Labels.Clear();
			if (Job == null)
				return;

			/*todo:iwen*/
			const string STR_Fmt = "Job Name: {0}\nOrder # {1}\nJob   # {2}\nQuantity: {3}\nCarton {4} of {5}";
			// Carton Label Prints CARTONS OF AN ORDER,
			// Initiated at a scan of a JOB NUMBER Of a JOB BAG


			if (!job.HasSplitDelivery)
			{
				// get total packages for this Job
				// get all package from order
				var packagesO = Job.Order.PackDetail.GetPackages(null);

				// add them to a ListOfPackages
				var packages1 = new ListOfPackages();
				packages1.AddRange(packagesO);

				// collect all the packages
				var packages2 = packages1.Traverse();

				// filter and keep just this jobs packages that are in level 2 to 8
				var packages3 = packages2.Where(_ => _.Level >= 2 && _.Level < 9 && _.JobId == Job.Id);

				var totalPackageCount = packages3.Count();
				if (totalPackageCount == 0)
					return;

				var currentPackage = 0;
				foreach (var p in packages3)
				{
					currentPackage++;
					_Labels.Add(string.Format(STR_Fmt, Job.Name,
						Job.Order.OrderNr,
						Job.JobNr,
						p.JobQty,
						currentPackage,
						totalPackageCount));
				}
			}
			else 
			{

				for (int i = 0; i < job.Splits.Count; i++)
				{
					if (SplitIndex != -1  // if a split index has been specified
						&& i != SplitIndex // then for all other splits 
						) continue;

					JobSplit split = job.Splits[i];
					
					// Carton Label Prints CARTONS OF AN ORDER,
					// Initiated at a scan of a JOB NUMBER Of a JOB BAG

					// get total packages for this Job

					// get all package from order
					var packagesO = split.Packages;

					// add them to a ListOfPackages
					var packages1 = new ListOfPackages();
					packages1.AddRange(packagesO);

					// collect all the packages
					var packages2 = packages1.Traverse();

					// filter and keep just this jobs packages that are in level 2 to 8
					var packages3 = packages2.Where(_ => _.Level >= 2 && _.Level < 9 && _.JobId == Job.Id);

					var totalPackageCount = packages3.Count();
					if (totalPackageCount == 0)
						return;

					var currentPackage = 0;
					foreach (var p in packages3)
					{
						currentPackage++;
						_Labels.Add(string.Format(STR_Fmt, Job.Name,
							Job.Order.OrderNr,
							Job.JobNr + " /" + (i + 1),
							p.JobQty,
							currentPackage,
							totalPackageCount));
					}


				}




			}


            _CurrentPage = 0;
        }

        public void SetupPrintProperties()
        {
            var ps = new PrinterSettings();
            PrintUtils.ConfigurePrinterSettings(PrinterAndTray, ref ps);

			PrinterSettings = ps;
			DefaultPageSettings.PaperSize = new PaperSize("65x 40mm", 255, 157);

			ps.PrintToFile = ps.PrinterName == "Microsoft XPS Document Writer";
			ps.DefaultPageSettings.Margins = new Margins(1, 1, 1, 1);

			if (ps.PrintToFile)
			{
				ps.PrintFileName = PrintFileName;
			}

			PrinterSettings = ps;
		}

        #endregion Public Methods
    }
}
