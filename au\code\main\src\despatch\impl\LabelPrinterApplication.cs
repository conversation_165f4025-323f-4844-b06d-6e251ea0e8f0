using lep.configuration;
using lep.job;
using lep.run;
using lep.user;
using System;
using System.Collections.Generic;
using System.Linq;
using FastReport;
using FastReport.Export.PdfSimple;
using FastReport.Export.PdfSimple.PdfCore;
using lep.order;
using System.IO;
using System.Runtime;
using System.Dynamic;
using lep.email;
using Serilog;
using System.Drawing.Printing;
using lep.extensionmethods;
using System.Diagnostics;

namespace lep.despatch.impl
{
	public class LabelPrinterApplication
	{
		private IConfigurationApplication ConfigurationApplication { get; set; }
		private IEmailApplication _emailApp;

		public LabelPrinterApplication(IConfigurationApplication configurationApplication, IEmailApplication emailApp)
		{
			this.ConfigurationApplication = configurationApplication;
			_emailApp = emailApp;
		}

		public void DoDispatchProcessUnifiedBC(IJob job)
		{
			if (!job.IsBusinessCard())
				return;

			Print(LabelType.FurtherProcessing, job);
		}

		public dynamic DoDispatchProcessUnifiedNonBC(IJob job, IUser user, Dictionary<string, string> printers, Facility facility)
		{
			//var color = "green";
			dynamic result = new ExpandoObject();
			result.jobId = job.Id;
			var order = job.Order;
			var orderId = job.Order.Id;

			if (job.Order.Jobs.All(_ => _.Status == JobStatusOptions.Dispatched))
			{

				result.msg = $"Order # {orderId} Already Dispatched";
				result.color = "green";
				result.btnGenerate = false;
			}
			else if (job.Order.Jobs.All(_ => _.Status == JobStatusOptions.PayMe))
			{
				if (job.IsCustomerGood())
				{
					job.SetStatus(JobStatusOptions.Packed, user);
					return new
					{
						lastJob = true,
						color = "green",
						msg = $"Last Job on Order # {job.Order.Id}",
						jobId = job.Id,
						btnGenerate = true
					};
				}
			}
			else if (job.IsPartOfMultiJobOrder())
			{
				if (job.IsTheLastJobInOrderBeingProcessed(facility))
				{
					result.lastJob = true;
					if (job.IsCustomerGood())
					{
						result.msg = $"Last Job on Order # {job.Order.Id}";
						result.color = "green";
						result.btnGenerate = true;

						job.SetStatus(JobStatusOptions.Packed, user);

						//if (job.Order.Courier.IsPickup)
						//{
						//	//Print(LabelType.Pickup, job);
						//	DispatcherPrint("PickUpLabel");
						//}
						//else
						//{
						//	//Last just in Order packed, doesn�t print �Filling or Freight Me� label
						//}
					}
					else
					{
						result.msg = $"Pay me! Order # {orderId}";
						result.color = "red";
						result.btnGenerate = false;
						job.SetStatus(JobStatusOptions.PayMe, user);
						_emailApp.SendPayMeOrder(order);
						DispatcherPrint("PayMeLabel");
					}
				}
				else
				{
					result.lastJob = false;
					result.msg = $"Not Last Job on Order # {orderId}";
					result.color = "green";
					result.btnGenerate = false;

					if (job.Status != JobStatusOptions.PayMe)
						job.SetStatus(JobStatusOptions.Packed, user);

					//Print(LabelType.Filing, job); //SCREEN 4
					DispatcherPrint("FillingLabel");
				}
			}
			else
			{
				// Single job

				if (job.IsCustomerGood())
				{
					job.SetStatus(JobStatusOptions.Packed, user);

					result.msg = $"Last Job on Order # {orderId}";
					result.color = "green";
					result.btnGenerate = true;

					//if (job.Order.Courier.IsPickup)
					//{
					//	//Print(LabelType.Pickup, job);
					//	DispatcherPrint("PickUpLabel");
					//}
					//else
					//{
					//	//Last just in Order packed, doesn�t print �Filling or Freight Me� label
					//}
				}
				else
				{
					result.msg = $"Pay me! Order # {orderId}";
					result.color = "red";
					result.btnGenerate = false;
					job.SetStatus(JobStatusOptions.PayMe, user);
					_emailApp.SendPayMeOrder(order);
					DispatcherPrint("PayMeLabel");
				}
			}


			void DispatcherPrint(string label)
			{
				var jobId = job.Id;
				Report report = new Report();
				var copies = 1;
				//FastReport.Utils.Config.ReportSettings.ShowProgress = false;

				var printerName = "";
				if (!printers.TryGetValue(label, out printerName))
				{
					Log.Error($"can not find printerName for {label}");
					return;
				}

				var printName = $"O{job.Order.Id}-J{job.Id}-{label}";
				Action<string, string> _ = (k, v) => (report.FindObject(k) as TextObject).Text = v;

				if (label == "PayMeLabel")
				{
					report.Load(LepGlobal.Instance.LabelsPayMeLabel);
					_("varOrderDetails", $"{orderId}\n{order.Customer.Name.Sanitize()}");
					_("varJobDetails", $"Job#{jobId}\nJob Name: {job.Name.Sanitize()}");
					PrintUtils.PrintReport(report, printerName, copies, printName);
				}
				if (label == "FillingLabel")
				{
					report.Load(LepGlobal.Instance.LabelsFillingLabel);
					_("varOrderDetails", $"{orderId}\n{order.Customer.Name.Sanitize()}");
					_("varJobDetails", $"Job#{jobId}\nJob Name: {job.Name.Sanitize()}");
					PrintUtils.PrintReport(report, printerName, copies, printName);
				}
				if (label == "AddressA4Label")
				{
					report.Load(LepGlobal.Instance.LabelsAddressA4Label);
					_("varName", order.ReceiverName.Sanitize());
					_("varAddress", order.DeliveryAddress.ToStringFormatted());
					_("varReferenceNo", $"Reference No: {orderId}");
					_("varContactPhoneNo", $"Contact Phone: {order.ReceiverPhone}");
					PrintUtils.PrintReport(report, printerName, copies, printName);
				}
				if (label == "SampleLabel")
				{
					report.Load(LepGlobal.Instance.LabelsSampleLabel);
					_("varComment", "");
					_("varName", order.ReceiverName.Sanitize());
					_("varAddress", order.Customer.BillingAddress.ToStringFormatted());
					_("varReferenceNo", $"");
					_("varContactPhoneNo", "Samples enclosed.");
					PrintUtils.PrintReport(report, printerName, copies, printName);
				}
				if (label == "PickUpLabel")
				{
					var phone = order.Customer.Contact1.PhoneFull().NullIf() ?? order.Customer.Contact1.Mobile.NullIf() ?? "-";
					_("varComment", $"{order.RecipientName}");
					_("varName", order.Customer.Name);
					_("varReferenceNo", $"{orderId}");
					_("varContactPhoneNo", $"Contact Phone: {phone}");
					PrintUtils.PrintReport(report, printerName, copies, printName);
				}

				report.Dispose();
			}

			return result;
		}
		public dynamic GetDispatcherFlags(IJob job)
		{
			dynamic result = new ExpandoObject();
			result.jobId = job.Id;
			var order = job.Order;
			var orderId = job.Order.Id;
			if (job.Order.Jobs.All(_ => _.Status == JobStatusOptions.Dispatched))
			{

				result.msg = $"Order # {orderId} Already Dispatched";
				result.color = "green";
				result.btnGenerate = false;
			}
			else if (job.Order.Jobs.All(_ => _.Status == JobStatusOptions.PayMe))
			{
				if (job.IsCustomerGood())
				{
					return new
					{
						lastJob = true,
						color = "green",
						msg = $"Last Job on Order # {job.Order.Id}",
						jobId = job.Id,
						btnGenerate = true
					};
				}
			}
			else if (job.IsPartOfMultiJobOrder())
			{
				if (job.IsTheLastJobInOrderBeingProcessed((Facility)job.Facility))
				{
					result.lastJob = true;
					if (job.IsCustomerGood())
					{
						result.msg = $"Last Job on Order # {job.Order.Id}";
						result.color = "green";
						result.btnGenerate = true;
					}
					else
					{
						result.msg = $"Pay me! Order # {orderId}";
						result.color = "red";
						result.btnGenerate = false;
					}
				}
				else
				{
					result.lastJob = false;
					result.msg = $"Not Last Job on Order # {orderId}";
					result.color = "green";
					result.btnGenerate = false;
				}
			}
			else
			{
				// Single job
				if (job.IsCustomerGood())
				{
					result.msg = $"Last Job on Order # {orderId}";
					result.color = "green";
					result.btnGenerate = true;
				}
				else
				{
					result.msg = $"Pay me! Order # {orderId}";
					result.color = "red";
					result.btnGenerate = false;
				}
			}
			return result;
		}

		//public void PrintAppropiateLabel(IJob job)
		//{
		//	if (job.IsBusinessCard())
		//		DoDispatchProcessUnifiedBC(job);
		//	else
		//		DoDispatchProcessUnifiedNonBC(job);
		//}

		public void Print(LabelType labelType, IJob job)
		{
			switch (labelType)
			{
				case LabelType.Filing:
				case LabelType.FilingPayMe:
				case LabelType.FreightMe:
				case LabelType.ImmediateFreightMe:
				//break;

				case LabelType.Freight:
				case LabelType.Pickup:
					//LORD - 614
					LepGlobal.Instance.PrintQueue.Enqueue(new PrintItem() { LabelType = labelType, DataNumber = job.Id });
					break;

				case LabelType.DPCProcessing:
					if (job.IsDigital())
					{
						LepGlobal.Instance.PrintQueue.Enqueue(new PrintItem() { LabelType = labelType, DataNumber = job.Id });
					}
					break;

				case LabelType.WideFormatProcessing:
					LepGlobal.Instance.PrintQueue.Enqueue(new PrintItem() { LabelType = labelType, DataNumber = job.Id });
					break;

				default:
					LepGlobal.Instance.PrintQueue.Enqueue(new PrintItem() { LabelType = labelType, DataNumber = job.Id });
					break;
			}
		}

		public void Print(LabelType labelType, IRun run, Facility? facility = null)
		{
			if (labelType == LabelType.FurtherProcessingList)
			{
				int[] slotOrdering = ConfigurationApplication.GetValue(Configuration.FPLSlotOrder).Split(',').Select(s => Convert.ToInt32(s)).ToArray();

				if (run.PrintType == PrintType.D)
				{
					slotOrdering = slotOrdering.Where(_ => _ < 22).ToArray();
				}


				var runslotsSorted = RunSlotsByPrintOrder(run, slotOrdering);

				// first print the group of Further processig  lables
				var jobIdsSorted = runslotsSorted.Select(rs => rs.Job.Id).Distinct().ToArray();
				var fpLablesPrint = new PrintItem() { LabelType = LabelType.FurtherProcessingList, DataNumber = run.Id, DataNumbers = jobIdsSorted };
				LepGlobal.Instance.PrintQueue.Enqueue(fpLablesPrint);

				// Now print Further processign for jobs that needs to send samples in reverse order
				var jobsIdsNeedingSample = new List<int>();
				foreach (var rs in runslotsSorted)
				{
					if (rs.Job.SendSamples && !jobsIdsNeedingSample.Contains(rs.Job.Id))
					{
						jobsIdsNeedingSample.Add(rs.Job.Id);
					}
				}

				if (jobsIdsNeedingSample.Count > 0)
				{
					jobsIdsNeedingSample.Reverse();
					var fpLablesForSamplesPrint = new PrintItem() { LabelType = LabelType.FurtherProcessingList, DataNumber = run.Id, DataNumbers = jobsIdsNeedingSample.ToArray() };
					LepGlobal.Instance.PrintQueue.Enqueue(fpLablesForSamplesPrint);
				}
				return;
			}

			LepGlobal.Instance.PrintQueue.Enqueue(new PrintItem() { LabelType = labelType, DataNumber = run.Id, Facility = facility });
		}

		private IList<IRunSlot> RunSlotsByPrintOrder(IRun run, int[] slotOrdering)
		{
			var runslots = new List<IRunSlot>(run.Slots);
			runslots.Sort((a, b) =>
			{
				var aOrdering = -1;
				var bOrdering = -1;
				if (a.Slot <= slotOrdering.Length && a.Slot > 0)
				{
					aOrdering = slotOrdering[a.Slot - 1];
				}
				if (b.Slot <= slotOrdering.Length && b.Slot > 0)
				{
					bOrdering = slotOrdering[b.Slot - 1];
				}
				return aOrdering.CompareTo(bOrdering);
			});

			var printLog = runslots.Select(s => s.Slot + "    " + s.Job.Id).Aggregate((m, n) => m + "\n" + n) ?? "";
			//Log.InformationprintLog);
			//Console.WriteLine(printLog);
			return runslots;
		}
	}
}
