﻿<?xml version="1.0" encoding="utf-8"?>
<ClassDiagram MajorVersion="1" MinorVersion="1">
  <Class Name="lep.job.impl.JobOptionSpecQuantity" Collapsed="true" BaseTypeListCollapsed="true">
    <Position X="1" Y="1" Width="1.5" />
    <TypeIdentifier>
      <HashCode>BAAQAEAIEABACAAEAAAABAAAAAAAABAAAAAAAAQAAAA=</HashCode>
      <FileName>src\job\impl\JobOptionSpecQuantity.cs</FileName>
    </TypeIdentifier>
    <Lollipop Position="0.2" Collapsed="true" />
  </Class>
  <Class Name="lep.job.impl.JobOptionSpecSize" BaseTypeListCollapsed="true">
    <Position X="4.25" Y="3.75" Width="2.75" />
    <TypeIdentifier>
      <HashCode>AAACAABIQAAAAAAgAAAAAAAAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>src\job\impl\JobOptionSpecSize.cs</FileName>
    </TypeIdentifier>
    <Lollipop Position="0.2" Collapsed="true" />
  </Class>
  <Class Name="lep.job.impl.JobOptionSpecStock" BaseTypeListCollapsed="true">
    <Position X="7.25" Y="1" Width="4.25" />
    <TypeIdentifier>
      <HashCode>ABACgAAAAAAEACBAAAAgAAAAkAAAAJBAAAIQQQACAgE=</HashCode>
      <FileName>src\job\impl\JobOptionSpecStock.cs</FileName>
    </TypeIdentifier>
    <Lollipop Position="0.2" Collapsed="true" />
  </Class>
  <Class Name="lep.job.impl.JobTemplate" Collapsed="true" BaseTypeListCollapsed="true">
    <Position X="1" Y="2" Width="1.5" />
    <TypeIdentifier>
      <HashCode>BAAKAAAAAABAAAIAAAACAgQAAAIEAASAQAAEAEAAAgA=</HashCode>
      <FileName>src\job\impl\JobTemplate.cs</FileName>
    </TypeIdentifier>
    <Lollipop Position="0.2" Collapsed="true" />
  </Class>
  <Class Name="lep.job.impl.PaperSize" Collapsed="true" BaseTypeListCollapsed="true">
    <Position X="2.75" Y="2" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAACAAAQEAAAgAAAAIAEAAQAAAAEAAQAAAAEAAAAAgA=</HashCode>
      <FileName>src\job\impl\PaperSize.cs</FileName>
    </TypeIdentifier>
    <Lollipop Position="0.2" Collapsed="true" />
  </Class>
  <Class Name="lep.job.impl.Size" Collapsed="true" BaseTypeListCollapsed="true">
    <Position X="4.5" Y="2" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAIBAAACAQAQAAAAEAAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>src\job\impl\Size.cs</FileName>
    </TypeIdentifier>
    <Lollipop Position="0.2" Collapsed="true" />
  </Class>
  <Class Name="lep.job.impl.Stock" Collapsed="true" BaseTypeListCollapsed="true">
    <Position X="1" Y="3" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAACAAAAAAAAAAAAAAAAAAQAAAAEAAQgAAAEAAAAAgA=</HashCode>
      <FileName>src\job\impl\Stock.cs</FileName>
    </TypeIdentifier>
    <Lollipop Position="0.2" Collapsed="true" />
  </Class>
  <Font Name="Segoe UI" Size="9" />
</ClassDiagram>