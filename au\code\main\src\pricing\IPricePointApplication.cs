using lep.job;
using lep.user;
using lumen.csv;
using System.Collections.Generic;
using System.IO;

namespace lep.pricing
{
	public interface IPricePointApplication
	{
		IDictionary<int, decimal> CreateEnoughPoints(JobTypeOptions jobType, IJobOptionSpecQuantity specQ, IDictionary<int, decimal> prices);

		// convenience method for calling the above using fields from this job
		IList<IPricePoint> FindPricePoints(IJob job, IPaperSize finishSize);

		// Return all records that match this jobs Template, Stock, Size, Celloglaze, Colour.
		// For magazines this includes all records for different numbers of pages - discrimination is done later.
		// Records must be returned in ascending order of quantity.

		IList<IPricePoint> FindPricePoints(SiteLocation siteLocation, IJobTemplate template, IStock stock,
			IPaperSize size, int numcoloursides, JobCelloglazeOptions frontcelloglaze,
			JobCelloglazeOptions backcelloglaze);

		IList<IPricePoint> FindPricePoints(SiteLocation siteLocation, IJobTemplate template, IStock stock,
			IPaperSize size, int numcoloursides, JobCelloglazeOptions frontcelloglaze,
			JobCelloglazeOptions backcelloglaze, int pages);

		//PrintType - Digital or offset
		IList<IPricePoint> FindPricePoints(PrintType printType, IJob job, IPaperSize finishSize);
		IList<IPricePoint> FindPricePointsIgnoringPrintType(IJob job );

		IList<IPricePoint> FindPricePoints(SiteLocation siteLocation, PrintType printType, IJobTemplate template,
			IStock stock, IPaperSize size, int numcoloursides, JobCelloglazeOptions frontcelloglaze,
			JobCelloglazeOptions backcelloglaze);

		IList<IPricePoint> FindPricePoints(SiteLocation siteLocation, PrintType printType, IJobTemplate template,
			IStock stock, IPaperSize size, int numcoloursides, JobCelloglazeOptions frontcelloglaze,
			JobCelloglazeOptions backcelloglaze, int pages);

		//IProductMYOB FindProductMYOB(IJob job, IPaperSize finishSize);

		//IProductMYOB FindProductMYOB(SiteLocation siteLocation, IJobTemplate template, IStock stock, IPaperSize size, int numcoloursides, JobCelloglazeOptions frontcelloglaze, JobCelloglazeOptions backcelloglaze, int pages);
		//PrintType - Digital or Offset
		//IProductMYOB FindProductMYOB(PrintType printType, IJob job, IPaperSize finishSize);
		//IProductMYOB FindProductMYOB(SiteLocation siteLocation, PrintType printType, IJobTemplate template, IStock stock,
		//	IPaperSize size, int numcoloursides, JobCelloglazeOptions frontcelloglaze,
		//	JobCelloglazeOptions backcelloglaze, int pages);

		void SetPricePoints(PrintType printType, IJobTemplate template, IStock stock, IPaperSize size,
			int numcoloursides, JobCelloglazeOptions frontcelloglaze, JobCelloglazeOptions backcelloglaze, int numpages,
			Dictionary<int, decimal> prices, Dictionary<int, string> myobNumbers, IUser changeBy, string myob1,
			string myob2);

		bool IsValidPoints(PrintType printType, IJobTemplate template, IStock stock, IPaperSize size,
			Dictionary<int, decimal> prices);

		//cr 20
		//string ImportPriceCSV(string tmpFileName);
		string ImportPriceCSV(TextReader reader);

		void ExportPriceCSV(CsvSerialiser serial, int desiredTemplate = 0 /*all*/);

		void DeletePricePoints(PrintType printType, IJobTemplate template, IStock stock, IPaperSize paperSize,
			int numColourSides, string celloglazing, int numPages);

		void UpdateOrSavePricePoint(PrintType printType, IJobTemplate template, IStock stock, IPaperSize papersize,
			int numColourSides, string celloglazing, int numPages, int quantity, decimal price, string myobNumber);

		//void UpdateOrSaveProductMYOB(PrintType printType, IJobTemplate template, IPaperSize paperSize, IStock stock,
		//	int numColourSides, int numPages, string myobNumber, JobCelloglazeOptions frontCelloglaze,
		//	JobCelloglazeOptions backCelloglaze);

		// SR 1119107
		IPaperSize FindSmallestFit(IJob job);

		//   PrintType? GetPrintTypesAvailable (int templateId, int paperSizeId, int stockId);
		PrintType? GetPrintTypesAvailable(int templateId, int paperSizeId, int stockId, int quantity);

		PrintType? GetPrintTypesAll(int templateId, int paperSizeId, int stockId);

		decimal GetPriceMargin(string priceCode, int jobOptionID);
	}
}
