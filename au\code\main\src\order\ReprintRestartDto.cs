﻿using System.Collections.Generic;

namespace lep.order
{
	public class ReprintRestartDto
	{
		public int Id { get; set; }
		public string Command { get; set; }
		public decimal InvoicePrice { get; set; }
		public decimal ReprintCost { get; set; }
		public bool CopyPreflight { get; set; }
		public bool HasDispatch { get; set; }
		public string Reason { get; set; }
		public string Result { get; set; }
		public string NcrNo { get; set; }
		public string PredefinedReason { get; set; }
		public int Quantity { get; set; }
	}

	public class OrderReprintRestartDto
	{
		public List<ReprintRestartDto> Jobs { get; set; } = new List<ReprintRestartDto>();
	}
}
