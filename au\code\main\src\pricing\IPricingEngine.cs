using lep.job;
using lep.pricing.impl;
using System.Collections.Generic;
using System.Text;

namespace lep.pricing
{
	public interface IPricingEngine
    {
        IPricePointApplication PricePointApplication { set; }
        IJobApplication JobApplication { set; }

		// Provides a price for the job if it can be determined otherwise returns 0.
		// Does NOT update the price field inside IJob
		decimal PriceJob(IJob job, StringBuilder sb, out int modQty, string productpriceCode);

		//List<(Int32, decimal)> PriceJobList(IJob job, StringBuilder sb, out int modQty, string productpriceCode);

		IList<IPricePoint> GetPricePoints(IJob job);
		//string FindMYOB(IJob job, int qty);
	}
}
