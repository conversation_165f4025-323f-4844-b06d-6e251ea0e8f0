namespace lep.job.impl
{
	public class CelloOption : ICelloOption
    {
        private JobCelloglazeOptions celloBack;
        private JobCelloglazeOptions celloFront;

        public CelloOption()
        {
        }

        public CelloOption(JobCelloglazeOptions front, JobCelloglazeOptions back)
        {
            celloFront = front;
            celloBack = back;
        }

        public int Id { set; get; }

        public virtual JobCelloglazeOptions CelloFront
        {
            get { return celloFront; }
            set { celloFront = value; }
        }

        public virtual JobCelloglazeOptions CelloBack
        {
            get { return celloBack; }
            set { celloBack = value; }
        }

        public virtual IJobOptionSpecStock JobOptionSpecStock { get; set; }
    }
}