using lep.address;
using lep.contact;
using lep.courier;
using lep.freight;
using lep.job;
using lep.order.impl;
using lep.promotion;
using lep.user;
using System;
using System.Collections.Generic;

namespace lep.order
{
	public interface IOrder
	{
		int Id { get; set; }
		String OrderNr { get; }
		ICustomerUser Customer { get; set; }
		OrderStatusOptions Status { get; set; }

		string ExtraStatus { get; set; }

		//bool Enable { get; set; }
		OrderPaymentStatusOptions PaymentStatus { get; set; }

		 
		ListOfOrderPaymentRecord Payments { get; set; }

		IPackDetail PackDetail { get; set; }
		IList<IDispatchLabel> DispatchLabels { get; set; }

		IPhysicalAddress DeliveryAddress { get; set; }
		String DeliveryInstructions { get; set; }
		IContact Contact { get; set; }
		IContact WLContact { get; set; }

		DateTime? SubmissionDate { get; set; }
		DateTime? DispatchDate { get; set; }
		DateTime? FinishDate { get; set; }

		DateTime DateCreated { get; set; }
		DateTime DateModified { get; set; }
		bool FileRemoved { get; set; }
		CourierType Courier { get; set; }

		IList<IJob> Jobs { get; set; }

		//IEnumerable<IJob> ActiveJobs { get; }
		IList<IOrderConNote> ConNotes { get; set; }

		bool CustomerLogoRequired { get; set; }
		string CustomerLogoYourRef { get; set; }
		string RecipientName { get; set; }
		string RecipientPhone { get; set; }
		string PurchaseOrder { get; set; }
		string FreightPriceCode { get; set; }
		DateTime? DispatchEst { get; set; }
		decimal GST { get; set; }

		decimal? PriceOfJobs { get; } // Sum (Job.Price) - Promotion Deductions
		decimal? PriceOfGST { get; } // GST
		decimal? PriceOfJobsWL { get; }
		decimal? PriceOfGSTWL { get; }
		decimal? Price { get; set; }
		decimal? PriceWL { get; }
		decimal? PriceOfOnlyJobs { get; } // Only Jobs Price

		void AddPayment(decimal amount);

		decimal? PendingPayment { get; }

		decimal PickUpCharge { get; set; }

		IPromotion Promotion { get; set; }
		decimal PromotionJobPriceBenefit { get; set; }
		decimal PromotionBenefit { get; set; }

		bool IsQuote { get; set; }

		//string GetVersion();
		string PigeonHoleSize(bool hasSkid);

		//bool IsV2();
		// void SetExtraStatus ();

		string StatusC { get; }
		string StatusS { get; }
		string Css { get; }

		//bool IsWithdrawn { get; set; }
		DateTime? RevisedDispatchDate { get; }

		Facility? FacilityAtSubmit { get; set; }

		/// <summary>
		/// Is this a White labeled order?
		/// </summary>
		bool IsWLOrder { get; set; }

		/// <summary>
		/// if this is a  White labeled order then the Id of the sub customer if they have logged in
		/// </summary>
		int? WLCustomerId { get; set; }

		string WLCustomerName { get; set; }

		/// <summary>
		/// if this is a  White labeled order then the Temporary Session Id of the sub customer if they have NOT Logged in or annoymous
		/// </summary>
		string WLAnonymousUserId { get; set; }

		bool IsWLOrderPaidFor { get; set; }

		string WLOrderPaymentDetails { get; set; }
		string Invoiced { get; set; }
		string Invoiced2 { get; set; }

		bool IsDeleted { get; set; }

		IList<OrderCredit> OrderCredits { get; set; }

		#region job methods

		IJob NewJob(string name, int quantity, bool trackProgress, string specialInstructions, IJobTemplate template, IUser createdByUser = null);

		/// <summary>
		/// Returns a copy of the current list of jobs.
		/// </summary>
		/// <returns>A copy of the current list of Jobs</returns>
		IList<IJob> ListJobs(IUser user);

		/// <summary>
		/// Delete an existing Job.
		/// PRE:
		///  Order Status is Open
		///  User submitting belongs to Customer owning this Order
		///  Job exists in the list
		/// POST:
		///  Job deleted from the list of jobs
		///  Job's Order set to this
		/// </summary>
		/// <param name="job"></param>
		void DeleteJob(IJob job);

		#endregion job methods

		#region Order methods

		bool CanReturn { get; }

		/// <summary>
		/// Return an Order.
		///
		/// PRE:
		///  order submitted
		/// POST:
		///  Status set to OrderStatusOptions.Open
		///  Order return to customer
		/// </summary>
		void Return(IStaff staff);

		/// <summary>
		/// Changes status to Ready.
		/// LEP have determined this Order is ready for Prepress
		///
		/// PRE:
		///  Status is Submitted
		///  All Job statuses are PreflightDone
		/// POST:
		///  Status set to OrderStatusOptions.Ready
		///  DateModified set to CURRENT_TIMESTAMP
		///  Job Comments added
		///  TODO: invoke filling process
		/// </summary>
		void Ready(IStaff user);

		bool Withdraw(IUser user);

		bool CanWithdraw(IUser user);

		//bool Reactivate(IUser user);
		//bool CanReactivate(IUser user);

		bool CanUpdate(IUser user);

		string Summary { get; }
		string Description { get; }
		string IsDigitalStyle { get; }
		string IsSpecialInstructions { get; }
		string JobType { get; }
		string JobSize { get; }
		int NumJobs { get; }

		void SetDispatchLabels();

		string GetJobLog();

		bool ArtRequired { get; }

		#endregion Order methods

		bool HasSkid(Facility facility);
		string SplitsSummary();

		string ReceiverName { get; }
		string ReceiverPhone { get; }


		bool PackWithoutPallets { get; set; }

		bool HasSplitDelivery { get; }
	}
}
