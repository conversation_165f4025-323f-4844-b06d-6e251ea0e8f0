using lep.user;
using System;

namespace lep.job.impl
{
	public class StatusChangeEvent : IStatusChangeEvent
    {
        #region Constructors

        public StatusChangeEvent()
        {
        }

        public StatusChangeEvent(IUser author, IJob job)
        {
            Author = author;
            Job = job;
            Status = job.Status;
            StatusReachedAt = DateTime.Now;
        }

        #endregion Constructors

        #region Properties

        public virtual IUser Author { get; set; }

        public virtual int Id { get; set; }

        public virtual IJob Job { get; set; }

        private JobStatusOptions _Status;

        public virtual JobStatusOptions Status
        {
            get { return _Status; }
            set {
                _Status = value;
                SortOrder = (int)_Status;
            }
        }

        public virtual int SortOrder { get; set; }

        public virtual DateTime StatusReachedAt { get; set; }

        #endregion Properties
    }
}