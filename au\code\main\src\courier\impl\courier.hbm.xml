<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2"
				   namespace="lep.courier.impl"
				   assembly="lep"
				   auto-import="true"
				   default-cascade="all">

	<!--<class name="StarTrackPostcode" table="StarTrack_Postcode" mutable="false">
		<cache usage="read-only" />
		<id name="Postcode" type="string">
			<generator class="assigned" />
		</id>
		<property name="DirectZone" not-null="true" length="10" />
		<property name="AltOnForwardZone" not-null="true" length="10" />
		<property name="OnForwardZone" not-null="true" length="10" />
	</class>

	<class name="StarTrackRate" table="StarTrack_Rate" mutable="false">
		<cache usage="read-only" />
		<id name="Id" type="Int32" unsaved-value="0">
			<generator class="identity" />
		</id>
		<property name="AccountNumber" not-null="true" length="10" />
		<property name="ServiceCode" not-null="true" length="10" />
		<property name="OriginZone" not-null="true" length="10" />
		<property name="DestinationZone" not-null="true" length="10" />
		<property name="BasicCharge" type="Decimal" not-null="true" />
		<property name="BreakRate" type="Decimal" not-null="true" />
		<property name="CubicConvFactor" type="Decimal" not-null="true" />
	</class>

	<class name="FastwayPostcode" table="Fastway_Postcode" mutable="false">
		<cache usage="read-only" />
    <composite-id>
      <key-property name="Postcode" type="string" />
      <key-property name="Origin" type="string" />
    </composite-id>
		<property name="LabelType" type="lep.GenericEnum`1[lep.courier.FastwayLabelType], lep" not-null="true" />
	</class>-->
</hibernate-mapping>