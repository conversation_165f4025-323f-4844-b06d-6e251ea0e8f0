using System;
using lep.user;

namespace lep.quote.impl
{
    public class Quote : IQuote
    {
        public const int DEFAULT_VALID_DAYS = 30;

        private Quote()
        {
        }

        public Quote(ICustomerUser customer, string description)
        {
            if (customer == null)
            {
                throw new ArgumentNullException("Customer");
            }
            if (description == null)
            {
                throw new ArgumentOutOfRangeException("Description cannot be empty");
            }

            Customer = customer;
            Description = description;
        }

        public bool equals(Object obj)
        {
            if (null == obj)
            {
                return false;
            }
            if (!(obj is Quote))
            {
                return false;
            }
            else
            {
                var quote = (Quote) obj;
                return Id == quote.Id;
            }
        }

        #region IQuote Methods

        public void RespondToQuote(IStaff staff, string quotation)
        {
            //AssertPermission( "quote.respond" );
            RespondToQuote(staff, quotation, DateTime.Now.AddDays(DEFAULT_VALID_DAYS));
        }

        public void RespondToQuote(IStaff staff, string quotation, DateTime validDate)
        {
            //AssertPermission( "quote.respond" );
            if (quotation == null || quotation.Trim().Length == 0)
            {
                throw new ArgumentOutOfRangeException("Response cannot be empty");
            }

            Quotation = quotation.Trim();
            Status = QuoteStatusOptions.Quoted;
            DateModified = new DateTime();
            ValidUntil = validDate;
        }

        public void RejectQuote(IStaff staff, string quotation)
        {
            //AssertPermission( "quote.reject" );
            if (quotation != null)
            {
                Quotation = quotation.Trim();
            }
            Status = QuoteStatusOptions.Rejected;
            DateModified = new DateTime();
        }

        #endregion

        #region IQuote Members

        public virtual int Id { get; set; }

        public virtual String QuoteNr
        {
            get
            {
                if (Id == 0)
                {
                    return "-";
                }
                return String.Format("{0:D6}", Id);
            }
        }

        public virtual ICustomerUser Customer { get; set; }

        public virtual QuoteStatusOptions Status { get; set; }

        public virtual DateTime ValidUntil { get; set; }

        public virtual string Description { get; set; }

        public virtual string Quotation { get; set; }

        public virtual DateTime DateCreated { get; set; }

        public virtual DateTime DateModified { get; set; }

        #endregion
    }
}