namespace lep.job
{
	public enum CutOptions
	{
		[Description("None")] None = 0,

		[Description("Single gusset")] SingleGusset = 1,
		[Description("Single gusset (No Business Card slits)")] SingleGussetNoBCSlits = 41,
		[Description("Double gusset")] DoubleGusset = 2,
		[Description("Double gusset (No Business Card slits)")] DoubleGussetNoBCSlits = 42,

		[Description("Custom")] Custom = 3,

		[Description("Style A - A5")] StyleA = 4,
		[Description("Style B - A5")] StyleB = 5,

		[Description("Style C - A4")] StyleC = 6,
		[Description("Style D - A4")] StyleD = 7,
		[Description("Style E - A4")] StyleE = 8,
		[Description("Style F - A4")] StyleF = 9,
		[Description("Style G - A4")] StyleG = 10,
		[Description("Style H - A4")] StyleH = 11,
		[Description("Style I - A4")] StyleI = 12,
		[Description("Style J - A4")] StyleJ = 13,
		[Description("Style K - A4")] StyleK = 14,
		[Description("Style L - A4")] StyleL = 15,
		[Description("Style M - A4")] StyleM = 16,
		[Description("Style N - A4")] StyleN = 17,
		[Description("Style O - A4")] StyleO = 18,
		[Description("Style P - A4")] StyleP = 19,
		[Description("Style Q - A4")] StyleQ = 20,
		[Description("Style R - A4")] StyleR = 21,
		[Description("Style S - A4")] StyleS = 22,
		[Description("Style T - A4")] StyleT = 23,
		[Description("Style U - A4")] StyleU = 24,
		[Description("Style V - A4")] StyleV = 25,
		[Description("Style W - A4")] StyleW = 26,

		[Description("LEP Standard")] LEPStandard = 31,
	}
}
