namespace lep.freight
{
    public interface ICarton
    {
        string Code { get; set; }
        decimal Weight { get; set; }
        int Width { get; set; }
        int Height { get; set; }
        int Depth { get; set; }

        int IWidth { get; set; }
        int IHeight { get; set; }
        int IDepth { get; set; }
        int? Capacity { get; set; }

        int Level { get; set; }

        //ICarton Level2Carton { get; set; }
        //bool Wrap { get; set; }
        //decimal AllowWeight { get; set; }
        //‎int[] GetLeast2Dims();
        int[] GetLeast2Dims();

        float SizeLeft(ICarton innerCarton);

        bool CanFit(float w, float h);

        //string Rule { get; set; }
        //Func<IJob, bool> PredicateOnJob { get; set; }

        long InternalVolume { get; }
    }
}