using lep.extensionmethods;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using static lep.job.JobPrintOptions;
using static lep.job.JobTypeOptions;

namespace lep.job.impl
{
	[DebuggerDisplay("Template: {Id}, {Name}, has  {SizeOptions.Count} Sizes")]
	public class JobTemplate : IJobTemplate
	{
		public int ColourSide(JobPrintOptions front, JobPrintOptions back)
		{
			if (front == Unprinted && back == Unprinted)
			{
				//this should not happen
				return 0;
			}

			if (Is(BusinessCard, BusinessCardNdd, BusinessCardSdd, DoubleBusinessCard, Postcard))
			{
				return (front, back).Is((Printed, Printed), (BW, Printed)) ? 2 : 1;
			}
			else
			{
				return (front, back).Is((Printed, Printed), (Printed, BW), (BW, Printed), (BW, BW)) ? 2 : 1;
			}
		}

		public bool HasMultiplePages()
		{
			return Is(WiroMagazines, MagazineSeparate, Magazine, MagazineNDD, A4CalendarSelfCover, A4CalendarSeparateCover, Notepads, DuplicateNCRBooks, TriplicateNCRBooks, QuadruplicateNCRBooks);
		}

		public bool AllowsSamples()
		{
			if (Id == 0)
				return false;

			if (Is(BacklitPosters, MeshBanner, VinylOutdoor, PosterMattArt, PosterCanvas, PullUpBannerStandardStand, PullUpBannerPremiumStand, VinylSticker, RemovableWallDecals, VinylStickerOutdoor, RigidSigns, DuplicateNCRBooks, TriplicateNCRBooks, QuadruplicateNCRBooks))
			{
				return false;
			}
			return true;
		}

		#region JobTemplate Members

		public virtual int Id { get; set; }

		public virtual string Name { get; set; }

		public virtual string Category { get; set; }

		//[JsonIgnore]
		//public virtual IList<IJobOptionSpecSize> SizeOptions { get; set; } = new List<IJobOptionSpecSize>();

		[JsonIgnore]
		public virtual DateTime DateCreated { get; set; }

		[JsonIgnore]
		public virtual DateTime DateModified { get; set; }

		[JsonIgnore]
		public virtual bool FG_Production { get; set; }

		[JsonIgnore]
		public virtual bool PM_Production { get; set; }

		public bool Is(params JobTypeOptions[] list)
		{
			return list.Any(_ => (int)_ == Id);
		}

		public bool IsNot(params JobTypeOptions[] list)
		{
			return !list.Any(_ => (int)_ == Id);
		}

		#endregion JobTemplate Members
	}
}
