using lep.job.impl;
using lep.run;
using lep.user;
using NHibernate;
using NHibernate.Criterion;
using System.Collections.Generic;
using System.IO;

namespace lep.job
{
	/// <summary>
	///
	/// </summary>
	public interface IJobApplication
    {
        IJob GetJob(int Id);

        IJob Refresh(IJob job);

        IPaperSize GetPaperSize(int id);
		
        IPaperSize GetPaperSize(string name);

        IStock GetStock(int id);

        IStock GetStock(string name);
		IList<IJobOptionSpecSize> ListSizeOptions(IJobTemplate jobTemplate);

		IBindingOption GetBindingOption(int id);

        IBindingOption GetBindingOption(string name);

        IJobOptionSpecStock GetSpecStock(int id);

        void Save(IJob job);

        void Save(IJobOptionSpecSize jobOptionSpecSize);

        void Save(IJobOptionSpecStock jobOptionSpecStock);

        void Save(IJobTemplate jobTemplate);

        IJobTemplate GetJobTemplate(int id);

        IJobTemplate GetJobTemplate(JobTypeOptions id);

        IJobTemplate GetJobTemplate(string name);

        IList<IJobTemplate> ListJobTemplates();

        IList<IJobTemplate> ListAllTemplates();

        IList<IPaperSize> ListPaperSize();

        IList<IStock> ListStock();

        IList<Stock> ListStockForRuns();

        IList<IBindingOptionLookup> FindBindingOptions(int jobOptionId, int paperSizeId, int stockId,
            PrintType printType, int pages);

        bool CheckPageSizeExist(string name);

        bool CheckJobTypeExist(string name);

        bool CheckStockExist(string name);

        IList<CelloOption> SortCelloOption(IList<CelloOption> cellos);

        IList<KeyValuePair<string, string>> GetCelloOptions(int specStockId, bool isNormal = true);

        IList<int> GetColourOptions(IJobOptionSpecStock stock);

        string GetColourOptionScript(IJobOptionSpecStock stock);

        IList<IJob> FindReadyJobs(Facility facility, IRunSearchCriteria criteria, Order ordering);

        ICriteria FindReadyJobs2(Facility facility, IRunSearchCriteria criteria, Order ordering);

        bool IsArtworkValid(IJob job, bool briefCheckOnly);

        IList<FileInfo> GetListArtwork(IJob job);

        void SaveArtwork(IArtwork art, Stream file);
		void SaveArtwork(IArtwork art, FileInfo input);


        void TechCheckArtwork(IArtwork art);

        void SaveReadyArtwork(IArtwork art, Stream file);

        bool SetArtworks(IJob job);

        bool SetSupplyArt(IJob job);

        void ArchieveArtwork(IJob job);

        /// <summary>
        /// send a print job which contain job need to print
        /// </summary>
        void PrintJob(IJob job);

        // TODO: error handling on Printing, maybe return list of error message , or throws error exception

        /// <summary>
        /// Get import Job Option from CSV files
        /// </summary>
        string ImportJobOption();

        void CopyArtwork(IJob job);

        void CopyReOrderedArtwork(IJob originalJob, IJob newJob);

        //string Render(IJob job, bool forStaff);
        //string RenderStaff(IJob job);

        void CreateArtworkScript(IJob job);

        void PrintJobSheet(IList<IJob> jobs);

        void PrintJobRunSheet(IRun run);

        void PrintJobSheetAsNonBC(IList<IJob> jobs);

        //CR14
        //long JobSpaceConsumed( IJob job );
        //long JobDeleteFiles( IJob job );
        IJob SaveAndRefreshJob(IJob job);

        bool DoStuffOnFinalPreflight(IJob j);

        //bool IsFacilityAvailable(Facility facility, PrintType printType, IJobTemplate jobTemplate, IStock stock);
        bool IsFacilityAvailable(Facility facility, IJob job);

        string PositionToDisplayLabel(string str);

        // code lurking in ascx
        void SetReadyArt(IJob job, IArtwork art, string filename);

        void SetSupplyArt(IJob job, IArtwork art, string filename);

        //IList<int> GetDispatchedJobId();
        //JobDespatchUpdate GetJobDespatchUpdate(int id);
        IList<MailHouse> GetBrohureMailHouses();

        void TrySetPreflightDone(IJob _job, string status, IUser user);

        void RemoveArtwork(IJob job, IArtwork art);


		void CopyArtworks(IJob originalJob, IJob newJob);

		void MergeFrontAndBackArtwork(IJob job);
		bool ReadyArtExists(IJob job);
	}
}
