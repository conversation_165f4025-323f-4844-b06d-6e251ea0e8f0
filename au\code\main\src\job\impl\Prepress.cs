using lep.user;
using System;

namespace lep.job.impl
{
	public class Prepress : IPrepress
    {
        #region IJobPrepress Members

        public Prepress()
        {
        }

        public Prepress(IStaff user)
        {
            PrepressBy = user;
        }

        public virtual IStaff PrepressBy { get; set; }

        public virtual DateTime? PrepressDate { get; set; }

        public virtual IPrepressWork OneSided { get; set; } = new PrepressWork(0, false, false);

        public virtual IPrepressWork WorkAndTurn { get; set; } = new PrepressWork(0, false, false);

        public virtual IPrepressWork WorkAndTumble { get; set; } = new PrepressWork(0, false, false);

        public virtual IPrepressWork SheetWork { get; set; } = new PrepressWork(0, false, false);

        public virtual IPrepressWork Cover { get; set; } = new PrepressWork(0, false, false);

        #endregion IJobPrepress Members

        public override bool Equals(object obj)
        {
            if (obj == null)
                return false;

            Prepress o = obj as Prepress;
            if (o == null) return false;
            var thisPrepressById = PrepressBy?.Id ?? 0;
            var otherPrepressById = o.PrepressBy?.Id ?? 0;

            var thisPrepressDate = PrepressDate?.Date ?? null;
            var otherPrepressDate = o.PrepressDate?.Date ?? null;

            if (
                thisPrepressById == otherPrepressById &&
                thisPrepressDate == otherPrepressDate &&
                OneSided.Equals(o.OneSided) &&
                WorkAndTumble.Equals(o.WorkAndTumble) &&
                WorkAndTurn.Equals(o.WorkAndTurn) &&
                SheetWork.Equals(o.SheetWork) &&
                Cover.Equals(o.Cover)
                )
            {
                return true;
            }

            return false;
        }

		public override int GetHashCode()
		{
			return HashCode.Combine(PrepressBy, PrepressDate, OneSided, WorkAndTurn, WorkAndTumble, SheetWork, Cover);
		}
	}
}
