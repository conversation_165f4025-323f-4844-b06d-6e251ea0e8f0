using lep.configuration;
using lep.job;

using Serilog;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Printing;
using System.Reflection;

namespace lep.despatch.impl.label
{
	public class BasePigeonHoleLabel : PrintDocument, IDespatchLabel
	{
		#region Constructors

		public BasePigeonHoleLabel()
		{
		}

		#endregion Constructors

		#region Fields

		// private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		protected float bottomMargin;

		protected List<Align> columnAlignments = new List<Align>()
		{
			Align.left,
			Align.left,
			Align.left,
			Align.center,
			Align.center,
			Align.right
		};

		protected List<String> ColumnHeaders = new List<String>()
		{
			"Job",
			"Job Name",
			"Job Details",
			"Status",
			"Pkgs",
			"Reqd By",
		};

		protected int columnPoint;

		// Maintain a generic list to hold start/stop points for the column printing
		// This will be used for wrapping in situations where the DataGridView will not fit on a single page
		protected List<int[]> columnPoints = new List<int[]>();

		protected List<float> columnPointsWidth = new List<float>();

		protected List<float> columnsWidth = new List<float>();

		protected int currentRow;

		protected float currentY;

		protected Font defaultCellStyleFont = new Font("Tahoma", 14, FontStyle.Regular, GraphicsUnit.Point);

		protected float dtWidth;

		protected IJob job;

		protected float leftMargin;

		public PigeonHoleOrderDTO orderData;

		protected float pageHeight;

		protected int pageNumber;

		protected float pageWidth;

		private PropertyInfo[] pi;

		protected float rightMargin;

		protected float rowHeaderHeight;

		protected List<float> rowsHeight = new List<float>();

		private Type t;

		protected float topMargin;

		#endregion Fields

		#region Properties

		public IConfigurationApplication ConfigurationApplication { get; set; }

		public IJob Job
		{
			get { return job; }

			set {
				job = value;
				orderData = FormatPrintData(job);
			}
		}

		public string PrinterAndTray { get; set; }

		public String PrintFileName { get; set; }

		public Color WatermarkStampColor { get; set; }

		public string WatermarkStamp { get; set; }

		public bool HasSkid { protected get; set; }

		#endregion Properties

		#region Public Methods

		public bool DrawDataGridView(Graphics g)
		{
			try
			{
				Calculate(g);
				DrawHeader(g);
				DrawStamp(g);
				var bContinue = DrawRows(g);
				return bContinue;
			}
			catch (Exception ex)
			{
				Log.Error(ex.Message, ex);
				throw;
			}
		}

		public void SetupPrintProperties()
		{
			var ps = new PrinterSettings();
			PrintUtils.ConfigurePrinterSettings(PrinterAndTray, ref ps);

			ps.PrintToFile = ps.PrinterName == "Microsoft XPS Document Writer";
			if (ps.PrintToFile)
			{
				ps.PrintFileName = PrintFileName;
			}
			ps.DefaultPageSettings.Landscape = true;

			for (var i = 0; i <= ps.PaperSizes.Count - 1; i++)
			{
				if (ps.PaperSizes[i].RawKind == (int)PaperKind.A4)
				{
					ps.DefaultPageSettings.PaperSize = ps.PaperSizes[i];
					break;
				}
			}

			ps.DefaultPageSettings.Margins = new Margins(40, 40, 40, 40);
			ps.DefaultPageSettings.Landscape = true;

			DefaultPageSettings.PrinterSettings = ps;
			PrinterSettings = ps;
			// Calculating the PageWidth and the pageHeight
			if (!DefaultPageSettings.Landscape)
			{
				pageWidth = PrinterSettings.DefaultPageSettings.PaperSize.Width;
				pageHeight = PrinterSettings.DefaultPageSettings.PaperSize.Height;
			}
			else
			{
				pageHeight = PrinterSettings.DefaultPageSettings.PaperSize.Width;
				pageWidth = PrinterSettings.DefaultPageSettings.PaperSize.Height;
			}

			// Claculating the page margins
			try
			{
				leftMargin = DefaultPageSettings.Margins.Left - (int)DefaultPageSettings.HardMarginX;
				topMargin = DefaultPageSettings.Margins.Top - (int)DefaultPageSettings.HardMarginY;
				rightMargin = DefaultPageSettings.Margins.Right + (int)DefaultPageSettings.HardMarginX;
				bottomMargin = DefaultPageSettings.Margins.Bottom + (int)DefaultPageSettings.HardMarginY;
			}
			catch (Exception)
			{
				leftMargin = DefaultPageSettings.Margins.Left;
				topMargin = DefaultPageSettings.Margins.Top;
				rightMargin = DefaultPageSettings.Margins.Right;
				bottomMargin = DefaultPageSettings.Margins.Bottom;
			}

			// First, the current row to be printed is the first row in the DataGridView control
			currentRow = 0;
		}

		#endregion Public Methods

		#region Protected Methods

		// The function that calculate the height of each row (including the header row),
		// the width of each column (according to the longest text in all its cells including the header cell),
		// and the whole DataGridView width
		protected void Calculate(Graphics g)
		{
			try
			{
				// Just calculate once
				if (pageNumber == 0)
				{
					var tmpSize = new SizeF();
					Font tmpFont;
					tmpFont = defaultCellStyleFont;
					float tmpWidth;

					if (orderData.Jobs.Count == 0)
						return;
					t = orderData.Jobs[0].GetType();
					pi = t.GetProperties();

					rowHeaderHeight = dtWidth = 0;

					for (var i = 0; i < ColumnHeaders.Count; i++)
					{
						tmpSize = g.MeasureString(ColumnHeaders[i], tmpFont);
						tmpWidth = tmpSize.Width;
						rowHeaderHeight = Math.Max(rowHeaderHeight, tmpSize.Height);

						for (var j = 0; j < orderData.Jobs.Count; j++)
						{
							tmpSize = g.MeasureString("Anything", tmpFont);
							rowsHeight.Add(tmpSize.Height);

							var s = ColumnHeaders[i];

							tmpSize = g.MeasureString(s, tmpFont);
							if (tmpSize.Width > tmpWidth)
								tmpWidth = tmpSize.Width;
						}
						//if (dt.Columns[i].Visible)
						dtWidth += tmpWidth;
						columnsWidth.Add(tmpWidth);
					}

					var totalWidth = 0.0F;
					for (var i = 0; i < columnsWidth.Count; i++)
						totalWidth += columnsWidth[i];

					if (totalWidth < pageWidth)
					{
						var increase = (pageWidth - leftMargin - rightMargin - totalWidth) / pi.Length;

						for (var i = 0; i < columnsWidth.Count; i++)
							columnsWidth[i] += increase;
					}

					rowsHeight.Clear();
					for (var j = 0; j < orderData.Jobs.Count; j++)
					{
						float tmpHeight = 0;

						for (var i = 0; i < pi.Length; i++)
						{
							var p = pi[i];
							var s = (string)p.GetValue(orderData.Jobs[j], null);
							tmpSize = g.MeasureString(s, tmpFont);
							if (tmpSize.Height > tmpHeight)
								tmpHeight = tmpSize.Height;
						}

						rowsHeight.Add(tmpHeight);
					}
					// Define the start/stop column points based on the page width and the DataGridView Width
					// We will use this to determine the columns which are drawn on each page and how wrapping will be handled
					// By default, the wrapping will occurr such that the maximum number of columns for a page will be determine
					int k;

					var mStartPoint = 0;
					for (k = 0; k < pi.Length; k++)
						//if (dt.Columns[k].Visible) {
						mStartPoint = k;
					//break;
					//}

					var mEndPoint = pi.Length;
					for (k = pi.Length - 1; k >= 0; k--)
						//if (dt.Columns[k].Visible) {
						mEndPoint = k + 1;
					//	break;
					//}

					var mTempWidth = dtWidth;
					var mTempPrintArea = (float)pageWidth - (float)leftMargin - (float)rightMargin;

					// We only care about handling where the total datagridview width is bigger then the print area
					if (dtWidth > mTempPrintArea)
					{
						mTempWidth = 0.0F;
						for (k = 0; k < pi.Length; k++)
						{
							//if (dt.Columns[k].Visible) {
							mTempWidth += columnsWidth[k];
							// If the width is bigger than the page area, then define a new column print range
							if (mTempWidth > mTempPrintArea)
							{
								mTempWidth -= columnsWidth[k];
								columnPoints.Add(new int[] { mStartPoint, mEndPoint });
								columnPointsWidth.Add(mTempWidth);
								mStartPoint = k;
								mTempWidth = columnsWidth[k];
							}
							//}
							// Our end point is actually one index above the current index
							mEndPoint = k + 1;
						}
					}
					// Add the last set of columns
					columnPoints.Add(new int[] { mStartPoint, mEndPoint });
					columnPointsWidth.Add(mTempWidth);
					columnPoint = 0;
				}
			}
			catch (Exception ex)
			{
				Log.Error(ex.Message, ex);
			}
		}

		protected void DrawHeader(Graphics g)
		{
			currentY = (float)topMargin;
			DrawOrderNoCustNameSkid(g);

			pageNumber++;

			var currentX = (float)leftMargin;
			var theLinePen = new Pen(Brushes.Gray, 1);
			var headerFont = new Font(defaultCellStyleFont, FontStyle.Bold);

			// Calculating and drawing the HeaderBounds
			var headerBounds = new RectangleF(currentX, currentY, columnPointsWidth[columnPoint], rowHeaderHeight);

			// Setting the format that will be used to print each cell of the header row
			var cellFormat = new StringFormat();
			cellFormat.Trimming = StringTrimming.Word;
			cellFormat.FormatFlags = StringFormatFlags.NoWrap | StringFormatFlags.LineLimit | StringFormatFlags.NoClip;

			// Printing each visible cell of the header row
			RectangleF cellBounds;
			float columnWidth;
			for (var i = 0; i < ColumnHeaders.Count; i++)
			{
				var p = pi[i];

				columnWidth = columnsWidth[i];
				if (columnAlignments[i] == Align.right)
					cellFormat.Alignment = StringAlignment.Far;
				else if (columnAlignments[i] == Align.center)
					cellFormat.Alignment = StringAlignment.Center;
				else
					cellFormat.Alignment = StringAlignment.Near;

				cellBounds = new RectangleF(currentX, currentY, columnWidth, rowHeaderHeight);

				// Printing the cell text
				g.DrawString(ColumnHeaders[i], headerFont, Brushes.Brown, cellBounds, cellFormat);

				g.DrawRectangle(theLinePen, currentX, currentY, columnWidth, rowHeaderHeight);

				currentX += columnWidth;
			}

			currentY += rowHeaderHeight;
		}

		protected void DrawPackingDetailsDispatchBoxes(Graphics g)
		{
			var height = 200;

			currentY += 10;
			var w = pageWidth - leftMargin - rightMargin;
			var pen = new Pen(Brushes.Black);
			var font = new Font("Tahoma", 12);

			// draw packing details box
			var rectPackingDetails = new Rectangle((int)leftMargin, (int)currentY, (int)w / 2 - 5, height);
			g.DrawRectangle(pen, rectPackingDetails);

			var orderPackDetails = "Packing Details\n" + orderData.PackDetails;
			g.DrawString(orderPackDetails, font, Brushes.Black, rectPackingDetails);

			// draw dispatch use only box
			var rectDispatchUse = new Rectangle((int)(leftMargin + w / 2 + 5), (int)currentY, (int)w / 2 - 5, height);
			g.DrawRectangle(pen, rectDispatchUse);
			g.DrawString("Dispatch Use Only", font, Brushes.Black, rectDispatchUse);

			currentY += height;
		}

		// The function that print a bunch of rows that fit in one page
		// When it returns true, meaning that there are more rows still not printed, so another PagePrint action is required
		// When it returns false, meaning that all rows are printed and no further PagePrint action is required
		protected bool DrawRows(Graphics g)
		{
			var LinePen = new Pen(Color.Gray, 1);

			// The style paramters that will be used to print each cell
			Font rowFont;
			Color rowForeColor;
			Color rowBackColor;

			// Setting the format that will be used to print each cell
			var CellFormat = new StringFormat();
			CellFormat.Trimming = StringTrimming.Word;
			CellFormat.FormatFlags = StringFormatFlags.LineLimit; //  StringFormatFlags.NoWrap |

			// Printing each visible cell
			RectangleF rowBounds;
			float currentX;
			float columnWidth;
			while (currentRow < orderData.Jobs.Count)
			{
				// Setting the row font style
				rowFont = defaultCellStyleFont;
				if (rowFont == null)
					rowFont = defaultCellStyleFont;

				// Setting the RowFore style
				rowForeColor = Color.Black;

				// Setting the RowBack (for even rows) and the RowAlternatingBack (for odd rows) styles
				rowBackColor = Color.White;

				// calating the starting x coordinate that the printing process will start from
				currentX = (float)leftMargin;

				// Calculating the entire currentRow bounds
				rowBounds = new RectangleF(currentX, currentY, columnPointsWidth[columnPoint], rowsHeight[currentRow]);

				for (var currentCell = 0; currentCell < pi.Length; currentCell++)
				{
					var p = pi[currentCell];

					// Check the CurrentCell alignment and apply it to the CellFormat
					if (columnAlignments[currentCell] == Align.right)
						CellFormat.Alignment = StringAlignment.Far;
					else if (columnAlignments[currentCell] == Align.center)
						CellFormat.Alignment = StringAlignment.Center;
					else
						CellFormat.Alignment = StringAlignment.Near;

					columnWidth = columnsWidth[currentCell];
					var CellBounds = new RectangleF(currentX, currentY, columnWidth, rowsHeight[currentRow]);

					// Printing the cell text
					var cellContent = (string)p.GetValue(orderData.Jobs[currentRow], null);

					g.DrawString(cellContent, rowFont, Brushes.Black, CellBounds, CellFormat);

					// Drawing the cell bounds
					g.DrawRectangle(LinePen, currentX, currentY, columnWidth, rowsHeight[currentRow]);
					//g.DrawLine( LinePen, CurrentX, currentY, CurrentX, currentY + rowsHeight[currentRow] );

					currentX += columnWidth;
				}
				currentY += rowsHeight[currentRow];

				// Checking if the currentY is exceeds the page boundries
				// If so then exit the function and returning true meaning another PagePrint action is required
				if ((int)currentY > pageHeight - topMargin - bottomMargin)
				{
					currentRow++;
					return true;
				}

				currentRow++;
			}

			columnPoint++; // Continue to print the next group of columns
			if (columnPoint == columnPoints.Count)
			{
				// Which means all columns are printed
				columnPoint = 0;
				return false;
			}
			else
				return true;
		}

		protected void DrawStamp(Graphics g)
		{
			if (!String.IsNullOrEmpty(WatermarkStamp))
			{
				using (var watermarkFont = new Font("Arial", 100, FontStyle.Bold))
				using (SolidBrush brush = new SolidBrush(Color.FromArgb(64, WatermarkStampColor)))
				{
					var s = g.MeasureString(WatermarkStamp, watermarkFont);
					float optimalY = 500;

					g.TranslateTransform((pageWidth - s.Width) / 2, optimalY);
					g.RotateTransform(-30);

					g.DrawString(WatermarkStamp, watermarkFont, brush, 0, 0);
					g.ResetTransform();
				}
			}
		}

		protected override void OnPrintPage(PrintPageEventArgs e)
		{
			base.OnPrintPage(e);
			var more = DrawDataGridView(e.Graphics);
			if (more == true)
				e.HasMorePages = true;
			if (more == false)
			{
				PrintExtraThingsAfterGrid(e.Graphics);
			}
		}

		protected virtual void PrintExtraThingsAfterGrid(Graphics g)
		{
		}

		protected void PrintOrderSubmitDate(Graphics g)
		{
			var dateSubmitted = DateTime.Parse(orderData.SubmitDate);
			var strLeft = String.Format("Order Submit Date {0:dddd,dd-MMM-yyyy}", dateSubmitted);
			var strRight = true ? "Pegion Hole" : "Job Bag";

			var format = new StringFormat();
			format.Trimming = StringTrimming.Word;
			format.FormatFlags = StringFormatFlags.NoWrap | StringFormatFlags.LineLimit | StringFormatFlags.NoClip;
			format.Alignment = StringAlignment.Near;

			var height = 25;
			currentY += 10;
			var w = pageWidth - leftMargin - rightMargin;
			var pen = new Pen(Brushes.Black);
			var font = new Font("Tahoma", 12);

			// draw left
			var rectOrderSubmitDate = new Rectangle((int)leftMargin, (int)currentY, (int)w, height);
			//g.DrawRectangle( pen, rectOrderSubmitDate );

			g.DrawString(strLeft, font, Brushes.Black, rectOrderSubmitDate, format);
			format.Alignment = StringAlignment.Far;
			g.DrawString(strRight, font, Brushes.Black, rectOrderSubmitDate, format);

			currentY += height;
		}

		#endregion Protected Methods

		#region Private Methods

		private void DrawOrderNoCustNameSkid(Graphics g)
		{
			// TOP LEFT : Draw Order Number in big fonts
			var orderNumber = orderData.OrderNo;
			var orderNumberHeight = 0f;
			var orderNumberWidth = 0f;
			using (var fontBig = new Font("Arial", 48, FontStyle.Bold, GraphicsUnit.Point))
			{
				orderNumberHeight = g.MeasureString(orderNumber, fontBig).Height;
				orderNumberWidth = g.MeasureString(orderNumber, fontBig).Width;
				
				var orderNumberRect = new RectangleF((float)leftMargin, currentY, orderNumberWidth, orderNumberHeight);
				//g.FillRectangle( Brushes.Gainsboro, orderNumberRect );
				SolidBrush brush = new SolidBrush(Color.Blue);
				g.DrawString(orderNumber, fontBig, brush, orderNumberRect);
			}

			// TOP RIGHT : Draw skid
			var topRightText = orderData.TopRight;
			var topRightTextWidth = 0f;
			var topRightTextHeight = 0f;
			using (var topRightFont = new Font("Arial", 36, FontStyle.Bold, GraphicsUnit.Point))
			{
				topRightTextHeight = g.MeasureString(topRightText, topRightFont).Height;
				topRightTextWidth = g.MeasureString(topRightText, topRightFont).Width;
				var topRightTextRect = new RectangleF(pageWidth - rightMargin - topRightTextWidth, currentY,
					topRightTextWidth, topRightTextHeight);
				g.FillRectangle(Brushes.Gainsboro, topRightTextRect);
				g.DrawString(topRightText, topRightFont, new SolidBrush(Color.Blue), topRightTextRect);
			}

			// Printing the title (if IsWithTitle is set to true)
			using (var custNameFont = new Font("Arial", 32, FontStyle.Bold, GraphicsUnit.Point))
			{
				var custName = orderData.Customer;
				var custNameRect = RectangleF.Empty;
				var promoRet = RectangleF.Empty;

				var custNameTitleFormat = new StringFormat();
				custNameTitleFormat.Trimming = StringTrimming.Word;
				custNameTitleFormat.FormatFlags = StringFormatFlags.LineLimit | StringFormatFlags.NoClip;
				custNameTitleFormat.Alignment = StringAlignment.Center;

				var custNameHeight = g.MeasureString(custName, custNameFont).Height;

				custNameRect = new RectangleF(leftMargin + orderNumberWidth, currentY,
					pageWidth - rightMargin - leftMargin - orderNumberWidth - topRightTextWidth, custNameHeight * 2);
				g.DrawString(custName, custNameFont, new SolidBrush(Color.Black), custNameRect, custNameTitleFormat);

				//promotion code under customername
				if (!String.IsNullOrEmpty(orderData.PromoCode))
				{
					using (var promoFont = new Font("Arial", 15, FontStyle.Bold, GraphicsUnit.Point))
					{
						promoRet = new RectangleF(custNameRect.X,
							currentY +
							g.MeasureString(custName, custNameFont, Convert.ToInt32(custNameRect.Width),
								custNameTitleFormat).Height, custNameRect.Width,
							g.MeasureString(orderData.PromoCode, promoFont).Height);
						g.DrawString(orderData.PromoCode, promoFont, new SolidBrush(Color.Black), promoRet,
							new StringFormat() { Alignment = StringAlignment.Center });
					}
				}

				currentY = Math.Max(Math.Max(currentY + orderNumberHeight, currentY + custNameRect.Height),
					promoRet.Height + promoRet.Y);
			}

			// Draw Second Row : Order submitted   and courier
			// LEFT Bit first
			var secondRowHeight = 0f;
			using (var secondRowFont = new Font("Arial", 20, FontStyle.Bold, GraphicsUnit.Point))
			{
				string secondRowText;
				secondRowText = String.Format("Order Submitted: {0}", orderData.SubmitDate);

				secondRowHeight = g.MeasureString(secondRowText, secondRowFont).Height;
				var secondRowLeftRect = new RectangleF(leftMargin, currentY, pageWidth - rightMargin - leftMargin,
					secondRowHeight);

				//g.FillRectangle( Brushes.Gainsboro, secondRowLeftRect );
				g.DrawString(secondRowText, secondRowFont, new SolidBrush(Color.Blue), secondRowLeftRect);

				//right bit
				secondRowText = String.Format("Courier : {0}", orderData.Courier);
				var secondRowCourierTextWidth = g.MeasureString(secondRowText, secondRowFont).Width;

				var secondRowRightRect = new RectangleF(pageWidth - rightMargin - secondRowCourierTextWidth, currentY,
					secondRowCourierTextWidth, secondRowHeight);

				g.DrawString(secondRowText, secondRowFont, new SolidBrush(Color.Black), secondRowRightRect);
			}
			currentY += secondRowHeight;
		}

		private PigeonHoleOrderDTO FormatPrintData(IJob j)
		{
			try
			{
				var orderData = new PigeonHoleOrderDTO();
				var jOrder = j.Order;
				orderData.OrderNo = jOrder.OrderNr;
				orderData.Customer = j.Order.Customer.Name;

				orderData.Courier = "";
				if (j.Facility != null)
				{
					orderData.Courier = j.Facility == Facility.FG ? jOrder.PackDetail.FGCourier : jOrder.PackDetail.PMCourier;
				}

				if (jOrder.SubmissionDate.HasValue)
				{
					orderData.SubmitDate = ((DateTime)jOrder.SubmissionDate.Value).ToString("ddd dd-MMM-yy");
				}
				else
				{
					orderData.SubmitDate = "";
				}

				orderData.TopRight = jOrder.PigeonHoleSize(HasSkid);
				orderData.BottomRight = "";
				orderData.PackDetails = jOrder.PackDetail.PackLog(j.Facility);
				orderData.PromoCode = j.Order.Promotion != null ? jOrder.Promotion.PromotionCode : String.Empty;

				Log.Information(string.Format("Print PigeonHole from J{0} at status {1}", j.Id, j.Status));

				foreach (var oj in j.Order.Jobs)
				{
					Log.Information(string.Format("        J{0} is at status {1}", oj.Id, oj.Status));
					var jobData = new PigeonHoleJobDTO();
					jobData.JobNumber = String.Concat("J" + oj.JobNr, "\n", oj.Runs.Count > 0 ? "R" + oj.Runs[0].RunNr : String.Empty);
					//if (!String.IsNullOrEmpty(oj.MYOB))
					//{
					//	jobData.JobNumber += "\n" + oj.MYOB;
					//}

					jobData.JobName = oj.Name;
					if (oj.SendSamples == true)
					{
						jobData.JobName += "(S)";
					}
					jobData.JobDetails = oj.Template.Name.ToString() + " - " + oj.FinishedSize.PaperSize.Name + "\n" +
										 oj.FinalStock.Name;
					//todo: iwen
					//jobData.Pkgs =
					//	oj.Order.PackDetail.Packages.Where(p => p.Content.ContainsKey(oj)).Sum(p => p.Quantity).ToString();
					jobData.Status = String.Concat(oj.Status.ToString(), "\n", oj.StatusDate.ToString("dd/MM/yy HH:mm"));
					if (oj.RequiredByDate != null)
						jobData.RequiredBy = oj.RequiredByDate.Value.ToString("dd/MMM/yy");
					else
						jobData.RequiredBy = "";

					orderData.Jobs.Add(jobData);
				}
				return orderData;
			}
			catch (Exception ex)
			{
				Log.Error(ex.Message, ex); 
				throw;

			}
		}

		void IDespatchLabel.Print()
		{
			Print();
		}

		#endregion Private Methods
	}
}
