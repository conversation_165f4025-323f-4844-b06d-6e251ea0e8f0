namespace lep.courier.csv
{
	using System;
	using System.IO;
	using System.Collections.Generic;
	using System.Text;
	using System.Data;
	using System.Data.SqlClient;
	using System.Linq;

	using NHibernate;
	using lumen.csv;
	using lep.courier.impl;

	public class StarTrackRateReader : lep.www.BulkCopyImport
	{
		public StarTrackRateReader()
			: base( "StarTrack_Rate", typeof(StarTrackRate) )
		{
		}

		protected override DataTable CreateDataTable()
		{
			
			DataTable table = new DataTable();

			DataColumn IdCol = new DataColumn( "Id",typeof( int ) );
			IdCol.AllowDBNull = false;
			IdCol.AutoIncrement = true;
			table.Columns.Add( IdCol );

			DataColumn accountCol = new DataColumn( "AccountNumber",typeof( string ) );
			accountCol.MaxLength = 10;
			accountCol.AllowDBNull = false;
			table.Columns.Add( accountCol );

			DataColumn serviceCol = new DataColumn( "ServiceCode",typeof( string ) );
			serviceCol.MaxLength = 10;
			serviceCol.AllowDBNull = false;
			table.Columns.Add( serviceCol );

			DataColumn originCol = new DataColumn( "OriginZone",typeof( string ) );
			originCol.MaxLength = 10;
			originCol.AllowDBNull = false;
			table.Columns.Add( originCol );

			DataColumn destCol = new DataColumn( "DestinationZone",typeof( string ) );
			destCol.MaxLength = 10;
			destCol.AllowDBNull = false;
			table.Columns.Add( destCol );

			DataColumn basicCol = new DataColumn( "BasicCharge",typeof( decimal ) );
			basicCol.AllowDBNull = false;
			table.Columns.Add( basicCol );

			DataColumn breakCol = new DataColumn( "BreakRate",typeof( decimal ) );
			breakCol.AllowDBNull = false;
			table.Columns.Add( breakCol );

			DataColumn facCol = new DataColumn( "CubicConvFactor",typeof( decimal ) );
			facCol.AllowDBNull = false;
			table.Columns.Add( facCol );

			return table;
		}

		protected override bool VerifyHeader( string[] values )
		{
			string[] headers = new string[] { "Account Number","Service Code","Origin Zone","Destination Zone","Rates Version", "Special Unit Code", "Cubic Conv Factor", "Specific Min Charge", "Charge Method", "Basic Charge", "Upper Limit", "Break Type", "Break Rate" };

			for (int i = 0; i < headers.Length; i++) {
				if (i >= values.Length || headers[i].ToLower() != values[i].ToLower()) {
					AddError( "Header row must start with: Account Number,Service Code,Origin Zone,Destination Zone,Rates Version,Special Unit Code,Cubic Conv Factor,Specific Min Charge,Charge Method,Basic Charge,Upper Limit,Break Type,Break Rate" );
					return false;
				}
			}

			return true;
		}

		protected override bool ProcessRow( DataRow dr, string[] values )
		{
			if (values.Length < 13) {
				AddError( "Not enough columns" );
				return false;
			}

			dr[1] = values[0].ToString();
			dr[2] = values[1].ToString();
			dr[3] = values[2].ToString();
			dr[4] = values[3].ToString();

			decimal tmp;

			if (decimal.TryParse(values[9], out tmp) && tmp > 0) {
				dr[5] = tmp;
			} else {
				AddError( "Invalid Basic Charge" );
				return false;
			}

			if (decimal.TryParse(values[12], out tmp) && tmp > 0) {
				dr[6] = tmp;
			} else {
				AddError( "Invalid Break Rate" );
				return false;
			}

			if (decimal.TryParse(values[6], out tmp) && tmp > 0) {
				dr[7] = tmp;
			} else {
				AddError( "Invalid Cubic Conv Factor" );
				return false;
			}
	
			return true;
		}
	}
}
