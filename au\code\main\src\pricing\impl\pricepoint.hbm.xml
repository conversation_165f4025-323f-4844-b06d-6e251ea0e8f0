<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2"
				   namespace="lep.pricing"
				   assembly="lep"
				   auto-import="true"
				   default-cascade="all">

	<class name="IPricePoint"  table="pricepoint" discriminator-value="null">
		<cache usage="read-write" />
		<id name="Id" type="Int32" unsaved-value="0">
			<generator class="identity" />
		</id>
		<discriminator column="Id" type="Int32" insert="false" />
		<timestamp name="DateModified" column="DateModified" />
		<many-to-one name="Template" column="JobOptionId" class="lep.job.IJobTemplate" not-null="true" cascade="none" />
		<many-to-one name="Stock" column="StockId" class="lep.job.IStock, lep" not-null="true" cascade="none" />
		<many-to-one name="PaperSize" column="PaperSizeId" class="lep.job.IPaperSize" not-null="true" cascade="none" />
		<property name="NumPages" column="NumPages"  not-null="true"  type="Int32" />
		<property name="NumColourSides" column="NumColourSides"  not-null="true"  type="Int32" />
		<property name="Celloglazing" column="celloglaze" not-null="true" />
		<property name="Quantity" column="Quantity"  not-null="true"  type="Int32" />
		<property name="Price" column="Price"  not-null="true"  type="Decimal(10,2)" />
		<!--<property name="MYOB" column="MYOB" not-null="true" type="string" />-->
		<many-to-one name="ChangeBy" class="lep.user.IUser, lep" column="ChangeBy" not-null="true" cascade="none" />
		<property name="SiteLocation" type="lep.GenericEnum`1[lep.SiteLocation], lep" not-null="false" />
        <property name="PrintType" type="lep.GenericEnum`1[lep.job.PrintType], lep" not-null="false" />
        <property name="DateCreated" column="DateCreated" type="lumen.hibernate.type.DateTimeType, lumen" not-null="false" update="false" insert="false" />
		<subclass name="lep.pricing.impl.PricePoint, lep" proxy="IPricePoint" discriminator-value="not null" />
	</class>

	<!--<class name="IProductMYOB"  table="productMYOB" discriminator-value="null">
		<cache usage="read-write" />
		<id name="Id" type="Int32" unsaved-value="0">
			<generator class="identity" />
		</id>
		<discriminator column="Id" type="Int32" insert="false" />
		<timestamp name="DateModified" column="DateModified" />
		<many-to-one name="Template" column="JobOptionId" class="lep.job.IJobTemplate" not-null="true" cascade="none" />
		<many-to-one name="Stock" column="StockId" class="lep.job.IStock, lep" not-null="true" cascade="none" />
		<many-to-one name="PaperSize" column="PaperSizeId" class="lep.job.IPaperSize" not-null="true" cascade="none" />
		<property name="NumPages" column="NumPages"  not-null="true"  type="Int32" />
		<property name="NumColourSides" column="NumColourSides"  not-null="true"  type="Int32" />
		<property name="Celloglazing" column="celloglaze" not-null="true" />
		<property name="MYOB1" length="50" not-null="true" />
		<property name="MYOB2" length="50" not-null="true" />
		<property name="SiteLocation" type="lep.GenericEnum`1[lep.SiteLocation], lep" not-null="false" />
        <property name="PrintType" type="lep.GenericEnum`1[lep.job.PrintType], lep" not-null="false" />
		<property name="DateCreated" column="DateCreated" type="lumen.hibernate.type.DateTimeType, lumen" not-null="false" update="false" insert="false" />
		<subclass name="lep.pricing.impl.ProductMYOB, lep" proxy="IProductMYOB" discriminator-value="not null" />
	</class>-->
</hibernate-mapping>
