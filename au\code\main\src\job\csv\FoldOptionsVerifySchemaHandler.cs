using System.Text.RegularExpressions;

namespace lep.job.csv
{
	public class FoldOptionsVerifySchemaHandler : BaseVerifySchemaHandler
	{
		public FoldOptionsVerifySchemaHandler()
			: base()
		{
			fileType = "Spec Fold File";
			headers = new string[]
			{
				"fid", "fold1", "fold2", "fold3", "fold4", "fold5",  "fold6",  "fold7"
			};
			regexs = new Regex[]
			{
				new Regex(@"^\d+$"), new Regex(@"^(\w+)||(\d+)$"), new Regex(@"^(\w+)||(\d+)$"),
				new Regex(@"^(\w+)||(\d+)$"), new Regex(@"^(\w+)||(\d+)$"), new Regex(@"^(\w+)||(\d+)$"), new Regex(@"^(\w+)||(\d+)$"), new Regex(@"^(\w+)||(\d+)$")
			};
		}
	}
}
