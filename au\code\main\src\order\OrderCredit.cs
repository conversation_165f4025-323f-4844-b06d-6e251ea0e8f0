﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using lep.user;

namespace lep.order
{
	public class OrderCredit
	{
		public virtual int Id { get; set; }

	

		public virtual IOrder Order { get; set; }
		public virtual ICustomerUser Customer { get; set; }

		public virtual string Type { get; set; }

		public virtual decimal Amount { get; set; }
		public virtual decimal GST { get; set; }
		public virtual decimal Total { get; set; }

		public virtual string Description { get; set; }
		
		public virtual string Account { get; set; }


		public virtual bool Approved { get; set; }
		public virtual bool Invoiced { get; set; }

		public virtual DateTime? InvoicedDate { get; set; }
		public virtual DateTime DateCreated { get; set; }
		public virtual DateTime DateModified { get; set; }
		public virtual IStaff IssuedBy { get; set; }
	}


}
