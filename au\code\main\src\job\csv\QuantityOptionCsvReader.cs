using System.Collections.Generic;

namespace lep.job.csv
{
    public class QuantityOptionCsvReader : BaseCsvReader
    {
        private QuantityOptionImportHandler importHandler;
        private QuantityOptionVerifySchemaHandler verifyHandler;

        public QuantityOptionCsvReader()
            : base()
        {
            verifyHandler = new QuantityOptionVerifySchemaHandler();
            importHandler = new QuantityOptionImportHandler();

            skipHandler.Handler = verifyHandler;
            verifyHandler.Handler = importHandler;
        }

        public IDictionary<int, IJobOptionSpecQuantity> Result { get; private set; }

        public override void StartDocument()
        {
            importHandler.JobApplication = jobApp;
            base.StartDocument();
        }

        public override void EndDocument()
        {
            Result = importHandler.Result;
            base.EndDocument();
        }
    }
}