using System;
using System.Text;
using lep.extensionmethods;

namespace lep.job
{
    public class MailHouse
    {
        virtual public int Id { get; set; }
        virtual public string Name { get; set; }
        virtual public string Instructions { get; set; }

        virtual public int InBundlesOf { get; set; }
        virtual public int MaxInBundle { get; set; }
        virtual public int MaxWeightPerCarton { get; set; }

        public MailHouse()
        {
        }
    }

    public enum PackingInstruction
    {
        [Description("Standard")]
        Standard,

        [Description("Banding")]
        Banding,

        [Description("Shrink Wrapping")]
        ShrinkWrapping,

        [Description("Bulk Pack Carton")]
        BulkPackCarton,

        [Description("Bulk Pack Pallet")]
        BulkPackPallet
    }

    public class BrochureDistPackInfo
    {
        public BrochureDistPackInfo()
        {
			//if (brochureDistPackInfo?.MailHouse == null && j.BrochureDistPackInfo.PackingInstruction == null)
			//{
			//PackingInstruction =  job.PackingInstruction.Standard;
			//}	
			
		}

        public BrochureDistPackInfo(BrochureDistPackInfo src)
        {
            if (src == null)
                return;
            MailHouse = src.MailHouse;
            PackingInstruction = src.PackingInstruction;
            BundlesOf = src.BundlesOf;

			if (src?.MailHouse == null && src.PackingInstruction == null)
			{
				PackingInstruction = job.PackingInstruction.Standard;
			}

			//if(MailHouse != null)
			//{
				
			//}
		}

        public MailHouse MailHouse { get; set; }
		public PackingInstruction? PackingInstruction { get; set; } = job.PackingInstruction.Standard;
		public Int32? BundlesOf { get; set; }

        // Fuji Xerox
        public Int32? QtyPerBox { get; set; }

        public string DatePacked { get; set; }

        // Salmat
        public string BookingIdNumber { get; set; }

        public string CampaignName { get; set; }

        public string ToInstructionsText()
        {
            var sb = new StringBuilder();
            if (MailHouse != null)
            {
                sb.AppendLine("Mailhouse:");
                sb.AppendLine(MailHouse.Name);
                sb.AppendLine(MailHouse.Instructions);


                // Salmat / Local Delivery Network
                if (MailHouse.Id.Is(1,2,3))
                {
                    if (!string.IsNullOrEmpty(BookingIdNumber))
                        sb.AppendLine($"Booking Id Number:  {BookingIdNumber}");
                    if (!string.IsNullOrEmpty(CampaignName))
                        sb.AppendLine($"Campaign Name:  {CampaignName}");
                }
				
				// Fuji Xerox
				if (MailHouse.Id == 5)
				{
					if (QtyPerBox != null && QtyPerBox > 0)
						sb.AppendLine($"Qty per box: {QtyPerBox.Value}");
					if (DatePacked != null)
						sb.AppendLine($"Date packed:  {DatePacked}");
				}

			}
            else
            {
                sb.AppendLine("Packing instructions:");
                sb.AppendLine(PackingInstruction.ToDescription());
                if (PackingInstruction != null && PackingInstruction != job.PackingInstruction.Standard  && BundlesOf != null)
                {
                    sb.AppendLine($"Bundles of {BundlesOf} approx");
                }
            }

            return sb.ToString();
        }



		public string ToInstructionsText2()
		{
			var sb = new StringBuilder();
			if (MailHouse != null)
			{
				sb.AppendLine("Mailhouse:");
				sb.AppendLine(MailHouse.Name);

				// Salmat / Local Delivery Network
				if (MailHouse.Id.Is(1, 2, 3))
				{
					if (!string.IsNullOrEmpty(BookingIdNumber))
						sb.AppendLine($"Booking Id Number:  {BookingIdNumber}");
					if (!string.IsNullOrEmpty(CampaignName))
						sb.AppendLine($"Campaign Name:  {CampaignName}");
				}

				// Fuji Xerox
				if (MailHouse.Id == 5)
				{
					if (QtyPerBox != null && QtyPerBox > 0)
						sb.AppendLine($"Qty per box: {QtyPerBox.Value}");
					if (DatePacked != null)
						sb.AppendLine($"Date packed:  {DatePacked}");
				}
			}
			else
			{
				sb.AppendLine("Packing instructions:");
				sb.AppendLine(PackingInstruction.ToDescription());
				if (PackingInstruction != null && PackingInstruction != job.PackingInstruction.Standard && BundlesOf != null)
				{
					sb.AppendLine($"Bundles of {BundlesOf} approx");
				}
			}

			return sb.ToString();
		}


		public string Summerize(string p = "")
		{
			var sb = new StringBuilder();
			if (MailHouse != null)
			{
				sb.AppendLine($"{p}MAILHOUSE:  {MailHouse.Name}");
				sb.AppendLine($"{p}{MailHouse.Instructions}");
				//Extra fields for  Salmat / Local Delivery Network / ovoto / pmp
				if (MailHouse.Id == 1 ||
					MailHouse.Id == 2 ||
					MailHouse.Id == 3 ||
					MailHouse.Id == 5)
				{
					sb.AppendLine($"{p}Booking ID #  :  {BookingIdNumber}");
					sb.AppendLine($"{p}Campaign Name :  {CampaignName}"); ;
				}
				else if (MailHouse.Id == 5)
				{
					sb.AppendLine($"{p}Quantity/box :  {QtyPerBox}");
					sb.AppendLine($"{p}Date Packed  :  {DatePacked}");
				}
			}
			else
			{
				sb.AppendLine($"{p}MAILHOUSE:  N/A");
				sb.AppendLine($"{p}Packing instructions:  {PackingInstruction}");
			}

			return sb.ToString();
		}
		public override bool Equals(object obj)
        {
            if (obj == null) return false;

            BrochureDistPackInfo o = obj as BrochureDistPackInfo;
            if (o == null) return false;

            var thisMailHouse = MailHouse?.Id ?? 0;
            var otherMailHouse = o.MailHouse?.Id ?? 0;

            if (thisMailHouse == otherMailHouse &&
                this.PackingInstruction.Equals(o.PackingInstruction) &&
                this.BundlesOf.Equals(o.BundlesOf))
            {
                return true;
            }

            return false;
        }
    }



	public class MailHouseInfo
	{
		public string JobId { get; set; }
		public string CampaignName { get; set; }
		public string VersionOrOverprintName { get; set; }

		// staff fields
		public string QuantityPerBundle { get; set; }
		public string NumberOfBundles { get; set; }
		public string TotalQuantityPerCarton { get; set; }
		public string TotalPalletQuantity { get; set; }
		public string TotalPalletWeight { get; set; }
	}

}
