﻿using lep.job;
using System;
using System.Diagnostics;

namespace lep.jobmonitor.impl
{
	[DebuggerDisplay("{ScheduleType} {Status} {Amber} {Red} {Facility}")]
    public class Schedule : ISchedule
    {
        private JobStatusOptions status;

        public Schedule()
        {
        }

        public virtual int Id { get; set; }
        public virtual IJobTemplate Template { get; set; }
        public virtual bool Folding { get; set; }
        public virtual int StatusInt { get; set; }

        public virtual JobStatusOptions Status
        {
            get { return status; }
            set {
                status = value;
                StatusInt = (int)value;
            }
        }

        public virtual ProductionTiming ScheduleType { get; set; }
        public virtual decimal Amber { get; set; }
        public virtual decimal Red { get; set; }
        public virtual Facility Facility { get; set; }

        public virtual DateTime DateCreated { get; set; }
        public virtual DateTime DateModified { get; set; }
    }
}