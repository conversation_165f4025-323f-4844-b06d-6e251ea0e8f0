using System;
using System.Collections.Generic;
using System.Collections;
using System.Text;
using System.Text.RegularExpressions;
using System.IO;
using Spring.Objects.Factory;
using NHibernate.Criterion;
using NHibernate;

using lep.courier.csv;
using lep.configuration;
using lep.order;
using lep.freight;
using lep.freight.impl;
using lep.job;


/*

namespace lep.courier.impl
{

	/// <summary>
    /// 
    /// </summary>
	public class FastwayApplication: BaseCourierApplication, ICourierApplication
    {
		private static readonly Common.Logging.ILog log = Common.Logging.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

		public FastwayApplication() : base( CourierType.FastWay )
        {
		}

        public bool CalculatePrice(Facility facility, IList<IPackage> packages, string postcode, decimal totalWeight, bool hasSkid, StringBuilder log, out decimal price)
		{
			price = 0;
			log.AppendLine( "\n\nFastway calculation" );

			FastwayLabelType? type = FindLabel( facility, postcode );
			if (! type.HasValue) {
				log.AppendLine( "destination not exist" );
				return false;
			}

			decimal baseRate = 0;
			decimal excessRate = 0;
			int max = 0;
			bool allowExcess = false;

			switch (type.Value) {
			case FastwayLabelType.R1:
				baseRate = Convert.ToDecimal(ConfigurationApplication.GetValue(Configuration.FastwayRedBase));
				excessRate = Convert.ToDecimal(ConfigurationApplication.GetValue(Configuration.FastwayRedExcess));
				max = 5;
				allowExcess = true;
				break;
			case FastwayLabelType.O1:
				baseRate = Convert.ToDecimal(ConfigurationApplication.GetValue(Configuration.FastwayOrangeBase));
				excessRate = Convert.ToDecimal(ConfigurationApplication.GetValue(Configuration.FastwayOrangeExcess));
				max = 5;
				allowExcess = true;
				break;
			case FastwayLabelType.G1:
				baseRate = Convert.ToDecimal(ConfigurationApplication.GetValue(Configuration.FastwayGreenBase));
				excessRate = Convert.ToDecimal(ConfigurationApplication.GetValue(Configuration.FastwayGreenExcess));
				max = 5;
				allowExcess = true;
				break;
			case FastwayLabelType.L1:
				baseRate = Convert.ToDecimal(ConfigurationApplication.GetValue(Configuration.FastwayLimeBase));
				max = 25;
				break;
			case FastwayLabelType.B1:
				baseRate = Convert.ToDecimal(ConfigurationApplication.GetValue(Configuration.FastwayBrownBase));
				max = 25;
				break;
			case FastwayLabelType.P1:
				baseRate = Convert.ToDecimal( ConfigurationApplication.GetValue( Configuration.FastwayPinkBase ) );
				excessRate = Convert.ToDecimal( ConfigurationApplication.GetValue( Configuration.FastwayPinkExcess ) );
				max = 5;
				allowExcess = true;
				break;
			}

			log.AppendFormat( "label:{0}  base price:{1}  base weight:{2}  allowExcess:{3}  excessPrice:{4}\n",type.Value, AuditDecimal(baseRate), max, allowExcess, AuditDecimal(excessRate) );

			decimal tmp = 0;
			//if (hasSkid) {
			//	log.AppendLine( "    pack everything in skid" );
			//	if (CalculateWeight( baseRate,excessRate,max,allowExcess,totalWeight,log,out tmp )) {
			//		price += tmp;
			//	} else {
			//		return false;
			//	}
			//} else 
			
			
			//{
				int maxBCCarton = Convert.ToInt32(ConfigurationApplication.GetValue( Configuration.FastwayMaxBC )) / FreightApplication.GetSpecialPack( SpecialPackType.BC300 ).Quantity;

				int bcInBag = 0;
				decimal bagWeight = 0;

                foreach (Package pack in packages)
                {
					if (pack.Carton.CartonType == CartonType.BC) {
						for (int i = 0; i < pack.Quantity; i++) {
							bcInBag++;
							bagWeight += pack.ContentWeightPerPackage;
							if (bcInBag == maxBCCarton) {
								log.AppendFormat( "    pack one BC bag\n" );
								if (CalculateWeight( baseRate,excessRate,max,allowExcess,bagWeight,log,out tmp )) {
									price += tmp;
									bcInBag = 0;
									bagWeight = 0;
								} else {
									return false;
								}
							}
						}
					} else {
						log.AppendFormat( "    pack quantity:{0}\n",pack.Quantity );
						if (CalculateWeight( baseRate,excessRate,max,allowExcess,pack.ContentWeightPerPackage + pack.Carton.Weight,log,out tmp )) {
							price += tmp * pack.Quantity;
						} else {
							return false;
						}
					}
				}

				if (bcInBag > 0) {
					log.AppendFormat( "    pack one BC bag\n" );
					if (CalculateWeight( baseRate,excessRate,max,allowExcess,bagWeight,log,out tmp )) {
						price += tmp;
						bcInBag = 0;
						bagWeight = 0;
					} else {
						return false;
					}
				}
			//}

			decimal surcharge = GetCourierSurCharge();
			log.AppendFormat( "total price: {0} * {1}(surcharge) = ${2}\n",price,surcharge,AuditDecimal(price * surcharge) );
			price = price * surcharge;

			return true;
		}

		private bool CalculateWeight(decimal baseRate, decimal excessRate, int max, bool allowExcess, decimal weight, StringBuilder log, out decimal price) {
			log.AppendFormat( "    weight per pack:{0} KG\n",AuditDecimal(weight) );
			price = 0;
			if (weight > max && !allowExcess) {
				log.AppendLine( String.Format( "    Package excess allowed weight {0}kg",max ) );
				return false;
			}
			
			price = baseRate;

			if (weight > max) {
				weight = weight - max;
				log.AppendFormat( "    excess price: (Excess Per 5kg) x {0} = ${1}\n",excessRate,AuditDecimal(Math.Ceiling( weight / 5 ) * excessRate) );
				price += Math.Ceiling( weight / 5 ) * excessRate;
			}

			log.AppendFormat( "    price per pack: ${0}\n\n",AuditDecimal(price) );
			return true;
		}

		private FastwayLabelType? FindLabel( Facility facility, string postcode )
		{
			FastwayPostcode data = Session.CreateCriteria( typeof( FastwayPostcode ) )
                .Add(Expression.Eq( "Origin", facility == Facility.PM ? MELBOURNE_ORIGIN_CODE : FOREST_GLEN_ORIGIN_CODE))
				.Add( Expression.Eq( "Postcode", postcode ) )
				//.SetMaxResults( 1 )
				.UniqueResult<FastwayPostcode>();

			if (data != null) {
				return data.LabelType;
			}

			return null;
		}
	}
}


*/