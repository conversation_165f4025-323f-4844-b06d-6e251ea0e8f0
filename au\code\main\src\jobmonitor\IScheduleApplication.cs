using lep.job;
using System.Collections.Generic;

namespace lep.jobmonitor
{
	public interface IScheduleApplication
    {
        IList<ISchedule> GetSchedulesByTemplate(Facility facility, IJobTemplate jobTemplate, ProductionTiming pt, bool folding);

        IList<ISchedule> GetSchedulesByTemplate(Facility facility, JobTypeOptions jobType, ProductionTiming pt, bool folding);

        void AddSchedule(Facility facility, IJobTemplate jobTemplate, ProductionTiming pt, JobStatusOptions status, int amber, int red, bool folding);

        ISchedule Get(int id);

        void DeleteById(int id);

        void Save(ISchedule s);

        //JobStatusOptions GetNextRoutingPoint(IJob job);
        //JobStatusOptions GetPreviousRoutingPoint(IJob job);

        ISchedule GetNextStandardSchedule(IJob job);

        IList<ISchedule> GetAllStandardSchedules();

        List<ISchedule> GetNextStandardSchedules(IJob job);
    }
}