using lep.user;
using System;

namespace lep.job
{
	public interface IPrepress
    {
        IStaff PrepressBy { get; set; }
        DateTime? PrepressDate { get; set; }
        IPrepressWork OneSided { get; set; }
        IPrepressWork WorkAndTurn { get; set; }
        IPrepressWork WorkAndTumble { get; set; }
        IPrepressWork SheetWork { get; set; }
        IPrepressWork Cover { get; set; }
    }
}