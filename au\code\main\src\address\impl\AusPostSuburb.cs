﻿using Newtonsoft.Json.Linq;
using Serilog;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;

namespace lep.address.impl
{
    public class AusPostSuburb
    {
        // private const string STR_AusPostUrl = "https://auspost.com.au/api/postcode/search.json?q={0}";
        // private const string STR_AusPostApiKey = "6e4c6de5-d714-4c54-b9e1-d2c8d7a92c7f";
        public string PostCode { get; set; }
        public string State { get; set; }
        public string Suburb { get; set; }

        // public static List<AusPostSuburb> Search(string postcode)
        // {
        //     try
        //     {
        //         var url = string.Format(STR_AusPostUrl, postcode);
        //         var req = WebRequest.Create(url);
        //         req.Headers.Add("AUTH-KEY", STR_AusPostApiKey);
        //         var resp = req.GetResponse();
        //         var sr = new StreamReader(resp.GetResponseStream());
        //         var jsonStr = sr.ReadToEnd().Trim();

        //         var d2 = JObject.Parse(jsonStr);

        //         var locality = d2["localities"]["locality"];

        //         if (locality is JArray)
        //         {
        //             return (from l in locality
        //                     select new AusPostSuburb
        //                     {
        //                         State = l["state"].ToString(),
        //                         PostCode = l["postcode"].ToString(),
        //                         Suburb = l["location"].ToString()
        //                     }).ToList();
        //         }
        //         else
        //         {
        //             return new List<AusPostSuburb>
        //             {
        //                 new AusPostSuburb
        //                 {
        //                     State = locality["state"].ToString(),
        //                     PostCode = locality["postcode"].ToString(),
        //                     Suburb = locality["location"].ToString()
        //                 }
        //             };
        //         }
        //     }
        //     catch (Exception ex)
        //     {
		// 		Log.Error(ex, ex.Message);
        //         // eat up
        //     }
        //     return new List<AusPostSuburb>();
        // }
    }
}
