using lumen.csv;

namespace lep.job.csv
{
    public class FoldOptionsVerifyForeignKey : BaseHandler
    {
        private IJobApplication jobApp;
        private int rowNumber = 0;
        private bool seenFirstRow = false;

        public IJobApplication JobApplication
        {
            set { jobApp = value; }
        }

        public override void StartDocument()
        {
        }

        public override void EndDocument()
        {
        }

        public override void RowData(string[] values)
        {
            if (!seenFirstRow)
            {
                seenFirstRow = true;
            }
            else
            {
                rowNumber++;
                for (var i = 1; i < values.Length; i++)
                {
                    if ("" != values[i] && "-" != values[i] && !jobApp.CheckPageSizeExist(values[i]))
                    {
                        throw new CsvParseException(
                            string.Format("Spec Fold File Input row {0} :  {1} : '{2}' is invalid page size", rowNumber,
                                i, values[i]));
                    }
                }
            }
            base.RowData(values);
        }
    }
}