using lep.job;
using lep.run;
using lep.user;
using NHibernate;
using System;
using System.Collections.Generic;

namespace lep.order
{
	/// <summary>
	///
	/// </summary>
	public interface IOrderApplication
	{
		IOrder NewOrder();

		IOrder NewOrder(ICustomerUser customer);

		IOrder GetOrder(int Id);

		IOrder Refresh(IOrder order);

		void Save(IOrder order);

		void BaseSave(IOrder order);

		void Delete(IOrder order);

		void Forget(IOrder order);

		void Submit(IOrder order, IUser submittingUser);

		IOrder ReorderOrder(IOrder sourceOrder);

		///create new order from existing, no artwork copy.Reprice
		IOrder CopyOrder2(IOrder sourceOrder);

		IOrder CopyOrder2(IOrder sourceOrder, IJob j);

		ICriteria OrderCriteria(ICustomerUser customer, List<OrderStatusOptions> statuslist, bool attention, bool cancel);

		ICriteria OrderCriteriaCust(ICustomerUser customer, string orderOrJobNum, List<OrderStatusOptions> statuslist,
			List<IJobTemplate> types, bool? cancel = null, RunCelloglazeOptions? cello = null, IPaperSize size = null, IStock stock = null
			, bool? rejected = null, bool? requireApproval = null, bool? quoteRequired = null, bool isWhiteLabel = false
			, int? wlCustomerId = null, bool? isWlOrderPaidFor = null
			);

		ICriteria OrderCriteria(string customer, string ordernumber, string jobnumber, string status, bool newOrder,
			bool onHold, bool urgent, bool corrected, bool prePay, bool open, bool awaitingPay, bool cancelled,
			bool requireApproval, bool quoteRequired, bool rejected, List<IJobTemplate> types, List<IJobTemplate> nonTypes,
			RunCelloglazeOptions? cello, IPaperSize size, IStock stock, bool isOrderWithDigitalJobs, bool IsOrderWithOutworkJob,
			Facility? facility = null, bool isWhiteLabel = false, bool isPaidButNotSubmitted = false, bool IsUnableToMeetPrice = false,
			bool hideOnHoldOrders = false, bool showOnlyOnHoldOrders = false);

		ICriteria OrderCriteria();

		ICriteria ReprintCriteria();

		IList<IOrder> GetRequireAttentionOrders(ICustomerUser customer);

		IList<IOrder> FindArchiveOrder(int day);

		IList<IOrder> FindEmptyOrder();

		//void MoveOrderFile(IOrder order);

		string Render(IOrder order);

		string Render(OrderStatusOptions orderStatus);

		string GenerateOrderFolderScript(IOrder order);

		IOrder ReprintOrder(IUser user, IOrder srcOrder , bool copyPreflight,
			bool hasDispatch, string reason, string result, string ncrNo, string predefinedReason   );

		IOrder ReprintJob(IUser user, IJob job, decimal invoicePrice, decimal reprintCost, bool copyPreflight, bool hasDispatch, string reason, string result, string ncrNo, string predefinedReason, int quantity);

		IOrder RestartJob(IJob job, decimal invoicePrice, decimal reprintCost, bool copyPreflight, bool hasDispatch, string reason, string result, string ncrNo, string predefinedReason, int quantity);

		int GetOrdersCountByCustomer(ICustomerUser customer);

		IOrder ReorderJob(IJob originalJob);

		DateTime? GetDispatchDate(IOrder order, DateTime ifSubmittedAt, out bool estimated);

		DateTime? GetDispatchDate2(IOrder order, DateTime ifSubmittedAt, out bool estimated);


		DateTime? UpdateDispatchDate(IOrder order, DateTime ifSubmittedAt);

		//void AutoAssignJobFacility(IJob job);

		List<IJobTemplate> JobTemplatesGetAllPossibleValues(JobTypeOptions? jobTypeOptions);

		//Facility? GetProductionFacilityByPostCode(string postcode);

		void CronTask_ArchiveOrders();

		void SubmitReady(IOrder order, IStaff staff);

		void ReturnOrder(IOrder order);

		// code from OrderEdit.cs
		// automatically applys a promotion to an order
		// since the code was in PreRender maybe this should be called on GetOrder web api call
		string ScatteredLogic_AutoPopulatePromotion(IOrder order, IUser currentUser);

		// code from OrderEdit.cs
		// switch between pick up and freight
		// this is just the first cut, some code maybe should be pushed to order.cs
		void ScatteredLogic_PreSaveOrder_ModifyPackDetailAndApplyFCode(IOrder order, IUser currentUser);

		void ScatteredLogic_PreSaveOrder_SendEmail(IOrder order, IUser currentUser);

		bool AttachConnote(IOrderConNote connote);
		Facility? GetProductionFacilityByPostCode(string postcode);

		ISession BaseSession { get; }

		IOrder ReprintOrder(IUser currentUser, IOrder order, OrderReprintRestartDto reprintRestartArgs);
	}
}
