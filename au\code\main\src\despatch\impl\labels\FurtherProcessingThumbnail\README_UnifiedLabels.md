# Unified Further Processing Labels

This document describes the new unified approach to Further Processing labels that consolidates all the different label types into a single, maintainable solution.

## Overview

The new system consists of three main components:

1. **FurtherProcessingLabelDataExtractor** - Utility class that extracts all job information
2. **FurtherProcessingLabelData** - DTO containing all extracted job information
3. **UnifiedFurtherProcessingLabel** - Single label class that can render any label type

## Benefits

- **Single Source of Truth**: All job information extraction logic is centralized
- **Maintainable**: Changes to job information extraction only need to be made in one place
- **Flexible**: Easy to add new label types or modify existing ones
- **Consistent**: All labels use the same data extraction logic
- **Testable**: Data extraction is separated from rendering logic

## Usage Examples

### Basic Usage

```csharp
// Create a standard Further Processing label
var label = FurtherProcessingLabelFactory.CreateFurtherProcessingLabel(
    job, printerAndTray, configApp, filename);
label.Print();

// Create a DPC Processing label
var dpcLabel = FurtherProcessingLabelFactory.CreateDPCProcessingLabel(
    job, printerAndTray, configApp, filename);
dpcLabel.Print();

// Auto-determine label type based on job
var autoLabel = FurtherProcessingLabelFactory.CreateAutoLabel(
    job, printerAndTray, configApp, filename);
autoLabel.Print();
```

### Advanced Usage

```csharp
// Create unified label with specific type
var unifiedLabel = new UnifiedFurtherProcessingLabel(LabelType.WideFormatProcessing);
unifiedLabel.Job = job;
unifiedLabel.PrinterAndTray = printerAndTray;
unifiedLabel.ConfigurationApplication = configApp;
unifiedLabel.PrintFileName = filename;
unifiedLabel.SetupPrintProperties();
unifiedLabel.Print();

// Access extracted data
var labelData = unifiedLabel.LabelData;
Console.WriteLine($"Job Size: {labelData.Size}");
Console.WriteLine($"Stock: {labelData.Stock}");
Console.WriteLine($"Special Instructions: {labelData.SpecialInstructionsText}");

// Get specific formatted sections
string basicInfo = unifiedLabel.GetFormattedSection("BasicInformation");
string processingText = unifiedLabel.GetFormattedSection("ProcessingText1");
```

### Data Extraction Only

```csharp
// Extract job data without creating a label
var jobData = FurtherProcessingLabelDataExtractor.ExtractJobData(job, LabelType.DPCProcessing);

// Access individual properties
Console.WriteLine($"Job Name: {jobData.JobName}");
Console.WriteLine($"Order Number: {jobData.OrderNumber}");
Console.WriteLine($"Quantity: {jobData.Quantity}");
Console.WriteLine($"Stock: {jobData.Stock}");

// Get formatted text sections
Console.WriteLine("Basic Information:");
Console.WriteLine(jobData.BasicInformationText);
Console.WriteLine("\nProcessing Instructions:");
Console.WriteLine(jobData.ProcessingText1);
Console.WriteLine("\nSpecial Instructions:");
Console.WriteLine(jobData.SpecialInstructionsText);
```

## Label Types

The system supports the following label types:

- **FurtherProcessing** - Standard further processing labels
- **DPCProcessing** - Digital processing labels with additional DPC-specific information
- **WideFormatProcessing** - Wide format processing labels
- **FurtherProcessingList** - List-style further processing labels

## Data Available

The `FurtherProcessingLabelData` DTO contains all the information extracted from jobs:

### Basic Information
- Job Name, Order Number, Job Number
- Original Job Number (for re-orders)
- Courier information
- Multi-job order indicator
- Barcode

### Processing Information
- Size, Quantity, Stock information
- Run number
- Printing options (Front/Back)
- Celloglaze options
- Finishing options (hole drilling, round corners, die cutting, folding, magnets)
- Magazine-specific information (pages, binding, cover stock)

### Special Instructions
- Send samples flag
- Customer logo required flag
- Special instructions text
- Digital job mail house instructions
- Pack without pallets flag
- Wiro magazine information
- Freight packages information

## Migration from Existing Labels

To migrate from existing label classes:

1. Replace the specific label class with `UnifiedFurtherProcessingLabel`
2. Set the appropriate `LabelType` 
3. Use the factory methods for convenience
4. Test to ensure output matches existing labels

### Example Migration

**Before:**
```csharp
var label = new DPCPocessingThumbnailLabel();
label.Job = job;
label.ConfigurationApplication = configApp;
label.PrinterAndTray = printerAndTray;
label.PrintFileName = filename;
label.SetupPrintProperties();
label.Print();
```

**After:**
```csharp
var label = FurtherProcessingLabelFactory.CreateDPCProcessingLabel(
    job, printerAndTray, configApp, filename);
label.SetupPrintProperties();
label.Print();
```

## Testing

The new system makes testing much easier:

```csharp
[Test]
public void TestJobDataExtraction()
{
    // Arrange
    var job = CreateTestJob();
    
    // Act
    var data = FurtherProcessingLabelDataExtractor.ExtractJobData(job, LabelType.DPCProcessing);
    
    // Assert
    Assert.AreEqual("Test Job", data.JobName);
    Assert.AreEqual("12345", data.OrderNumber);
    Assert.AreEqual(1000, data.Quantity);
    // ... more assertions
}

[Test]
public void TestFormattedOutput()
{
    // Arrange
    var job = CreateTestJob();
    
    // Act
    var basicInfo = FurtherProcessingLabelDataExtractor.GetFormattedSection(
        job, LabelType.FurtherProcessing, "BasicInformation");
    
    // Assert
    Assert.IsTrue(basicInfo.Contains("Job Name: Test Job"));
    Assert.IsTrue(basicInfo.Contains("Order #: 12345"));
}
```

## Future Enhancements

The unified system makes it easy to add new features:

1. **New Label Types**: Add new enum values and formatting logic
2. **Additional Data**: Extend the DTO with new properties
3. **Custom Formatting**: Override formatting methods for specific requirements
4. **Export Formats**: Use the extracted data for other output formats (PDF, HTML, etc.)
