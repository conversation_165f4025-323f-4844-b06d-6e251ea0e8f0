using lep.freight;
using lep.freight.impl;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Reflection;
using BCThicknessType = System.Collections.Generic.Dictionary<string, System.Collections.Generic.Dictionary<string, decimal>>;
namespace lep.freight.impl
{
	[DebuggerDisplay("{Carton.Code} \t{NumberOfCartons} \t{QtyInEachCartons} \t{Slack} \t{TVol}")]
	public class Cost
	{
		public ICarton Carton;
		public decimal NumberOfCartons;
		public decimal QtyInEachCartons;
		public decimal QtyInEachCartons2;
		public decimal Slack;
		public decimal TVol;
		public int InBundlesOf;

	}

	public class CartonFinder
	{
		private readonly IEnumerable<Carton> _data;
		private BCThicknessType _500BCThickness;
		public CartonFinder(string jobOptionCSVsFolder)
		{
		    var pathOfCartonsCsv = Path.Combine(jobOptionCSVsFolder, "cartons.csv");
			var datajson = CsvToJson.Convert(pathOfCartonsCsv);
			_data = JsonConvert.DeserializeObject<List<Carton>>(datajson).AsEnumerable()
					.OrderBy(_ => _.Level)
					.ThenBy(_ => (_.IWidth * _.IHeight))
					.ThenBy(_ => (_.IWidth * _.IHeight * _.IDepth));


			var pathOf500BCThicknessJson =  Path.Combine(jobOptionCSVsFolder, "500BCThickness.json");
			_500BCThickness = JsonConvert.DeserializeObject<BCThicknessType>(File.ReadAllText(pathOf500BCThicknessJson));

			#region dynamic deletegte on column

			//var options = ScriptOptions.Default.AddReferences(typeof(IJob).Assembly).WithImports("lep");
			//options.AddReferences(typeof(PrintType).Assembly);

			//foreach (var c in _data) {
			//	if (!string.IsNullOrEmpty(c.Rule)) {
			//		Func<IJob, bool> result = null;
			//		string rule = "";
			//		try {
			//			rule = c.Rule.Trim(new char[] { '"' }).Replace("\"\"", "\"");
			//			CSharpScript.EvaluateAsync<Func<IJob, bool>>(rule, options)
			//				.ContinueWith(s => result = s.Result).Wait();
			//			c.PredicateOnJob = result;
			//		}
			//		catch (Exception ex) {
			//			var m = ex.Message;
			//		}

			//	}
			//}

			#endregion dynamic deletegte on column
		}

		public bool Has500BCThicknessDataFor(string stockName)
		{
			return _500BCThickness.ContainsKey(stockName);
		}

		public decimal Get500BCThickness(string stockName, string cello)
		{
			try
			{
				return _500BCThickness[stockName][cello];
			}
			catch (Exception ex)
			{
				try
				{
					return _500BCThickness[stockName]["None/None"];
				}
				catch (Exception ex2)
				{
					return 0;
				}
			}
		}

		public IList<ICarton> ListCarton(int? packagingLevel)
		{
			if (packagingLevel.HasValue)
				return _data.Where(c => c.Level == packagingLevel.Value)
					.Select(c => (ICarton)c).ToList();

			return _data.Select(c => (ICarton)c).ToList();
		}

		public Carton GetCarton(string code)
		{
			return _data.FirstOrDefault(c => c.Code == code);
		}

		// find smallest possible cartons given paper width & height
		public IEnumerable<Carton> Get(float w, float h, int level = 4)
		{
			var r = _data.Where(c => (c.Level == level) && c.CanFit(w, h));

			return r;
		}

		// find smallest possible cartons given paper width & height
		public IEnumerable<Carton> Get3(float w, float h, float d, int level)
		{
			var r = _data.Where(c => (c.Level <= level) &&
									  c.SizeLeft(new Carton() { Width = (int)w, Height = (int)h, Depth = (int)d }) > -1);

			return r;
		}
	}

	/*
	public static class DimFixer
	{
		public static Carton FixDim(Carton c)
		{
			var inners = (new List<int>() { c.IWidth, c.IHeight, c.IDepth }).AsEnumerable().OrderByDescending(x => x)
				.ToArray();
			c.IWidth = inners[0];
			c.IHeight = inners[1];
			c.IDepth = inners[2];

			var outers = (new List<int>() { c.Width, c.Height, c.Depth }).AsEnumerable().OrderByDescending(x => x)
				.ToArray();
			c.Width = outers[0];
			c.Height = outers[1];
			c.Depth = outers[2];
			return c;
		}
	}
	*/
}
