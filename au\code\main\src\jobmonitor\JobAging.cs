﻿using lep.job;
using System;
using System.Data.SqlTypes;

namespace lep.jobmonitor
{
	/// <summary>
	/// Job Age Calculator
	///		Calculates age of
	///			Standard	 Jobs - by calculating elaped hours 6am to 10pm
	///			Prioritised  Jobs - (held off by LEP)
	///			Guranteed	 Jobs - (held off by LEP)
	/// </summary>
	internal static class JobAging
    {
        private static int OFF_HOURS = 50;

        public static bool HasValue(this DateTime d)
        {
            return (d != DateTime.MinValue) && (d != DateTime.MaxValue) &&
                   (bool)(d != SqlDateTime.MinValue) && (bool)(d != SqlDateTime.MaxValue);
        }

        public static TimeSpan GetStandardAge(IJob j)
        {
            if ((int)j.Status >= (int)JobStatusOptions.Submitted)
            {
                if (j.ReceivedDate.HasValue)
                {
                    var submittedAt = j.ReceivedDate.Value;
                    var end = DateTime.Now;
                    return CalcBusinessTime(submittedAt, end);
                }
            }
            return TimeSpan.Zero;
        }

        public static TimeSpan CalcBusinessTime(DateTime a, DateTime b)
        {
            var start = a > b ? b : a;
            var end = a > b ? a : b;

            if (IsBetweenOffHour(start))
            {
                start = setToNextSunday(start);
            }

            if (IsBetweenOffHour(end))
            {
                end = setToNextSunday(end);
            }

            var timeLeft = end - start;
            var offHours = OFF_HOURS * NumberOfSaturdays(start, end);

            return timeLeft - new TimeSpan(offHours, 0, 0);
        }

        //Sat 10pm
        private static DateTime setToNextSunday(DateTime start)
        {
            var delayDays = start.DayOfWeek != DayOfWeek.Sunday ? 7 - (int)start.DayOfWeek : 0;
            return new DateTime(start.Year, start.Month, start.Day, 22, 0, 0).AddDays(delayDays);
        }

        //Fri 8pm
        private static DateTime setToPreviousFriday(DateTime end)
        {
            var delayDays = end.DayOfWeek != DayOfWeek.Friday ? ((int)end.DayOfWeek + 7 - 5) % 7 : 0;
            return new DateTime(end.Year, end.Month, end.Day, 20, 0, 0).AddDays(-delayDays);
        }

        private static int NumberOfSaturdays(DateTime start, DateTime end)
        {
            var saturdayCount = 0;
            while (end > start)
            {
                if (start.DayOfWeek == DayOfWeek.Saturday)
                {
                    saturdayCount++;
                }
                start = start.AddDays(1);
            }

            return saturdayCount;
        }

        private static bool IsBetweenOffHour(DateTime a)
        {
            return (a.DayOfWeek == DayOfWeek.Friday && a.Hour > 20)
                   || a.DayOfWeek == DayOfWeek.Saturday
                   || (a.DayOfWeek == DayOfWeek.Sunday && a.Hour < 22);
        }

        /*
		private static TimeSpan CalcBusinessTime6amTo10pm(DateTime a,DateTime b)
		{
			if (a > b) {
				DateTime tmp = a;
				a = b;
				b = tmp;
			}

			if (a.TimeOfDay < new TimeSpan(6,0,0))
				a = new DateTime(a.Year,a.Month,a.Day,6,0,0);

			if (b.TimeOfDay > new TimeSpan(22,0,0))
				b = new DateTime(b.Year,b.Month,b.Day,22,0,0);

			TimeSpan sum = TimeSpan.Zero;

			while (a < b) {
				if (a.DayOfWeek != DayOfWeek.Saturday && a.DayOfWeek != DayOfWeek.Sunday) {
					if (a.Date == b.Date) {
						sum += (b - a);
					} else {
						DateTime endOfThatDay = new DateTime(a.Year,a.Month,a.Day,22,0,0);
						sum += (endOfThatDay - a);
					}
				}
				a = (new DateTime(a.Year,a.Month,a.Day,6,0,0)).AddDays(1);
			}

			return sum;
		}
        */
    }
}