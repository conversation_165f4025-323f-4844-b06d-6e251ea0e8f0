using Serilog;
using System;
using System.Reflection;

namespace lep.content.impl
{
	public class Content : IContent
	{
		//// private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		public Content()
		{
		}

		public int Id { get; set; }

		public String Title { get; set; }

		public bool IsPlainText { get; set; }

		public String Body { get; set; }

		public DateTime DateCreated { get; set; }

		public DateTime DateModified { get; set; }
	}
}
