using System.Collections.Generic;

namespace lep.despatch.impl.label
{
    /// <summary>
    /// Order Data transfer object for Pigeon hole lables
    /// </summary>
    public sealed class PigeonHoleOrderDTO
    {
        public PigeonHoleOrderDTO()
        {
            Jobs = new List<PigeonHoleJobDTO>();
        }

        public string BottomRight { get; set; }
        public string Courier { get; set; }
        public string Customer { get; set; }
        public List<PigeonHoleJobDTO> Jobs { get; set; }
        public string OrderNo { get; set; }
        public string SubmitDate { get; set; }
        public string TopRight { get; set; }
        public string PackDetails { get; set; }
        public string PromoCode { get; set; }
    }
}