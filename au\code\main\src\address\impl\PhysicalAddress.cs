using System;
using System.Collections.Generic;
using System.Text;
using System.Text.RegularExpressions;

namespace lep.address.impl
{
	[Serializable]
	public class PhysicalAddress : IPhysicalAddress
	{
		public PhysicalAddress()
		{
			Address1 = "";
			Address2 = "";
			Address3 = "";
			City = "";
			Postcode = "";
			Country = "AU";
			State = "";
		}

		public PhysicalAddress(string address1, string city, string state, string postcode)
		{
			this.Address1 = address1;
			this.Address2 = "";
			this.Address3 = "";
			this.City = city;
			this.State = state;
			this.Postcode = postcode;
			this.Country = @"Australia";
		}

		public override int GetHashCode()
		{
			return Address1.GetHashCode() * Postcode.GetHashCode();
		}

		public override bool Equals(object obj)
		{
			var addr = obj as IPhysicalAddress;
			if (addr == null)
			{
				return false;
			}

			return Address1 == addr.Address1
				   && Address2 == addr.Address2
				  // && Address3 == addr.Address3
				   && City == addr.City
				   && State == addr.State
				   && Postcode == addr.Postcode
				   && Country == addr.Country;
		}

		#region IPhysicalAddress Members

		public virtual string Address1 { get; set; }

		public virtual string Address2 { get; set; }
		public virtual string Address3 { get; set; }
		public virtual string City { get; set; }

		public virtual string State { get; set; }

		public virtual string Postcode { get; set; }

		public virtual string Country { get; set; }

		public string ToStringFormatted()
		{
			var sb = new StringBuilder();

			sb.AppendLine(Address1);
			sb.AppendLine(Address2);
			//sb.AppendLine(Address3);
			sb.AppendLine(City);
			sb.Append($"{State} {Postcode} {Country}");

			//sb.ToString().Dump();
			var s = sb.ToString();
			var s2 = Regex.Replace(s, @"[\r\n]+", "\n", RegexOptions.Multiline);
			return s2;
		}

		#endregion IPhysicalAddress Members
	}

	public class DeliveryDetails
	{
		public virtual int Id { get; set; }
		public virtual int CustomerId { get; set;  }
		public virtual int UserId { get; set; }
		public virtual PhysicalAddress Address { get; set; } = new PhysicalAddress();
		public virtual string RecipientName { get; set; }
		public virtual  string RecipientPhone { get; set; }

		public virtual string DeliveryInstructions { get; set; }

		public virtual DateTime DateModified { get; set; }
		

		public override bool Equals(object obj)
		{
			var other = obj as DeliveryDetails;
			if (other == null)
			{
				return false;
			}

			return RecipientName == other.RecipientName
				   && RecipientPhone == other.RecipientPhone
				   && Address.Equals(other.Address);
		}

		public override int GetHashCode()
		{
			return 3 * (3 * RecipientName?.GetHashCode() ?? 0 + RecipientPhone?.GetHashCode() ?? 0) + Address?.GetHashCode() ?? 0;
		}
	}

	[Serializable]
	public class ListOfDeliveryDetails : HashSet<DeliveryDetails>
	{
	}
}
