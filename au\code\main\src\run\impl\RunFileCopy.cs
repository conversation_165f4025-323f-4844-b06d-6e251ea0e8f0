using System;

namespace lep.run.impl
{
	public class RunFileCopy : IInitializingObject
    {
        private bool initialised;
        private IRunApplication runApplication;

        public RunFileCopy()
        {
        }

        public IRunApplication RunApplication
        {
            set { runApplication = value; }
        }

        public void AfterPropertiesSet()
        {
            if (runApplication == null)
            {
                throw new ArgumentNullException("runApplication");
            }
            initialised = true;
        }

        public void CronTask()
        {
            if (!initialised)
            {
                throw new ApplicationException("RunFileCopy not initialised");
            }
            foreach (var run in runApplication.FindFileCopyRun())
            {
                runApplication.CopyRunFile(run);
            }
        }
    }
}