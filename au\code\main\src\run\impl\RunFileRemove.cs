using System;

namespace lep.run.impl
{
	public class RunFileRemove : IInitializingObject
    {
        private bool initialised;
        private IRunApplication runApplication;

 
        public RunFileRemove(IRunApplication runApplication)
        {
            this.runApplication = runApplication;
        }
 
        public IRunApplication RunApplication
        {
            set { runApplication = value; }
        }

        public void AfterPropertiesSet()
        {
            if (runApplication == null)
            {
                throw new ArgumentNullException("runApplication");
            }
            initialised = true;
        }

        public void CronTask()
        {
            if (!initialised)
            {
                throw new ApplicationException("RunFileRemove not initialised");
            }
            foreach (var run in runApplication.FindOldRun())
            {
                runApplication.RemoveRunFile(run);
                run.FileRemoved = true;
                runApplication.Save(run);
            }
        }
    }
}
