using System;

namespace lep.job.impl
{
	public class PaperSize : IPaperSize
    {
        public PaperSize()
        {
        }

        public PaperSize(int id, string name, ISize size)
        {
            Id = id;
            Name = name;
            Size = size;
        }

        #region IPaperSize Members

        public virtual int Id { get; set; }
        public virtual string Name { get; set; }
        public virtual ISize Size { get; set; }

        //public virtual IPaperSize FreightPaperSize { get; set; }
        //public virtual int? FreightPaperSizeQty { get; set; }

        public DateTime DateCreated { get; set; }
        public DateTime DateModified { get; set; }

        #endregion IPaperSize Members
    }
}