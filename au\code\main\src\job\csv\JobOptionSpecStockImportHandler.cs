using lep.job.impl;

using Serilog;
using lumen.csv;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using static lep.job.JobCelloglazeOptions;
using static lep.job.JobPrintOptions;

namespace lep.job.csv
{
	public class JobOptionSpecStockImportHandler : BaseHandler
	{
		// private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);
		private Dictionary<string, int> fieldMap = new Dictionary<string, int>();

		private IJobApplication jobApp;

		private int rowNumber = 0;

		private bool seenFirstRow;

		public IDictionary<int, IJobOptionSpecStock> Result { get; } = new Dictionary<int, IJobOptionSpecStock>();

		public IJobApplication JobApplication
		{
			set { jobApp = value; }
		}

		public override void StartDocument()
		{
			seenFirstRow = false;
		}

		public override void EndDocument()
		{
		}

		public override void RowData(string[] values)
		{
			if (!seenFirstRow)
			{
				seenFirstRow = true;
				//Map column names to column numbers
				fieldMap = new Dictionary<string, int>(values.Length);
				for (var i = 0; i < values.Length; i++)
				{
					fieldMap[values[i].ToLower()] = i;
				}
			}
			else
			{
				rowNumber++;

				try
				{
					var pid = Convert.ToInt32(values[fieldMap["pid"]]);
					IJobOptionSpecStock jobOptionSpecStock;
					if (Result.ContainsKey(pid))
					{
						jobOptionSpecStock = Result[pid];
					}
					else
					{
						jobOptionSpecStock = new JobOptionSpecStock();
						//jobOptionSpecStock.Magnet = false;
					}

					jobOptionSpecStock.FrontPrintOptions = ParseColour(values[fieldMap["colour-front"]], jobOptionSpecStock.FrontPrintOptions);
					jobOptionSpecStock.BackPrintOptions = ParseColour(values[fieldMap["colour-back"]], jobOptionSpecStock.BackPrintOptions);
					jobOptionSpecStock.CelloOptions = ParseCello(values[fieldMap["cello-front"]], values[fieldMap["cello-back"]], jobOptionSpecStock.CelloOptions);
					Result.Add(pid, jobOptionSpecStock);
				}
				catch (Exception ex)
				{
					//Wrap the exception in another that gives the line number
					// Log.Error(string.Format("Invalid value in row {0} : {1}", rowNumber, ex.Message), ex);
				}
			}

			base.RowData(values);
		}

		private IList<CelloOption> ParseCello(string celloFront, string celloBack, IList<CelloOption> oldlist)
		{
			//if (celloFront == "E" || celloFront == "F" || celloFront == "E&F")
			//{
			//	int i = 0;
			//}

			if ("X" == celloFront || "X" == celloBack)
			{
				oldlist = new List<CelloOption>
				{
					new CelloOption(None, None),
					new CelloOption(Gloss, None),
					new CelloOption(Matt, None),
					new CelloOption(Velvet, None),

					new CelloOption(Gloss, Gloss),
					new CelloOption(Matt, Matt),
					new CelloOption(Velvet, Velvet),

			        //new CelloOption(EmbossedGlossFront, None),
			        //new CelloOption(EmbossedMattFront, None),
			        new CelloOption(Foil, Matt),
					new CelloOption(SpotUV,Matt)
				};
			}
			else if ("S" == celloFront && "S" == celloBack)
			{
				oldlist = new List<CelloOption>
				{
					new CelloOption(None, None),
					new CelloOption(Gloss, None),
					new CelloOption(Matt, None),
					new CelloOption(Gloss, Gloss),
					new CelloOption(Matt, Matt)
				};
			}
			else if ("W" == celloFront && "W" == celloBack)
			{
				oldlist = new List<CelloOption>
				{
					new CelloOption(Gloss, None),
					new CelloOption(Matt, None),
					new CelloOption(Gloss, Gloss),
					new CelloOption(Matt, Matt)
				};
			}
			else if ("E&F" == celloFront && "N" == celloBack)
			{
				oldlist.Add(new CelloOption(EmbossFoil, None));
			}
			else if ("S" == celloFront && "M" == celloBack)
			{
				oldlist.Add(new CelloOption(SpotUV, Matt));
			}
			else if ("SMF" == celloFront && "N" == celloBack)
			{
				oldlist.Add(new CelloOption(SpotUVFrontMattFront, None));
			}
			else if ("EGF" == celloFront && "N" == celloBack)
			{
				oldlist.Add(new CelloOption(EmbossedGlossFront, None));
			}
			else if ("EMF" == celloFront && "N" == celloBack)
			{
				oldlist.Add(new CelloOption(EmbossedMattFront, None));
			}
			else
			{
				var celloOption = new CelloOption();
				celloOption.CelloFront = ParseJobCelloglazeOptions(celloFront);
				celloOption.CelloBack = ParseJobCelloglazeOptions(celloBack);
				var exist = oldlist.Cast<ICelloOption>().Any(tmp => celloOption.CelloFront == tmp.CelloFront && celloOption.CelloBack == tmp.CelloBack);
				if (!exist)
				{
					oldlist.Add(celloOption);
				}
			}
			return oldlist;
		}

		private JobCelloglazeOptions ParseJobCelloglazeOptions(string value)
		{
			switch (value)
			{
				case "N":
					return None;

				case "G":
					return Gloss;

				case "M":
					return Matt;

				case "V":
					return Velvet;

				case "E":
					return Emboss;

				case "F":
					return Foil;

				case "S":
					return SpotUV;

				case "SMF":
					return SpotUVFrontMattFront;

				case "EGF":
					return EmbossedGlossFront;

				case "EMF":
					return EmbossedMattFront;

				default:
					return None;
			}
		}

		private IList<JobPrintOptions> ParseColour(string colour, IList<JobPrintOptions> oldlist)
		{
			Action<JobPrintOptions> add = (c) =>
			{
				if (!oldlist.Contains(c))
				{
					oldlist.Add(c);
				}
			};

			foreach (var c in colour)
			{
				switch (c)
				{
					case 'N':
						add(Unprinted);
						break;

					case 'F':
						add(Printed);
						break;

					case 'B':
						add(BW);
						break;

					case 'R':
						add(Black);
						add(ReflexBlue);
						break;
				}
			}

			return oldlist;
		}
	}
}
