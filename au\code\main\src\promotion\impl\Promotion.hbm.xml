<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2"
                   namespace="lep.promotion"
                   assembly="lep"
                   auto-import="true"
                   default-cascade="all">

  <class name="IPromotion" table="`Promotion`" discriminator-value="null">

    <cache usage="read-write" />
    <id name="Id" type="Int32" unsaved-value="0">
      <generator class="identity" />
    </id>
    <discriminator column="Id" type="Int32" insert="false" />
    <timestamp name="DateModified" column="DateModified" />

    <property column="PromotionCode"    type="String" name="PromotionCode"      not-null="true" length="20" unique="true" />
    <property column="ShortDescription" type="String" name="ShortDescription"   not-null="true" length="255" />
    <property column="LongDescription"  type="String" name="LongDescription"    length="2147483647" />
    <property column="SalesDescription" type="String" name="SalesDescription"   length="255" />
    <property column="MaxDeduction"		type="Decimal"	name="MaxDeduction"      not-null="true" />
    <property column="Discount"         type="Int32"    name="Discount"         not-null="true" />
    <property column="MaxDiscount"      type="Decimal"  name="MaxDiscount"      not-null="true" />

    <property column="MaxJobPrice"      type="Decimal"  name="MaxJobPrice"      not-null="true" />
    <property column="MinJobPrice"      type="Decimal"  name="MinJobPrice"      not-null="true" />
    <property column="MinJobQuantity"   type="Int32"    name="MinJobQuantity"   not-null="true" />

    <property column="MaxOrderPrice"    type="Decimal"  name="MaxOrderPrice"    not-null="true" />
    <property column="MinOrderPrice"    type="Decimal"  name="MinOrderPrice"    not-null="true" />

    <property column="OnlyFirstOrder"   type="YesNo"  name="OnlyFirstOrder"   not-null="true" />
    <property column="CanUseOnce"       type="YesNo"  name="CanUseOnce"       not-null="true" />
    <property column="FreeDelivery"     type="YesNo"  name="FreeDelivery"     not-null="true" />
    <property column="FreeBusinessCard" type="YesNo"  name="FreeBusinessCard" not-null="true" />

    <property column="LifeSpan"         type="lep.GenericEnum`1[lep.promotion.PromotionLifeSpan], lep" name="LifeSpan" not-null="true" />

    <property column="DateValidStart"   type="lumen.hibernate.type.DateTimeType, lumen" name="DateValidStart" />
    <property column="DateValidEnd"     type="lumen.hibernate.type.DateTimeType, lumen" name="DateValidEnd" />
    <property column="Window"           type="Int32"    name="Window" />

    <property column="OnlyValidInCampaign"          type="YesNo" name="OnlyValidInCampaign"           not-null="true" />
    <property column="CheckCustomerAgainstCampaign" type="YesNo" name="CheckCustomerAgainstCampaign"    not-null="true" />
    <property column="Active"                       type="YesNo" name="Active"                          not-null="true" />

    <property name="DateCreated"  column="DateCreated"   type="lumen.hibernate.type.DateTimeType, lumen" not-null="true" update="false" />

    <bag name="PromotedProducts" cascade="all-delete-orphan" lazy="true">
      <key column="PromotionId"  not-null="true" />
      <one-to-many class="lep.promotion.IPromotedProduct, lep" />
    </bag>

    <property column="SalesCategoryLead"        type="YesNo"  name="SalesCategoryLead"        not-null="true" />
    <property column="SalesCategoryProspect"    type="YesNo"  name="SalesCategoryProspect"    not-null="true" />
    <property column="SalesCategoryLapsed"      type="YesNo"  name="SalesCategoryLapsed"      not-null="true" />
    <property column="SalesCategoryCustomer"    type="YesNo"  name="SalesCategoryCustomer"    not-null="true" />

    <subclass name="lep.promotion.impl.Promotion, lep" proxy="IPromotion" discriminator-value="not null" />
  </class>

  <class name="IPromotedProduct" table="`PromotedProducts`"  discriminator-value="null">
    <cache usage="read-write" />
    <id name="Id" type="Int32" unsaved-value="0">
      <generator class="identity" />
    </id>
    <discriminator column="Id" type="Int32" insert="false" />

    <!--
        <many-to-one name="Promotion"   column="PromotionId" class="lep.promotion.IPromotion, lep" not-null="true" cascade="none" />
        <many-to-one name="JobTemplate" column="JobOptionId" class="lep.job.IJobTemplate, lep"      not-null="true" cascade="none" />
        <many-to-one name="Stock"       column="StockId"     class="lep.job.IStock, lep"            not-null="true" cascade="none" />
        <many-to-one name="PaperSize"   column="PaperSizeId" class="lep.job.IPaperSize, lep"        not-null="true" cascade="none" />
    -->

    <property column="JobOptionId" type="Int32"    name="JobOptionId" />
    <property column="StockId"     type="Int32"    name="StockId" />
    <property column="PaperSizeId" type="Int32"    name="PaperSizeId" />
    <!--<property column="PromotionId" type="Int32"    name="PromotionId" />-->
    <subclass name="lep.promotion.impl.PromotedProduct, lep" proxy="IPromotedProduct" discriminator-value="not null" />
  </class>

  <class name="CustomerOffer" table="`CustomerOffers`"  discriminator-value="null">
		<cache usage="read-write" />
		<id name="Id" type="Int32" unsaved-value="0">
			<generator class="identity" />
		</id>
		<discriminator column="Id" type="Int32" insert="false" />
		<timestamp name="DateModified" column="DateModified" />
    <property name="CreatedBy"   type="String"   not-null="false" length="50" />
    <property name="ModifiedBy"  type="String"   not-null="false" length="50" />

		<many-to-one name="Customer"   class="lep.user.impl.CustomerUser, lep"   column="CustomerId"  not-null="true" cascade="none" />
		<many-to-one name="Promotion"  class="lep.promotion.impl.Promotion, lep" column="PromotionId" not-null="true" cascade="none" />
		<property name="DateOffered" />
		<property name="DateOfferEnds" />
		<property name="DateTakenUp" not-null="false" />
    <property name="AllowReuse"  type="YesNo" not-null="true" />

    <property name="OrderNumberUsedOn"  not-null="false" type="lep.JsonType`1[lep.promotion.ListOfUsesOfOffer], lep" />

		<property name="DateCreated"  column="DateCreated"   type="lumen.hibernate.type.DateTimeType, lumen" not-null="true" update="false" />
  </class>
</hibernate-mapping>