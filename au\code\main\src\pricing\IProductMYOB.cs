using lep.job;
using System;

namespace lep.pricing
{
	//public interface IProductMYOB
 //   {
 //       int Id { get; set; }
 //       IJobTemplate Template { get; set; }
 //       IStock Stock { get; set; }
 //       IPaperSize PaperSize { get; set; }
 //       int NumPages { get; set; }
 //       int NumColourSides { get; set; }
 //       string Celloglazing { get; set; }
 //       string MYOB1 { get; set; } // Being used as other MYOB
 //       string MYOB2 { get; set; }
 //       SiteLocation SiteLocation { get; set; }
 //       PrintType PrintType { get; set; }
 //       DateTime DateCreated { get; set; }
 //       DateTime DateModified { get; set; }
 //   }
}
