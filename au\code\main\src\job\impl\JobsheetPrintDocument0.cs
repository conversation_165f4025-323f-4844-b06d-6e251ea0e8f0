using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Printing;
using System.Globalization;

namespace lep.job.impl
{
	public class JobSheetPrintDocument0 : PrintDocument
	{
		private int currentIndex = 0;

		private IList<IJob> multijobs = new List<IJob>();
		private string tickimg;

		public JobSheetPrintDocument0(IList<IJob> jobs, string tickpath)
			: base()
		{
			multijobs = jobs;
			tickimg = tickpath;
		}

		protected override void OnPrintPage(PrintPageEventArgs e)
		{
			base.OnPrintPage(e);
			DoPrint(-10, -9, e.Graphics, multijobs[currentIndex]);
			if (currentIndex < multijobs.Count - 1)
			{
				currentIndex++;
				e.Has<PERSON>ore<PERSON> = true;
			}
		}

		private string StockString(IStock stock)
		{
			return stock.Name.Replace("Deluxe", "").Replace("LEP", "");
		}

		public string JobInfoElide(IJob job)
		{
			var jobinfo = job.Name.Length > 16 ? job.Name.Substring(0, 13) + "..." : job.Name;
			return String.Format("{2} - {0} x {1:0.##}K ({2})", jobinfo, job.Quantity / 1000.0, job.JobNr, job.Facility);
		}

		private void DoPrint(int x, int y, Graphics g, IJob job)
		{
			var wordFont = new Font("Arial", 12);
			var jobFont = new Font("Arial", 14);
			Brush brush = new SolidBrush(Color.Black);
			var barcodefont = new Font(new Font("Free 3 of 9", 12F).FontFamily, (float)38, FontStyle.Regular);
			var tickImg = Image.FromFile(tickimg);

			//barcode
			g.DrawString(String.Format("*{0}*", job.Barcode), barcodefont, brush, x + 567, y + 100);
			if (job.Runs.Count > 0)
			{
				g.DrawString(job.Runs[0].Barcode, jobFont, brush, x + 615, y + 155);
			}
			g.DrawString(job.Barcode, jobFont, brush, x + 615, y + 70);

			//jobdescription
			g.DrawString(job.Template.Name, wordFont, brush, x + 152, y + 22);
			//g.DrawString(job.MYOB, wordFont, brush, x + 380, y + 22);
			if (job.ReceivedDate.HasValue)
				g.DrawString(job.ReceivedDate.Value.ToString("dd/MM/yyyy HH:mm"), wordFont, brush, x + 620, y + 22);

			g.DrawString(job.Name, wordFont, brush, x + 110, y + 68);

			//SR 1118175 Print  Page # next to job name right justified
			var typesWithPages = new List<JobTypeOptions>()
			{
				JobTypeOptions.Magazine,
				JobTypeOptions.MagazineSeparate,
			};
			if (typesWithPages.Contains((JobTypeOptions)job.Template.Id))
			{
				// if magazines or separaet magazine
				g.DrawString(job.Pages.ToString() + "pp", wordFont, brush, x + 525, y + 68); // show number of pages
			}

			g.DrawString(job.Quantity.ToString(), wordFont, brush, x + 110, y + 102);

			g.DrawString("1/Sided", wordFont, brush, x + 305, y + 102);
			g.DrawString("2/Sided", wordFont, brush, x + 415, y + 102);
			if (!job.IsCMYK)
			{
				g.DrawImage(tickImg, x + 380, y + 102);
			}
			else
			{
				g.DrawImage(tickImg, x + 490, y + 102);
			}

			g.DrawString(job.FinishedSize.PaperSize.Name, wordFont, brush, x + 110, y + 134);

			//SR 1114850 - Show Potrait or Landscape as P/L after size
			var typesWithoutRotation = new List<JobTypeOptions>()
			{
                //JobTypeOptions.BusinessCard,
                //JobTypeOptions.DoubleBusinessCard,
                JobTypeOptions.PresentationFolder
			};

			if (!typesWithoutRotation.Contains((JobTypeOptions)job.Template.Id))
			{
				// if nit presentation folder
				g.DrawString(job.Rotation == RotationOption.Portrait ? "(P)" : "(L)", wordFont, brush, x + 157, y + 134);
				// show rotation
			}

			g.DrawString(job.FinishedSize.Width.ToString(), wordFont, brush, x + 295, y + 134);
			g.DrawString(job.FinishedSize.Height.ToString(), wordFont, brush, x + 465, y + 134);

			if (job.Proofs.ProofsRequired)
			{
				g.DrawImage(tickImg, x + 155, y + 165);
			}
			g.DrawString(job.Proofs.NumProofsSentA1.ToString(), wordFont, brush, x + 380, y + 165);
			g.DrawString(job.Proofs.NumProofsSentA2.ToString(), wordFont, brush, x + 445, y + 165);
			g.DrawString(job.Proofs.NumProofsSentA3.ToString(), wordFont, brush, x + 512, y + 165);

			//prepress-printing
			/*
            g.DrawString( StockString(job.Stock) ,wordFont,brush,x + 160,y + 270 );
            if (job.Prepress.OneSided.Sheets > 0) {
                g.DrawString( job.Prepress.OneSided.Sheets.ToString(),wordFont,brush,x + 160,y + 300 );
            }
            if (job.Prepress.OneSided.A1) {
                g.DrawImage( tickImg,x + 360,y + 300 );
            }
            if (job.Prepress.OneSided.A2) {
                g.DrawImage( tickImg,x + 460,y + 300 );
            }

            if (job.Prepress.WorkAndTurn.Sheets > 0) {
                g.DrawString( job.Prepress.WorkAndTurn.Sheets.ToString(),wordFont,brush,x + 160,y + 332 );
            }
            if (job.Prepress.WorkAndTurn.A1) {
                g.DrawImage( tickImg,x + 360,y + 332 );
            }
            if (job.Prepress.WorkAndTurn.A2) {
                g.DrawImage( tickImg,x + 460,y + 332 );
            }

            if (job.Prepress.WorkAndTumble.Sheets > 0) {
                g.DrawString( job.Prepress.WorkAndTumble.Sheets.ToString(),wordFont,brush,x + 160,y + 365 );
            }
            if (job.Prepress.WorkAndTumble.A1) {
                g.DrawImage( tickImg,x + 360,y + 365 );
            }
            if (job.Prepress.WorkAndTumble.A2) {
                g.DrawImage( tickImg,x + 460,y + 365 );
            }

            if (job.Prepress.SheetWork.Sheets > 0) {
                g.DrawString( job.Prepress.SheetWork.Sheets.ToString(),wordFont,brush,x + 160,y + 395 );
            }
            if (job.Prepress.SheetWork.A1) {
                g.DrawImage( tickImg,x + 360,y + 395 );
            }
            if (job.Prepress.SheetWork.A2) {
                g.DrawImage( tickImg,x + 460,y + 395 );
            }

            if (job.Prepress.Cover.Sheets > 0) {
                g.DrawString( job.Prepress.Cover.Sheets.ToString(),wordFont,brush,x + 160,y + 425 );
            }
            if (job.Prepress.Cover.A1) {
                g.DrawImage( tickImg,x + 360,y + 425 );
            }
            if (job.Prepress.Cover.A2) {
                g.DrawImage( tickImg,x + 460,y + 425 );
            }

            if (job.Prepress.PrepressBy != null) {
                g.DrawString( String.Format( "{0} {1}",job.Prepress.PrepressBy.FirstName,job.Prepress.PrepressBy.LastName ),wordFont,brush,x + 630,y + 300 );
                g.DrawString( job.Prepress.PrepressDate.ToString( "dd/MM/yyyy HH:mm" ),wordFont,brush,x + 630,y + 330 );
            }

            if (job.PrintedDate > DateTime.MinValue) {
                g.DrawString( job.PrintedDate.ToString( "dd/MM/yyyy HH:mm" ),wordFont,brush,x + 630,y + 360 );
            }

            if (job.StockForCover != null) {
                g.DrawString( StockString(job.StockForCover) ,wordFont,brush,x + 630,y + 425 );
            }*/

			for (var i = 0; i < job.PressDetails.Count; i++)
			{
				//SR 1130601,  Max prepress details is 5 now
				if (i == 5)
					break;

				var posy = 302 + 32 * (i >= 5 ? i - 5 : i);
				var posx = i >= 5 ? 415 : 23;
				g.DrawString(job.PressDetails[i].Stock, wordFont, brush, x + posx, y + posy);
				if (job.PressDetails[i].Sect > 0)
				{
					g.DrawString(job.PressDetails[i].Sect.ToString(), wordFont, brush, x + posx + 157, y + posy);
				}
				g.DrawString(job.PressDetails[i].Size, wordFont, brush, x + posx + 205, y + posy);
				g.DrawString(job.PressDetails[i].Method, wordFont, brush, x + posx + 253, y + posy);
				if (job.PressDetails[i].Qty > 0)
				{
					g.DrawString(job.PressDetails[i].Qty.ToString(), wordFont, brush, x + posx + 310, y + posy);
				}
			}

			//finishing
			if (job.FoldedSize != null)
			{
				if (job.FoldedSize.PaperSize.Name == "Custom")
				{
					g.DrawString(
						String.Format("{0}({1}x{2})", job.FoldedSize.PaperSize.Name, job.FoldedSize.Height,
							job.FoldedSize.Width), wordFont, brush, x + 145, y + 525);
				}
				else
				{
					g.DrawString(job.FoldedSize.PaperSize.Name, wordFont, brush, x + 145, y + 525);
				}
			}

			/*
            SizeF size = g.MeasureString( job.SpecialInstructions,wordFont,145 );
            Rectangle rect = new Rectangle( x + 340,y + 525,145,(int) size.Height );
            g.DrawString( job.SpecialInstructions,wordFont,brush,rect );
            */

			g.DrawString(String.Format("{0} ({1})", job.FinishedSize.PaperSize.Name, job.Rotation.ToString()[0]),
				wordFont, brush, x + 145, y + 555);
			g.DrawString(job.FinishedSize.Width.ToString(), wordFont, brush, x + 310, y + 555);
			g.DrawString(job.FinishedSize.Height.ToString(), wordFont, brush, x + 440, y + 555);

			if (job.Scoring)
			{
				g.DrawImage(tickImg, x + 145, y + 590);
			}
			var size = g.MeasureString(job.ScoringInstructions, wordFont, 255);
			var rect = new Rectangle(x + 225, y + 590, 255, (int)size.Height);
			g.DrawString(job.ScoringInstructions, wordFont, brush, rect);

			if (job.Perforating)
			{
				g.DrawImage(tickImg, x + 145, y + 620);
			}
			size = g.MeasureString(job.PerforatingInstructions, wordFont, 255);
			rect = new Rectangle(x + 225, y + 620, 255, (int)size.Height);
			g.DrawString(job.PerforatingInstructions, wordFont, brush, rect);

			if (job.DieCutType != CutOptions.None)
			{
				size = g.MeasureString(job.DieCutType.ToString() + " " + job.DieCutting, wordFont, 335);
				rect = new Rectangle(x + 145, y + 650, 335, (int)size.Height);
				g.DrawString(job.DieCutType.ToString() + " " + job.DieCutting, wordFont, brush, rect);
			}

			//SR 1118175 When Celloglaze Front or back = none do not print none
			if (job.FinalFrontCelloglaze != JobCelloglazeOptions.None)
			{
				g.DrawString(job.FinalFrontCelloglaze.ToString(), wordFont, brush, x + 190, y + 682);
			}
			if (job.FinalBackCelloglaze != JobCelloglazeOptions.None)
			{
				g.DrawString(job.FinalBackCelloglaze.ToString(), wordFont, brush, x + 370, y + 682);
			}

			if (job.NumberOfMagnets>0)
			{
				g.DrawString(job.NumberOfMagnets + " Magnet", wordFont, brush, x + 460, y + 682);
			}

			size = g.MeasureString(job.SpecialInstructions + "\n" + job.RequestedPackaging, wordFont, 250);
			rect = new Rectangle(x + 510, y + 530, 250, (int)size.Height);
			g.DrawString(job.SpecialInstructions + "\n" + job.RequestedPackaging, wordFont, brush, rect);

			if (job.Template.Id == (int)JobTypeOptions.Notepads)
			{
				if (job.PadDirection != PadDirection.None)
				{
					g.DrawString(String.Format("{0}", job.PadDirection.ToDescription()), wordFont, brush, x + 635,
						y + 682);
				}
			}

			//packing
			if (!job.Order.Courier.IsNone)
			{
				g.DrawString(job.Order.Courier, wordFont, brush, x + 285, y + 738);
			}

			size = g.MeasureString(job.GetActualPackaging(), wordFont, 340);
			rect = new Rectangle(x + 20, y + 815, 340, (int)size.Height);
			g.DrawString(job.GetActualPackaging(), wordFont, brush, rect);
			g.DrawString(job.Order.OrderNr, wordFont, brush, x + 485, y + 780);
			g.DrawString(
				String.Format("Job Price ${0}",
					decimal.Parse(job.Price).ToString("N", CultureInfo.CurrentCulture.NumberFormat)), wordFont, brush,
				x + 635, y + 780);

			var initialy = 815;
			foreach (var j in job.Order.Jobs)
			{
				var jobinfo = JobInfoElide(j);
				g.DrawString(jobinfo, wordFont, brush, x + 400, y + initialy);
				initialy += 23;
			}

			if (job.FinishedDate.HasValue)
			{
				g.DrawString(job.FinishedDate.Value.ToString("dd/MM/yyyy HH:mm"), wordFont, brush, x + 530, y + 960);
			}

			g.DrawString(job.Order.PurchaseOrder, wordFont, brush, x + 490, y + 1022);

			//customer detail
			g.DrawString(job.Order.Customer.Name, wordFont, brush, x + 115, y + 1060);
			g.DrawString(job.Order.Contact.Name, wordFont, brush, x + 115, y + 1090);
			g.DrawString(job.Order.Contact.AreaCode + " " + job.Order.Contact.Phone, wordFont, brush, x + 525, y + 1060);
			g.DrawString(job.Order.Contact.Mobile, wordFont, brush, x + 525, y + 1090);

			//edge test
			/*
            Pen r = new Pen( new SolidBrush( Color.Red ) );
            g.DrawRectangle( r,0,0,10,10 );
            g.DrawRectangle( r,0,1117,10,10 );
            Pen b = new Pen( new SolidBrush( Color.Blue ) );
            g.DrawRectangle( b,778,0,10,10 );
            g.DrawRectangle( b,778,1117,10,10 );
            */
		}
	}
}
