#region using

using lumen.csv;
using System;
using System.Collections.Generic;

#endregion using

namespace lep.jobmonitor.impl
{
	internal class JobBoardLayoutParser : CsvParser
    {
        private bool isHeader = true;

        public JobBoardLayoutParser()
        {
            HandleQuotes = true;
            Data = new Dictionary<string, JobBoardDisplayColumnSetting>();
        }

        public Dictionary<string, JobBoardDisplayColumnSetting> Data { get; private set; }

        public override void RowData(string[] values)
        {
            if (isHeader)
            {
                isHeader = false;
            }
            else if (values.Length >= 3 && !String.IsNullOrEmpty(values[0]))
            {
                Data.Add(values[0], new JobBoardDisplayColumnSetting
                {
                    Width = String.IsNullOrEmpty(values[1]) ? String.Empty : values[1] + "px",
                    CssClass = values[2]
                });
            }
            base.RowData(values);
        }
    }
}