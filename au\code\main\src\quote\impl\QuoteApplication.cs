using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using log4net;
using lep.configuration;
using lep.email;
using lep.job;
using lep.macro.impl;
using lep.security;
using lep.user;
using NHibernate;
using NHibernate.Criterion;

namespace lep.quote.impl
{
    /// <summary>
    ///
    /// </summary>
    public class QuoteApplication : BaseApplication, IQuoteApplication
    {
        private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);
        private IConfigurationApplication configApp;

        private FileInfo quoteTemplate;
        private FileInfo rejectTemplate;
        private string urlPrefix;

#if LEP2016

        public QuoteApplication (ISession sf, ISecurityApplication _securityApp) : base(sf, _securityApp)
        {
        }
#else
    public QuoteApplication ()
        {
        }


#endif





        public FileInfo QuoteTemplate
        {
            set { quoteTemplate = value; }
        }

        public FileInfo RejectTemplate
        {
            set { rejectTemplate = value; }
        }

        public string QuoteUrl
        {
            set { urlPrefix = value; }
        }

        public IConfigurationApplication ConfigurationApplication
        {
            set { configApp = value; }
        }

        #region IQuoteApplication Members

        public IQuote NewQuote (ICustomerUser customer, string description)
        {
            //AssertPermission("quote.create");
            var quote = new Quote(customer, description);
            quote.Status = QuoteStatusOptions.Requested;
            return quote;
        }

        public IQuote GetQuote (int Id)
        {
            //AssertPermission("quote.read");
            return Get<IQuote>(Id);
        }

        public void Save (IQuote quote)
        {
            //AssertPermission("quote.update");
            base.Save<IQuote>(quote);
        }

        public void Delete (IQuote quote)
        {
            //AssertPermission("quote.delete");
            base.Delete<IQuote>(quote);
        }

        public ICriteria QuoteCriteria (string customer, string quoteNr, bool request, bool rejected, bool supplied,
            bool completed)
        {
            var statuslist = new List<QuoteStatusOptions>();

            var criteria = Session.CreateCriteria(typeof(IQuote), "quote");
            criteria.CreateAlias("Customer", "cust");
            criteria.CreateAlias("Customer.Contact1", "contact1");

            if (customer != "") {
                var customerId = 0;
                Int32.TryParse(customer, out customerId);
                var customerDisjunction = Restrictions.Disjunction();

                customerDisjunction.Add(Restrictions.Eq("cust.Id", customerId));
                customerDisjunction.Add(Restrictions.Like("cust.Contact1.Name", customer, MatchMode.Anywhere));
                customerDisjunction.Add(Restrictions.Like("cust.Contact1.Phone", customer, MatchMode.Anywhere));
                customerDisjunction.Add(Restrictions.Like("cust.Contact2.Name", customer, MatchMode.Anywhere));
                customerDisjunction.Add(Restrictions.Like("cust.Contact2.Phone", customer, MatchMode.Anywhere));
                criteria.Add(customerDisjunction);
            }
            if (quoteNr != "") {
                var quoteNrInt = 0;
                Int32.TryParse(quoteNr, out quoteNrInt);
                criteria.Add(Restrictions.Eq("Id", quoteNrInt));
            }

            if (rejected) {
                statuslist.Add(QuoteStatusOptions.Rejected);
            }

            if (request) {
                statuslist.Add(QuoteStatusOptions.Requested);
            }

            if (supplied) {
                statuslist.Add(QuoteStatusOptions.Quoted);
            }

            if (statuslist.Count != 0) {
                criteria.Add(Restrictions.In("Status", statuslist));
            }

            var jobCriteria =
                DetachedCriteria.For(typeof(IJob), "job").Add(Restrictions.EqProperty("job.Quote.Id", "quote.Id"));
            jobCriteria.SetProjection(Projections.Property("job.Quote.Id"));


            if (!completed) {
                criteria.Add(Subqueries.NotExists(jobCriteria));
            } else {
                criteria.Add(Subqueries.Exists(jobCriteria));
            }
            var validDisjuntion = Restrictions.Disjunction();
            validDisjuntion.Add(Restrictions.Ge("ValidUntil", DateTime.Now));
            validDisjuntion.Add(Restrictions.IsNull("ValidUntil"));
            criteria.Add(validDisjuntion);
            return criteria;
        }

        public ICriteria QuoteCriteria (bool ready, bool used, bool requested, bool rejected)
        {
            var statuslist = new List<QuoteStatusOptions>();

            var jobCriteria =
                DetachedCriteria.For(typeof(IJob), "job").Add(Restrictions.EqProperty("job.Quote.Id", "quote1.Id"));
            jobCriteria.SetProjection(Projections.Property("job.Quote.Id"));

            var completestatuslist = new List<OrderStatusOptions>();
            //completestatuslist.Add(OrderStatusOptions.Withdrawn);
            completestatuslist.Add(OrderStatusOptions.Archived);

            var validUntil = Convert.ToDateTime(DateTime.Now.AddMonths(-8).ToShortDateString());

            var criteria = Session.CreateCriteria(typeof(IQuote), "quote1");
            if (ready) {
                statuslist.Add(QuoteStatusOptions.Quoted);
            }
            if (requested) {
                statuslist.Add(QuoteStatusOptions.Requested);
            }
            if (rejected) {
                statuslist.Add(QuoteStatusOptions.Rejected);
            }
            if (statuslist.Count != 0) {
                criteria.Add(Restrictions.In("Status", statuslist));
            }

            if (used) {
                criteria.Add(Subqueries.Exists(jobCriteria));
                criteria.Add(Restrictions.Ge("ValidUntil", validUntil));
            }
            var jobCriteria1 = DetachedCriteria.For(typeof(IJob), "job1");
            jobCriteria1.Add(Restrictions.EqProperty("job1.Quote.Id", "quote1.Id"));
            jobCriteria1.CreateAlias("Order", "o");
            jobCriteria1.Add(Restrictions.Not(Restrictions.In("o.Status", completestatuslist)));
            jobCriteria1.SetProjection(Projections.Property("job1.Quote.Id"));
            var validWithOrder = Restrictions.Conjunction();
            validWithOrder.Add(Subqueries.Exists(jobCriteria1));
            validWithOrder.Add(Restrictions.Ge("ValidUntil", validUntil));
            var valid = Restrictions.Disjunction();
            valid.Add(Restrictions.IsNull("ValidUntil"));
            valid.Add(Subqueries.NotExists(jobCriteria));
            valid.Add(validWithOrder);
            criteria.Add(valid);
            return criteria;
        }

        public IMailMessage GenerateNotice (IQuote quote, IMailMessage message)
        {
            try {
                if (quoteTemplate != null) {
                    var emailAddress = (string)configApp.GetValue(Configuration.EmailReplyAddress);
                    message.From = emailAddress;
                    var typeMessage = "quote";
                    message.To = quote.Customer.AccountEmail;

                    FileInfo file = null;
                    ;
                    if (quote.Status == QuoteStatusOptions.Quoted) {
                        message.Subject = "Quotation is supplied";
                        file = quoteTemplate;
                        typeMessage = "Quotation";
                    } else if (quote.Status == QuoteStatusOptions.Rejected) {
                        message.Subject = "Quotation is rejected";
                        file = rejectTemplate;
                        typeMessage = "Rejection";
                    }

                    var sr = file.OpenText();
                    var msg = new StringWriter();
                    try {
                        var s = "";
                        var filter = new StringMacroFilter(msg);
                        filter["customer"] = quote.Customer.Name;
                        filter["contact"] = quote.Customer.Contact1.Name;
                        filter["areacode"] = quote.Customer.Contact1.AreaCode;
                        filter["phone"] = quote.Customer.Contact1.Phone;
                        filter["mobile"] = quote.Customer.Contact1.Mobile;
                        filter["request"] = quote.Description;
                        if (quote.ValidUntil != DateTime.MinValue) {
                            filter["validuntil"] = quote.ValidUntil.ToString("dd/MM/yyyy");
                        } else {
                            filter["validuntil"] = "";
                        }

                        filter["quotenr"] = quote.QuoteNr;
                        filter["quotedate"] = quote.DateModified.ToString("dd/MM/yyyy");
                        filter["quotation"] = quote.Quotation;
                        filter["quotationlink"] =
                            string.Format(
                                "<a href=\"{0}/customer/quote-view.aspx?quoteId={1}\">Please click here to view {2}</a>",
                                urlPrefix, quote.Id, typeMessage);

                        while ((s = sr.ReadLine()) != null) {
                            //filter.WriteLine( s );
                        }

                        message.Body = msg.ToString();
                        return message;
                    } catch (Exception ex) {
                        Log.Error("Error in reading the template file :" + ex.Message, ex);
                    }
                }
            } catch (Exception ex) {
                Log.Error("Error for Send Login Details : " + ex.Message, ex);
            }
            return null;
        }

        #endregion
    }
}