using System;
using System.Collections.Generic;

namespace lep.job
{
	public interface IJobTemplate //: IIdAndName
	{
		int Id { get; set; }
		string Name { get; set; }
		string Category { get; set; }

		DateTime DateCreated { get; set; }
		DateTime DateModified { get; set; }
		bool FG_Production { get; set; }
		bool PM_Production { get; set; }

		//IList<IJobOptionSpecSize> SizeOptions { get; set; }

		int ColourSide(JobPrintOptions front, JobPrintOptions back);

		bool HasMultiplePages();

		bool Is(params JobTypeOptions[] list);

		bool IsNot(params JobTypeOptions[] list);

		bool AllowsSamples();
	}
}
