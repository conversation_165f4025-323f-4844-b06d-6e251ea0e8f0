using lep.job;
using System;

namespace lep.pricing.impl
{
	/*
	public class ProductMYOB : IProductMYOB
    {
        public ProductMYOB()
        {
        }

        // TODO need unit test for constructor
        public ProductMYOB(SiteLocation siteLocation, PrintType printType, IJobTemplate template, IStock stock,
            IPaperSize papersize, int numColourSides, JobCelloglazeOptions frontCello, JobCelloglazeOptions backCello,
            int numpages, string myob1, string myob2)
        {
            Template = template;
            Stock = stock;
            PaperSize = papersize;
            NumPages = numpages;
            MYOB1 = myob1;
            MYOB2 = myob2;
            NumColourSides = numColourSides;
            SiteLocation = siteLocation;
            PrintType = printType; // Product myob is diffrent for print types
            Celloglazing = frontCello + "/" + backCello;
        }

        public int Id { get; set; }

        public IJobTemplate Template { get; set; }

        public IStock Stock { get; set; }

        public IPaperSize PaperSize { get; set; }

        public int NumPages { get; set; }

        public int NumColourSides { get; set; }

        public string Celloglazing { get; set; }

        public string MYOB1 { get; set; } = "";

        public string MYOB2 { get; set; } = "";

        public SiteLocation SiteLocation { get; set; }

        public PrintType PrintType { get; set; }

        public DateTime DateCreated { get; set; }

        public DateTime DateModified { get; set; }
    }
	*/
}
