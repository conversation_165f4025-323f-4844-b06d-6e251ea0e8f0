using lep.courier;
using lep.job;
using System.Collections.Generic;

namespace lep.freight
{
    public interface IPackDetail
    {
        decimal? Price { get; set; }

        string PackLog(Facility? facility);

        CourierType FGCourier { get; set; }
        string FGPackingSpecification { get; set; }
        bool IsFGCourierCustom { get; set; }

        CourierType PMCourier { get; set; }
        string PMPackingSpecification { get; set; }
        bool IsPMCourierCustom { get; set; }

        //string FGPackageJson { get; set; }
        //string PMPackageJson { get; set; }

        ListOfPackages FGPackageJson { get; set; }
        ListOfPackages PMPackageJson { get; set; }

        bool IsCustom { get; set; }
        bool IsCustomPackages { get; set; }

        IList<Package> GetPackages(Facility? facility);

        void SetPackages(IList<Package> items, Facility facility);
    }
}