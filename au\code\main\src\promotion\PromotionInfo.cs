﻿using System;

namespace lep.promotion
{
	[Serializable]
    public class PromotionInfo
    {
        #region Properties

        public string PromotionCode { get; set; }

        public bool Active { get; set; }

        public bool CanUseOnce { get; set; }

        public DateTime DateCreated { get; set; }

        public DateTime DateModified { get; set; }

        public DateTime? DateValidEnd { get; set; }

        public DateTime? DateValidStart { get; set; }

        public int Discount { get; set; }

        public bool FreeBusinessCard { get; set; }

        public bool FreeDelivery { get; set; }

        public int Id { get; set; }

        public PromotionLifeSpan LifeSpan { get; set; }

        public string LongDescription { get; set; }

        public decimal MaxJobPrice { get; set; }

        public decimal MaxOrderPrice { get; set; }

        public decimal MinJobPrice { get; set; }

        public int MinJobQuantity { get; set; }

        public decimal MinOrderPrice { get; set; }

        public bool OnlyFirstOrder { get; set; }

        public bool OnlyValidInCampaign { get; set; }

        public bool CheckCustomerAgainstCampaign { get; set; }

        public string SalesCategories
        {
            get {
                var value = "";
                value += SalesCategoryLead ? "Lead " : "";
                value += SalesCategoryProspect ? "Prospect " : "";
                value += SalesCategoryLapsed ? "Lapsed " : "";
                value += SalesCategoryCustomer ? "Customer " : "";
                return value;
            }
        }

        public bool SalesCategoryCustomer { get; set; }

        public bool SalesCategoryLapsed { get; set; }

        public bool SalesCategoryLead { get; set; }

        public bool SalesCategoryProspect { get; set; }

        public string SalesDescription { get; set; }

        public string ShortDescription { get; set; }

        public string Vaild
        {
            get {
                var value = "?";

                if (LifeSpan == PromotionLifeSpan.Windowed)
                {
                    value = Window + " days";
                }
                else
                {
                    if (DateValidStart.HasValue && DateValidEnd.HasValue)
                    {
                        var t = (int)(DateValidEnd.Value - DateValidStart.Value).TotalDays;

                        value = string.Format("{0} to {1} ({2} days)",
                            DateValidStart.Value.ToString("dd-MMM-yy"),
                            DateValidEnd.Value.ToString("dd-MMM-yy"),
                            t);
                    }
                }

                return value;
            }
        }

        public int Window { get; set; }

        #endregion Properties
    }
}