﻿using System.Collections.Generic;
using System.Linq;
using static lep.job.JobTypeOptions;

namespace lep.job.impl
{
	public static class QuantitiesByTemplate
	{
		public static int GetMaxValueFromTemplate(JobTypeOptions templateId)
		{
			var maxvalue = 100000; // default max value for all products

			if (templateId.Is(BusinessCardNdd,  PresentationFolderNDD, Notepads))
				maxvalue = 1000;
			if (templateId.Is(BusinessCardSdd))
				maxvalue = 2000;
			if (templateId.Is(BusinessCardNdd))
				maxvalue = 10000;
			else if (templateId.Is(LetterheadNDD, ComplimentsNDD, LetterheadSDD, ComplimentsSDD))
				maxvalue = 50000;
			else if (templateId.Is(BrochureSDD, MagazineNDD))
				maxvalue = 2000;
			else if (templateId.Is(DuplicateNCRBooks, TriplicateNCRBooks, QuadruplicateNCRBooks))
				maxvalue = 100;
			else if (templateId.Is(VinylSticker))
				maxvalue = 5000;
			else if (templateId.Is(RemovableWallDecals, VinylStickerOutdoor, RigidSigns))
				maxvalue = 5;
			else if (templateId.Is(PullUpBannerStandardStand, PullUpBannerPremiumStand, BacklitPosters, MeshBanner, VinylOutdoor, PosterCanvas))
				maxvalue = 5;
			else if (templateId.Is(PosterMattArt))
				maxvalue = 10;
			else if (templateId.Is(Poster))
				maxvalue = 100000;
			else if (templateId.Is(EnvelopeBlack, Envelope1Pms, Envelope2Pms, EnvelopeCmyk))
				maxvalue = 5000;
			else if (templateId.Is(WiroMagazines))
				maxvalue = 2000;
			return maxvalue;
		}

		public static IList<int> Get(JobTypeOptions templateId, IJobOptionSpecQuantity QuantityOption)
		{
			var maxvalue = 0;

			var qo = QuantityOption;
			if (qo.Change2 != 0)
			{
				maxvalue = qo.Change2;
			}
			else
			{
				maxvalue = GetMaxValueFromTemplate(templateId);
			}

			IList<int> tmp = new List<int>();
			tmp.Add(qo.Minium);
			int current = qo.Minium;
			int step = qo.Step1;
			var reachedChange = false;
			if (step > 0)
			{
				while (true)
				{
					current += step;
					if (current >= maxvalue)
					{
						tmp.Add(maxvalue);
						break;
					}
					if (current >= qo.Change && qo.Change != 0 && qo.Step2 == 0)
					{
						tmp.Add(qo.Change);
						break;
					}

					if (qo.Step2 != 0 && current >= qo.Change && !reachedChange)
					{
						step = qo.Step2;
						current = qo.Change;
						reachedChange = true;
					}

					tmp.Add(current);
				}
			}
			else if (qo.Change > 0)
			{
				tmp.Add(qo.Change);
				current = qo.Change;
				step = qo.Step2;
				if (step > 0)
				{
					while (true)
					{
						current += step;
						if (current >= maxvalue)
						{
							tmp.Add(maxvalue);
							break;
						}
						tmp.Add(current);
					}
				}
			}
			//
			return tmp;
		}

		/*
		given the following 
		specQ.Minium = 10
		specQ.Step1 = 10
		specQ.Change = 100
		specQ.Step2 = 10
		specQ.Change2 = 200
		*/
		public static IList<int> CreateEnoughPoints(JobTypeOptions templateId, IJobOptionSpecQuantity specQ)
		{
			var maxvalue = GetMaxValueFromTemplate(templateId); // maxvalue = 100000
			var x = specQ.Minium;
			IList<int> tmp = new List<int>();
			if (specQ.Minium == 0)
			{
				// should never occur should it???
			}
			else
			{
				// for all cases add minumum
				tmp.Add(specQ.Minium);
				//check for 1 point case
				if (specQ.Step1 == 0 && specQ.Change == 0 && specQ.Step2 == 0 && specQ.Change2 == 0)
				{
					// for specials all 0, minimum alread provided
				}
				else
				{
					//check first range that is from  min  to change
					if (specQ.Change > 0)
					{
						if (specQ.Step1 > 0)
						{
							// if step provided add some steps
							        // if step provided add some steps
							x += specQ.Step1;
							if (x % specQ.Minium == 0 && x <= specQ.Change)
							{
								//tmp.Add(x);
							}
						}
						tmp.Add(specQ.Change);
					}

					//check for 3 point case,  after change
					if (specQ.Change > 0 && specQ.Change2 > 0)
					{
						tmp.Add(x = specQ.Change);
						if (specQ.Step2 != 0)
						{
							x += specQ.Step2;
							if (x % specQ.Change == 0 && x <= specQ.Change2)
							{
								tmp.Add(x);
							}
						}
						tmp.Add(specQ.Change2);
					}
					// if only min and step1 provided then generate step wise
					if (specQ.Step1 > 0 && specQ.Change == 0 && specQ.Change2 == 0)
					{
						while (((x += specQ.Step1) % specQ.Minium == 0) && x <= maxvalue)
						{
							tmp.Add(x);
						}
					}
				}
			}
			return tmp.ToList().Distinct().ToList();
		}
	}
}
