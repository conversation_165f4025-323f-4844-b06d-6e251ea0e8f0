using System;

namespace lep.job.impl
{
	public class Proofs : IProofs
    {
        private int numProofsSentA1 = 0;
        private int numProofsSentA2 = 0;
        private int numProofsSentA3 = 0;

        public Proofs()
        {
        }

        public Proofs(bool req)
        {
            ProofsRequired = req;
            NumProofsSentA1 = 0;
            NumProofsSentA2 = 0;
            NumProofsSentA3 = 0;
        }

        public Proofs(bool req, int a1, int a2, int a3)
        {
            ProofsRequired = req;
            NumProofsSentA1 = a1;
            NumProofsSentA2 = a2;
            NumProofsSentA3 = a3;
        }

        #region IJobProofs Members

        public virtual bool ProofsRequired { get; set; } = false;

        public virtual int NumProofsSentA1
        {
            get { return numProofsSentA1; }
            set {
                if (value < 0)
                {
                    throw new ArgumentOutOfRangeException("NumProofsSentA1");
                }
                numProofsSentA1 = value;
            }
        }

        public virtual int NumProofsSentA2
        {
            get { return numProofsSentA2; }
            set {
                if (value < 0)
                {
                    throw new ArgumentOutOfRangeException("NumProofsSentA2");
                }
                numProofsSentA2 = value;
            }
        }

        public virtual int NumProofsSentA3
        {
            get { return numProofsSentA3; }
            set {
                if (value < 0)
                {
                    throw new ArgumentOutOfRangeException("NumProofsSentA3");
                }
                numProofsSentA3 = value;
            }
        }

        #endregion IJobProofs Members

        public override bool Equals(object obj)
        {
            if (obj == null) return false;
            var other = obj as Proofs;
            if (other == null) return false;
            if (ProofsRequired == other.ProofsRequired &&
                NumProofsSentA1 == other.NumProofsSentA1 &&
                NumProofsSentA2 == other.NumProofsSentA2 &&
                NumProofsSentA3 == other.NumProofsSentA3)
            {
                return true;
            }
            return false;
        }

		public override int GetHashCode()
		{
			return HashCode.Combine(ProofsRequired, NumProofsSentA1, NumProofsSentA2, NumProofsSentA3);
		}
	}
}
