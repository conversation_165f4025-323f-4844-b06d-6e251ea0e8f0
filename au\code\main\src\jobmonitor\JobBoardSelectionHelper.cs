using lep.job;
using System;
using System.Collections.Generic;
using System.Linq;
using static lep.job.JobStatusOptions;
using static lep.jobmonitor.JobBoardTypes;
using Serilog;

namespace lep.jobmonitor
{
	public static class JobBoardSelectionHelper
	{
		private static readonly Dictionary<JobStatusOptions, JobBoardTypes> OffsetPathMapping = new()
		{
			{ Filling, PrePress },
			{ LayoutRequired, PrePress },
			{ LayoutDone, PrePress },
			{ ApprovedForPlating, PrePress },
			{ PlatingDone, Plateroom },
			{ PressDone, PressRoom },
			{ DPCPreProduction, DPCProduction },
			{ DPCPrinted, DPCProduction },
			{ Celloglazed, Celloglaze },
			{ Cut, Guillotine },
			{ Folded, Folding },
			{ Scored, LetterPress },
			{ Letterpressed, LetterPress },
			{ Stitched, Stitching },
			{ Drilled, Finishing },
			{ Rounded, Finishing },
			{ Packed, Finishing },
			{ Dispatched, Despatch }
		};
		public static JobBoardTypes[] GetBoardsToAppearIn(IJob job)
		{
			// Special case for Packed status - should go to Despatch board
			if (job.Status == Packed || job.Status == ShrinkWrapped)
				return new JobBoardTypes[] { Despatch };

			// For all other cases, get the board based on current job status
			var board = GetBoardToAppearIn(job);
			return board == None ? Array.Empty<JobBoardTypes>() : new JobBoardTypes[] { board };
		}
		public static JobBoardTypes GetBoardToAppearIn(IJob job)
		{
			// Universal check for open/editable jobs
			if (job.IsOpenish())
				return None;

			// Special statuses with fixed board assignments
			if (job.ProofStatus == JobProofStatus.OnHold)
				return OnHold;

			//JobStatusOptions, JobBoardTypes
			switch (job.Status)
			{
				case JobStatusOptions.Outwork: return JobBoardTypes.Outwork;
				case JobStatusOptions.PayMe: return JobBoardTypes.PayMe;
				case ShrinkWrapped: return Despatch;
			}

		

			// Universal rules for all job types
			if (job.Status == Submitted)
				return PreFlight;

			if (job.Status == PreflightDone &&
				!job.Order.Jobs.All(j => j.SupplyArtworkApproval == JobApprovalOptions.NotNeeded))
			{
				return PrePress;
			}
			var x = StandardRouting.Instance.GetAllRoutesForJob(job);
			// Route based on job type
			if (job.ShouldFollowOffsetPath())
			{
				var nextStatus = StandardRouting.Instance.GetNextRouteForJob(job);
				return OffsetPathMapping.TryGetValue(nextStatus, out var board)
					? board
					: None;
			}

			//if (job.IsDigital())
			//{


			//	var x = StandardRouting.Instance.GetAllRoutesForJob (job);
			//	return job.NextStatus switch
			//	{
			//		Filling or LayoutRequired or LayoutDone or
			//		DPCPreProduction or	DPCPrinted or Finished
			//			=> DPCProduction,

			//	 	PressDone => PressRoom,
			//		Celloglazed => Celloglaze,
			//		Cut => Guillotine,
			//		Folded => Folding,
			//		Scored => LetterPress,
			//		Letterpressed => LetterPress,
			//		Stitched => Stitching,
			//		Drilled => Finishing,
			//		Rounded => Finishing,
			//		Packed => Finishing,
			//		_ => None
			//	};
			//}

			if (job.IsWideFormat())
			{
				return job.Status switch
				{
					JobStatusOptions.WideFormatProduction => JobBoardTypes.WideFormatProduction,
					WideFormatComplete => Despatch,
					_ => None
				};
			}

			// Error handling for unprocessed jobs
			if (job.Status != Dispatched && job.Status != Packed)
			{
				var nextStatus = StandardRouting.Instance.GetNextRouteForJob(job);
				Log.Error($"No Job Board mapping for {{JobId}} {job.Status}->{nextStatus}", job.Id);
			}

			return None;
		}
	}

	public static class JobTypeExtensions
	{
		public static bool ShouldFollowOffsetPath(this IJob job) =>
			(job.PrintType == PrintType.O) || (job.PrintType == PrintType.D);
			//job.IsDigitalAndRunGanged();
	}
}
