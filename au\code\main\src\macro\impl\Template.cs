using System.Collections.Specialized;
using System.IO;

namespace lep.macro.impl
{
	public class Template : ITemplate
    {
        private FileInfo file;
        private StringDictionary macro = new StringDictionary();

        public Template(FileInfo file)
        {
            this.file = file;
        }

        public string this[string name]
        {
            set { macro[name] = value; }
        }

        public override string ToString()
        {
            var writer = new StringWriter();
            var filter = new StringMacroFilter(writer);

            foreach (string s in macro.Keys)
            {
                filter[s] = macro[s];
            }

            StreamReader reader = null;
            try
            {
                reader = file.OpenText();

                while (true)
                {
                    var line = reader.ReadLine();
                    if (line == null)
                    {
                        break;
                    }
                    filter.WriteLine(line);
                }

                filter.Flush();
                return writer.ToString();
            }
            finally
            {
                if (reader != null)
                {
                    reader.Close();
                }
            }
        }
    }
}