using lep.job;
using lumen.csv;
using lumen.csv.handler;
using System.Collections.Generic;

namespace lep.pricing.csv
{
	public class PriceCsvReader : CsvParser
    {
        private PriceImportHandler importHandler;
        protected IJobApplication jobApp;

        protected IPricePointApplication priceApp;
        private SkipEmptyRowHandler skipEmptyRowHandler;
        private TrimWhitespaceHandler trimWhitespaceHandler;
        private PriceVerifyForeignKey verifyForeignKeyHandler;
        private PriceVerifySchemaHandler verifySchemaHandler;

        public PriceCsvReader()
            : base()
        {
            HandleQuotes = true;
            trimWhitespaceHandler = new TrimWhitespaceHandler();
            skipEmptyRowHandler = new SkipEmptyRowHandler();
            verifySchemaHandler = new PriceVerifySchemaHandler();
            verifyForeignKeyHandler = new PriceVerifyForeignKey();
            importHandler = new PriceImportHandler();

            handler = importHandler;
        }

        public int NumRowAdded { get; set; } = 0;

        public List<string> ErrorList
        {
            get { return importHandler.errorList; }
        }

        public IJobApplication JobApplication
        {
            set { jobApp = value; }
        }

        public IPricePointApplication PricePointApplication
        {
            set { priceApp = value; }
        }

        public override void RowData(string[] values)
        {
            base.RowData(values);
        }

        public override void StartDocument()
        {
            verifyForeignKeyHandler.JobApplication = jobApp;
            importHandler.JobApplication = jobApp;
            importHandler.PricePointApplication = priceApp;
            base.StartDocument();
        }

        public override void EndDocument()
        {
            NumRowAdded = importHandler.NumRowAdded;
            base.EndDocument();
        }
    }
}