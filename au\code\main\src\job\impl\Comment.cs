using lep.user;
using System;

namespace lep.job.impl
{
	public class Comment : IComment
    {
        private IUser author;
        private string commentText;
        private IJob job;

        public Comment()
        {
        }

        public Comment(IUser author, IJob job, string commentText, bool staffOnlyComment = false)
        {
            Author = author;
            Job = job;
            CommentText = commentText;
            CreationDate = DateTime.Now;
            LepOnly = staffOnlyComment;
        }

        #region IComment Members

        public virtual int Id { get; set; }

        public virtual string CommentText
        {
            get { return commentText; }
            set {
                if (value == null)
                {
                    throw new ArgumentNullException("CommentText");
                }
                commentText = value;
            }
        }

        public virtual IUser Author
        {
            get { return author; }
            set {
                if (value == null)
                {
                    throw new ArgumentNullException("Author");
                }
                author = value;
            }
        }

        public virtual DateTime CreationDate { get; set; }

        public virtual IJob Job
        {
            get { return job; }
            set {
                if (value == null)
                {
                    throw new ArgumentNullException("Job");
                }
                job = value;
            }
        }

        public virtual bool LepOnly { get; set; }

        #endregion IComment Members
    }
}