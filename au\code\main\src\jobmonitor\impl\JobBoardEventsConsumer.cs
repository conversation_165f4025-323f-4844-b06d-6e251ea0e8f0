﻿namespace lep.jobmonitor.impl
{/*
	public class JobBoardEventsConsumer : BaseApplication, IJobBoardEventsConsumer
	{
		private static readonly ILog log = LogManager.GetLogger("JobBoard");
		private bool modified = false;
		private IList<int> runIds = new List<int>();


		public JobBoardEventsConsumer (ISession sf, ISecurityApplication _securityApp,
			JobBoard globalJobBoard,
			JobBoardDTOHelper jobBoardDtoHelper
		) : base(sf, _securityApp)
		{
			JobBoardDTOHelper = jobBoardDtoHelper;
			GlobalJobBoard = globalJobBoard;
		}


		object syncroot = new object();

		public JobBoard GlobalJobBoard { get; set; }
		public JobBoardDTOHelper JobBoardDTOHelper { get; set; }
		public bool IsExternal { protected get; set; }

		public void ConsumeEvents ()
		{
			return;

			if (LepGlobal.Instance.ChangedJobs.Count() == 0)
				return;

			if (runIds == null) {
				runIds = new List<int>();
			} else {
				runIds.Clear();
			}

			lock (LepGlobal.Instance.JobBoardSyncRoot) {
				try {
					if (GlobalJobBoard.Entries == null || !GlobalJobBoard.Entries.Any()) {
						Log.Information("ConsumeEvents: job board null");
						return;
					}

					modified = false;
					var jobsProcessed = new List<int>();

					int jobId2 = 0;
					while (LepGlobal.Instance.ChangedJobs.TryTake(out jobId2)) {
						ProcessJob(jobId2);
						modified = true;
						jobsProcessed.Add(jobId2);
					}

					if (jobsProcessed.Count > 0) {
						if (modified) {
							GlobalJobBoard.ColoriseRuns();
						}

						GlobalJobBoard.UpdateVersion();
					}
				} catch (Exception ex) {
					Log.Error(ex.Message, ex);
				}
			}
		}

		/// <summary>
		/// Given a Job Board Event this function
		///		extracts the Job in question
		///		adds	JobDetailDTO in the Global job board => if it does not already exist
		///		deletes JobDetailDTO from the Global job board => if it has reached completed status?
		///		updates JobsDetailsDTOs Age and job health color other wise
		/// </summary>
		/// <param name="jobId"></param>
		///
		public void ProcessJob (int jobId)
		{
			IJob j = null;
			try {
				j = Session.Get<IJob>(jobId);
				if (j == null)
					return;

				Session.Refresh(j);

				var boardsToApparIn = JobBoardSelectionHelper.GetBoardsToAppearIn(j);
                var isEligibleForBeingInBoard = (boardsToApparIn.Count() > 0)
                                                    && j.Enable
                                                    && !j.HasReject
                                                    && j.SupplyArtworkApproval == JobApprovalOptions.NotNeeded
                                                    && j.ReadyArtworkApproval == JobApprovalOptions.NotNeeded
                                                    && j.ProofStatus == JobProofStatus.None
													&& !j.NeedApproval;

				//if (j.IsBusinessCard() && j.Runs.Count > 0) {
				//    runIds.Add(j.Runs[0].Id);
				//}

				var runId = 0;

				if (j.IsBusinessCard() && j.Runs.Count > 0) {
					runId = j.Runs[0].Id;
				}

				//Console.WriteLine($"{jobId}   {runId}");
				//if (runId == 226665) {
				//    int i = 0;
				//}
				// JOB Does not exist in board
				if (!GlobalJobBoard.HasJob(jobId)) {
					//jbLog.Information$"Job {jobId} not found in board. eligible to be in board? : {isEligibleForBeingInBoard}");

					if (isEligibleForBeingInBoard) {
						var newEntry = JobBoardDTOHelper.Create(j);
						GlobalJobBoard.Add(newEntry);
						modified = true;
						Log.Information$"Adding Job {jobId}  to board {boardsToApparIn[0]}");
					}
				} else {
					if (!isEligibleForBeingInBoard) {
						// if job is complete remove from board
						GlobalJobBoard.Remove(jobId);
						Log.Information$"Removing Job {jobId} , under run {runId}");
						modified = true;
					} else {
						GlobalJobBoard.Remove(jobId);
						var newEntry = JobBoardDTOHelper.Create(j);
						GlobalJobBoard.Add(newEntry);
						Log.Information$"Updating Job {jobId} to board {boardsToApparIn[0]}");
						modified = true;
					}
				}

				if (runId != 0) {
					var countOfjobsWithRun
						= GlobalJobBoard.Entries.Count(x => x.RunId == runId);
					if (countOfjobsWithRun > 0) {
						//var itemsToRemove = GlobalJobBoard.Entries.Where(x => x.Id == 0 && x.RunId == runId).ToArray();
						//  GlobalJobBoard.Entries = new System.Collections.Concurrent.ConcurrentBag<JobDetailsDTO>(GlobalJobBoard.Entries.Except(itemsToRemove));
						GlobalJobBoard.RemoveRun(runId);

						// find the first job of the run
						var i = GlobalJobBoard.Entries.FindIndex(jd => jd.RunId == runId);

						var runRow = JobBoardDTOHelper.CreateFromRun(runId, 3);
						// insert the run row just before the first job
						if (i > 0) {
							GlobalJobBoard.Entries.Insert(i - 1, runRow);
						} else {
							GlobalJobBoard.Entries.Add(runRow);
						}
					}
				}
			} catch (Exception ex) {
				Log.Error(ex.Message, ex);
			}
		}
	}
	*/
}
