using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Printing;
using System.IO;

namespace lep.job.printing
{
	public class JobPrintDocument : PrintDocument
    {
        private int currentLine = 0;
        private Font font;
        private string fontName = "Arial";
        private int fontSize = 10;
        private IJob job;
        private IList<IList<KeyValuePair<string, string>>> table = new List<IList<KeyValuePair<string, string>>>();

        public JobPrintDocument(IJob job)
            : base()
        {
            font = new Font(fontName, fontSize);
            this.job = job;
            FormatContent();
        }

        private void FormatContent()
        {
            var header = new KeyValuePair<string, string>("", "----------------- Job Details  ---------------");
            var dateSubmit = new KeyValuePair<string, string>("Date Submitted:",
                String.Format("{0:MM/dd/yy HH:mm}", job.Order.SubmissionDate));
            var orderNr = new KeyValuePair<string, string>("Order No:", job.Order.OrderNr);
            var jobNr = new KeyValuePair<string, string>("Job No:", job.JobNr);

            var spaceline = new KeyValuePair<string, string>("", "");
            var customer = new KeyValuePair<string, string>("Customer:", job.Order.Customer.Name.Trim());
            var customerNr = new KeyValuePair<string, string>("Customer Nr:", job.Order.Customer.CustomerNr);
            var contact = new KeyValuePair<string, string>("Contact:", job.Order.Customer.Contact1.Name.Trim());
            var createdDate = new KeyValuePair<string, string>("Created on:", job.DateCreated.ToString("dd/MM/yyyy"));
            var phoneString = String.Format("{0} {1}", job.Order.Customer.Contact1.AreaCode,
                Standardize(job.Order.Customer.Contact1.Phone, 12));
            var phone = new KeyValuePair<string, string>("Phone:", phoneString);
            var fax = new KeyValuePair<string, string>("Fax:", job.Order.Customer.Contact1.Fax);
            var mobile = new KeyValuePair<string, string>("Mobile:", job.Order.Customer.Contact1.Mobile);

            var jobName = new KeyValuePair<string, string>("Job:", job.Name);

            var jobType = new KeyValuePair<string, string>("Job Type:", job.Template.Name);
            var quantity = new KeyValuePair<string, string>("Quantity:", job.Quantity.ToString("N"));
			var stock = new KeyValuePair<string, string>("Stock:", job.FinalStock.Name); ;
            var cover = new KeyValuePair<string, string>("Cover Stock:",
                job.FinalStockForCover != null ? job.FinalStockForCover.Name : "");
            var frontColour = new KeyValuePair<string, string>("Front Colour:", job.FrontPrinting.ToString());
            var backColour = new KeyValuePair<string, string>("Back Colour:", job.BackPrinting.ToString());
            var frontCello = new KeyValuePair<string, string>("Front Cello:", job.FinalFrontCelloglaze.ToString());
            var backCello = new KeyValuePair<string, string>("Back Cello:", job.FinalBackCelloglaze.ToString());
            var pages = new KeyValuePair<string, string>("Pages:", job.Pages.ToString());
            var finihsedSizedName = job.FinishedSize.PaperSize.Name;
            if ("custom" == job.FinishedSize.PaperSize.Name.ToLower())
            {
                finihsedSizedName = "custom " + job.FinishedSize.Height.ToString() + "x" +
                                    job.FinishedSize.Width.ToString() + "mm";
            }
            var finishedSize = new KeyValuePair<string, string>("Finished Size:", finihsedSizedName);
            var foldeddSizedName = job.FoldedSize != null ? job.FoldedSize.PaperSize.Name : "";
            if ("custom" == foldeddSizedName.ToLower())
            {
                foldeddSizedName = "custom " + job.FoldedSize.Height.ToString() + "x" + job.FoldedSize.Width.ToString() +
                                   "mm";
            }
            var foldedSize = new KeyValuePair<string, string>("Folded Size:", foldeddSizedName);
            var magent = new KeyValuePair<string, string>("Magnet:", job.NumberOfMagnets.ToString());

            var footer = new KeyValuePair<string, string>("", "----------------- END  ---------------");

            table.Add(GenCols(header));
            table.Add(GenCols(dateSubmit));
            table.Add(GenCols(orderNr));
            table.Add(GenCols(jobNr));
            table.Add(GenCols(spaceline));

            table.Add(GenCols(customer));
            table.Add(GenCols(customerNr));
            table.Add(GenCols(createdDate));
            table.Add(GenCols(spaceline));

            table.Add(GenCols(contact));
            table.Add(GenCols(phone));
            table.Add(GenCols(fax));
            table.Add(GenCols(mobile));
            table.Add(GenCols(spaceline));

            table.Add(GenCols(jobName));
            table.Add(GenCols(jobType));
            table.Add(GenCols(quantity));
            table.Add(GenCols(stock));
            if (job.IsMagazine())
            {
                if (job.IsMagazineSeparate())
                {
                    table.Add(GenCols(cover));
                }
                table.Add(GenCols(pages));
            }
            table.Add(GenCols(frontColour));
            table.Add(GenCols(backColour));
            table.Add(GenCols(frontCello));
            table.Add(GenCols(backCello));
            table.Add(GenCols(spaceline));

            table.Add(GenCols(finishedSize));
            table.Add(GenCols(foldedSize));
            table.Add(GenCols(magent));
            table.Add(GenCols(spaceline));

            var strReader = new StringReader(job.SpecialInstructions);
            var aLine = strReader.ReadLine();
            while (aLine != null)
            {
                var commentCol = new KeyValuePair<string, string>("Special Instruction:", aLine);
                table.Add(GenCols(commentCol));
                aLine = strReader.ReadLine();
            }
            table.Add(GenCols(footer));
        }

        protected override void OnPrintPage(PrintPageEventArgs e)
        {
            base.OnPrintPage(e);

            float y = 0;
            var count = 0;
            float top = e.MarginBounds.Top;
            float right = e.MarginBounds.Right;
            float left = e.MarginBounds.Left;
            float height = e.MarginBounds.Height;
            var fontHeight = font.GetHeight(e.Graphics);
            var noOfLines = (int)Math.Floor((decimal)(height / font.GetHeight(e.Graphics)));
            while (count < noOfLines && currentLine < table.Count)
            {
                y = top + count * font.GetHeight(e.Graphics);
                var row = table[currentLine];
                float colWidth = e.MarginBounds.Width / row.Count;

                var maxRowUsed = 0;
                for (var i = 0; i < row.Count; i++)
                {
                    var rowUsed = PrintCol(row[i], i * colWidth + left, y, colWidth, e.Graphics);
                    if (rowUsed > maxRowUsed)
                    {
                        maxRowUsed = rowUsed;
                    }
                }
                count += maxRowUsed;
                currentLine++;
            }
            if (currentLine < table.Count)
            {
                e.HasMorePages = true;
            }
            else
            {
                e.HasMorePages = false;
            }
        }

        private int PrintCol(KeyValuePair<string, string> col, float x, float y, float width, Graphics graphics)
        {
            var widthNeed = graphics.MeasureString(col.Key + col.Value, font).Width;
            var valX = graphics.MeasureString(col.Key, font).Width + x;
            var count = 0;
            graphics.DrawString(col.Key, font, Brushes.Black, x, y, new StringFormat());
            if (widthNeed <= width)
            {
                graphics.DrawString(col.Value, font, Brushes.Black, valX, y, new StringFormat());
                count = 1;
            }
            else
            {
                // just rough value, char width is vary
                var charWidth = graphics.MeasureString(" ", font).Width;
                var noOfChar = (int)Math.Round((decimal)((width - valX + x) / charWidth));
                var usedChar = col.Value;

                var startChar = 0;
                while (startChar < col.Value.Length - 1)
                {
                    var len = noOfChar;
                    if (len > col.Value.Length - startChar)
                    {
                        len = col.Value.Length - startChar - 1;
                    }
                    var endIndex = LastIndexBreakCharacters(col.Value.Substring(startChar, len)) + 1;
                    if (endIndex <= 0)
                    {
                        endIndex = len + 1;
                    }
                    var text = col.Value.Substring(startChar, endIndex);
                    var lenText = graphics.MeasureString(text, font).Width;
                    var offset = 0;
                    while (lenText + valX > width)
                    {
                        endIndex = LastIndexBreakCharacters(col.Value.Substring(startChar, endIndex - 1)) + 1;
                        if (endIndex <= 0)
                        {
                            endIndex = len - offset + 1;
                        }
                        text = col.Value.Substring(startChar, endIndex);
                        lenText = graphics.MeasureString(text, font).Width;
                        offset++;
                    }

                    graphics.DrawString(text, font, Brushes.Black, valX, y, new StringFormat());
                    startChar += endIndex;
                    count++;
                    y = y + font.GetHeight(graphics);
                }
            }

            return count;
        }

        private int LastIndexBreakCharacters(string str)
        {
            var maxIndex = -1;
            var indexBreakCharacters = " /\\[];,.-=+_!#$%^&*_<>?".ToCharArray();
            for (var i = 0; i < indexBreakCharacters.Length; i++)
            {
                var currentIndex = str.LastIndexOf(indexBreakCharacters[i]);
                if (currentIndex > maxIndex)
                {
                    maxIndex = currentIndex;
                }
            }
            return maxIndex;
        }

        private IList<KeyValuePair<string, string>> GenCols(params KeyValuePair<string, string>[] args)
        {
            IList<KeyValuePair<string, string>> cols = new List<KeyValuePair<string, string>>(args);
            return cols;
        }

        private string Standardize(string message, int length)
        {
            var trimmed = message.Trim();
            var spaceNeeded = length - message.Length;
            for (var i = 0; i < spaceNeeded; i++)
            {
                trimmed += " ";
            }
            return trimmed;
        }
    }
}
