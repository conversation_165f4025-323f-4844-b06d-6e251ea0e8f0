namespace lep.freight.impl
{
    using lep.courier;
    using lep.job;

    // special data obj for intergation with freight applicator
    public class DispatchLabel : IDispatchLabel
    {
        public virtual int Id { get; set; }
        public virtual decimal Weight { get; set; }
        public virtual string CartonType { get; set; }
        public virtual CourierType CourierType { get; set; }
        public virtual Facility Facility { get; set; }
    }
}