﻿using lep.user;
using System;
using System.Collections.Generic;

namespace lep.promotion
{
	public class OfferUseRecord
    {
        public virtual int OrderId { get; set; }
        public virtual DateTime On { get; set; }
    }

    public class ListOfUsesOfOffer : List<OfferUseRecord> { }

    public class CustomerOffer
    {
        public virtual int Id { get; set; }
        public virtual IPromotion Promotion { get; set; }
        public virtual ICustomerUser Customer { get; set; }

        public virtual DateTime DateOffered { get; set; }
        public virtual DateTime DateOfferEnds { get; set; }
        public virtual DateTime? DateTakenUp { get; set; }

        public virtual bool AllowReuse { get; set; }

        public virtual DateTime DateCreated { get; set; }
        public virtual DateTime DateModified { get; set; }
        public virtual string CreatedBy { get; set; }
        public virtual string ModifiedBy { get; set; }

        public virtual ListOfUsesOfOffer OrderNumberUsedOn { get; set; } = new ListOfUsesOfOffer();
    }
}