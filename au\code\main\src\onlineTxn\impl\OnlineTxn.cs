using lep.order;
using System;

namespace lep.onlineTxn.impl
{
	public class OnlineTxn : IOnlineTxn
    {
        private string vpc_acqresponsecode;
        private string vpc_amount;
        private string vpc_authorizeid;
        private string vpc_avsresultcode;
        private string vpc_batchno;
        private string vpc_card;
        private string vpc_command;
        private string vpc_cscresultcode;
        private string vpc_merchant;
        private string vpc_merchtxnref;
        private string vpc_message;
        private string vpc_orderinfo;
        private string vpc_receiptno;
        private string vpc_transactionno;
        private string vpc_txnresponsecode;
        private string vpc_version;

        public OnlineTxn()
        {
            OlpId = 0;

            PaymentDate = DateTime.MinValue;
            vpc_avsresultcode = String.Empty;
            vpc_acqresponsecode = String.Empty;
            vpc_amount = String.Empty;
            vpc_authorizeid = String.Empty;
            vpc_batchno = String.Empty;
            vpc_cscresultcode = String.Empty;
            vpc_card = String.Empty;
            vpc_command = String.Empty;
            vpc_merchtxnref = String.Empty;
            vpc_merchant = String.Empty;
            vpc_message = String.Empty;
            vpc_orderinfo = String.Empty;
            vpc_receiptno = String.Empty;
            vpc_transactionno = String.Empty;
            vpc_txnresponsecode = String.Empty;
            vpc_version = String.Empty;
        }

        public OnlineTxn(int olpid, int orderid, DateTime paymentdate) : this()
        {
            OlpId = olpid;
            //orderid = orderid;

            PaymentDate = paymentdate;
            vpc_avsresultcode = String.Empty;
            vpc_acqresponsecode = String.Empty;
            vpc_amount = String.Empty;
            vpc_authorizeid = String.Empty;
            vpc_batchno = String.Empty;
            vpc_cscresultcode = String.Empty;
            vpc_card = String.Empty;
            vpc_command = String.Empty;
            vpc_merchtxnref = String.Empty;
            vpc_merchant = String.Empty;
            vpc_message = String.Empty;
            vpc_orderinfo = String.Empty;
            vpc_receiptno = String.Empty;
            vpc_transactionno = String.Empty;
            vpc_txnresponsecode = String.Empty;
            vpc_version = String.Empty;
        }

        public int OlpId { get; set; }

        public IOrder Order { get; set; }

        public DateTime PaymentDate { get; set; }

        public string VpcAvsResultCode
        {
            get { return vpc_avsresultcode; }

            set {
                if (value != null && value.Length > 255)
                    throw new ArgumentOutOfRangeException("Invalid value for VpcAvsResultCode", value, value.ToString());

                vpc_avsresultcode = value;
            }
        }

        public string VpcAcqResponseCode
        {
            get { return vpc_acqresponsecode; }

            set {
                if (value != null && value.Length > 255)
                    throw new ArgumentOutOfRangeException("Invalid value for VpcAcqResponseCode", value,
                        value.ToString());

                vpc_acqresponsecode = value;
            }
        }

        public string VpcAmount
        {
            get { return vpc_amount; }

            set {
                if (value != null && value.Length > 255)
                    throw new ArgumentOutOfRangeException("Invalid value for VpcAmount", value, value.ToString());

                vpc_amount = value;
            }
        }

        public string VpcAuthorizeId
        {
            get { return vpc_authorizeid; }

            set {
                if (value != null && value.Length > 255)
                    throw new ArgumentOutOfRangeException("Invalid value for VpcAuthorizeId", value, value.ToString());

                vpc_authorizeid = value;
            }
        }

        public string VpcBatchNo
        {
            get { return vpc_batchno; }

            set {
                if (value != null && value.Length > 255)
                    throw new ArgumentOutOfRangeException("Invalid value for VpcBatchNo", value, value.ToString());

                vpc_batchno = value;
            }
        }

        public string VpcCscResultCode
        {
            get { return vpc_cscresultcode; }

            set {
                if (value != null && value.Length > 255)
                    throw new ArgumentOutOfRangeException("Invalid value for VpcCscResultCode", value, value.ToString());

                vpc_cscresultcode = value;
            }
        }

        public string VpcCard
        {
            get { return vpc_card; }

            set {
                if (value != null && value.Length > 255)
                    throw new ArgumentOutOfRangeException("Invalid value for VpcCard", value, value.ToString());

                vpc_card = value;
            }
        }

        public string VpcCommand
        {
            get { return vpc_command; }

            set {
                if (value != null && value.Length > 255)
                    throw new ArgumentOutOfRangeException("Invalid value for VpcCommand", value, value.ToString());

                vpc_command = value;
            }
        }

        public string VpcMerchTxnRef
        {
            get { return vpc_merchtxnref; }

            set {
                if (value != null && value.Length > 255)
                    throw new ArgumentOutOfRangeException("Invalid value for VpcMerchTxnRef", value, value.ToString());

                vpc_merchtxnref = value;
            }
        }

        public string VpcMerchant
        {
            get { return vpc_merchant; }

            set {
                if (value != null && value.Length > 255)
                    throw new ArgumentOutOfRangeException("Invalid value for VpcMerchant", value, value.ToString());

                vpc_merchant = value;
            }
        }

        public string VpcMessage
        {
            get { return vpc_message; }

            set {
                if (value != null && value.Length > 255)
                    throw new ArgumentOutOfRangeException("Invalid value for VpcMessage", value, value.ToString());

                vpc_message = value;
            }
        }

        public string VpcOrderInfo
        {
            get { return vpc_orderinfo; }

            set {
                if (value != null && value.Length > 255)
                    throw new ArgumentOutOfRangeException("Invalid value for VpcOrderInfo", value, value.ToString());

                vpc_orderinfo = value;
            }
        }

        public string VpcReceiptNo
        {
            get { return vpc_receiptno; }

            set {
                if (value != null && value.Length > 255)
                    throw new ArgumentOutOfRangeException("Invalid value for VpcReceiptNo", value, value.ToString());

                vpc_receiptno = value;
            }
        }

        public string VpcTransactionNo
        {
            get { return vpc_transactionno; }

            set {
                if (value != null && value.Length > 255)
                    throw new ArgumentOutOfRangeException("Invalid value for VpcTransactionNo", value, value.ToString());

                vpc_transactionno = value;
            }
        }

        public string VpcTxnResponseCode
        {
            get { return vpc_txnresponsecode; }

            set {
                if (value != null && value.Length > 255)
                    throw new ArgumentOutOfRangeException("Invalid value for VpcTxnResponseCode", value,
                        value.ToString());

                vpc_txnresponsecode = value;
            }
        }

        public string VpcVersion
        {
            get { return vpc_version; }

            set {
                if (value != null && value.Length > 255)
                    throw new ArgumentOutOfRangeException("Invalid value for VpcVersion", value, value.ToString());

                vpc_version = value;
            }
        }

        //private string vpc_AVSResultCode
        //{
        //    get { return vpc_avsresultcode; }
        //    set { vpc_avsresultcode = value; }
        //}
        //private string vpc_AcqResponseCode
        //{
        //    get { return vpc_acqresponsecode; }
        //    set { vpc_acqresponsecode = value; }
        //}
        //private string vpc_Amount
        //{
        //    get { return vpc_amount; }
        //    set { vpc_amount = value; }
        //}
        //private string vpc_AuthorizeId
        //{
        //    get { return vpc_authorizeid; }
        //    set { vpc_authorizeid = value; }
        //}
        //private string vpc_BatchNo
        //{
        //    get { return vpc_batchno; }
        //    set { vpc_batchno = value; }
        //}
        //private string vpc_CSCResultCode
        //{
        //    get { return vpc_cscresultcode; }
        //    set { vpc_cscresultcode = value; }
        //}
        //private string vpc_Card
        //{
        //    get { return vpc_card; }
        //    set { vpc_card = value; }
        //}
        //private string vpc_Command
        //{
        //    get { return vpc_command; }
        //    set { vpc_command = value; }
        //}
        //private string vpc_MerchTxnRef
        //{
        //    get { return vpc_merchtxnref; }
        //    set { vpc_merchtxnref = value; }
        //}
        //private string vpc_Merchant
        //{
        //    get { return vpc_merchant; }
        //    set { vpc_merchant = value; }
        //}
        //private string vpc_Message
        //{
        //    get { return vpc_message; }
        //    set { vpc_message = value; }
        //}
        //private string vpc_OrderInfo
        //{
        //    get { return vpc_orderinfo; }
        //    set { vpc_orderinfo = value; }
        //}
        //private string vpc_ReceiptNo
        //{
        //    get { return vpc_receiptno; }
        //    set { vpc_receiptno = value; }
        //}
        //private string vpc_TransactionNo
        //{
        //    get { return vpc_transactionno; }
        //    set { vpc_transactionno = value; }
        //}
        //private string vpc_TxnResponseCode
        //{
        //    get { return vpc_txnresponsecode; }
        //    set { vpc_txnresponsecode = value; }
        //}
        //private string vpc_Version
        //{
        //    get { return vpc_version; }
        //    set { vpc_version = value; }
        //}
        public bool IsSuccessful { get; set; }
    }
}