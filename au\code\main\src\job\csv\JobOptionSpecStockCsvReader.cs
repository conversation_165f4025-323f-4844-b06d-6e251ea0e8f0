using System.Collections.Generic;

namespace lep.job.csv
{
    public class JobOptionSpecStockCsvReader : BaseCsvReader
    {
        private JobOptionSpecStockImportHandler importHandler;
        private JobOptionSpecStockVerifySchemaHandler verifyHandler;

        public JobOptionSpecStockCsvReader()
            : base()
        {
            verifyHandler = new JobOptionSpecStockVerifySchemaHandler();
            importHandler = new JobOptionSpecStockImportHandler();

            skipHandler.Handler = verifyHandler;
            verifyHandler.Handler = importHandler;
        }

        public IDictionary<int, IJobOptionSpecStock> Result { get; private set; }

        public override void StartDocument()
        {
            importHandler.JobApplication = jobApp;
            base.StartDocument();
        }

        public override void EndDocument()
        {
            Result = importHandler.Result;
            base.EndDocument();
        }
    }
}