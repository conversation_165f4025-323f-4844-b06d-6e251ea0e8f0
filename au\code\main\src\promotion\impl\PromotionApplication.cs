﻿using lep.configuration;
using lep.job;
using lep.order;
using lep.security;

using Serilog;
using NHibernate;
using NHibernate.Criterion;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;

namespace lep.promotion.impl
{
	public class PromotionApplication : BaseApplication, IPromotionApplication, IInitializingObject
	{
		// private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		#region Constants

		private const string JobPriceNotBetween = "Job price {0:0.00} does not fall between {1:0.00} and {2:0.00}";
		private const string OrderPriceNotBetween = "Order price {0:0.00} not between  {1:0.00}  and {2:0.00} ";
		private const string ValidForDays = "Valid for {0} days from {1:d}. Today is {2:d}";
		private const string ValidInBetweenCRM = "CRM states Valid between {0:d} to {1:d}. Today is {2:d}";
		private const string ValidInBetweenLEPOnline = "LEP Online states between {0:d} to {1:d}. Today is {2:d}";
		private const string OrderDoesNotHavePrice = "Order does not have a price";
		private const string PromotionCodeDoesNotExist = "Promotion Code Invalid";
		private const string PromotionIsNotActive = "Promotion is not active";
		private const string PromotionAlreadyUsedUp = "Promotion already used up";
		private const string PromitionServiceNotAvailable = "Promition service not available";
		private const string GettingOfferFail = "Getting offer fail";
		private const string CustomerNotAssignedCampaign = "Customer not assigned Campaign";
		private const string JobDoesNotHaveAPrice = "Job does not have a price";
		private const string NotCoverdInPromotion = "Not coverd in promotion ";
		private const string OfferDoesNotExistInCRM = "Offer does not exist in CRM";

		private const string NoJobsInThisOrderAreCoveredByThisPromotion =
			"No jobs in this order are covered by this promotion ";

		private const string PromotionCoversJobsInThisOrder = "Promotion covers {0} jobs in this order ";
		private const string ErrorSetUsedPromotion = "Error set used promotion";

		private const string ThisPromotionIsMeantForFirstOrderEverByACustomer =
			"This Promotion is meant for First Order ever by a customer";

		private const string BCNotAccompaniedBySelectedTemplates = "BC not accompanied by selected templates";

		#endregion Constants

		#region Constructors

 
		public PromotionApplication(ISession sf, ISecurityApplication _securityApp,
			IConfigurationApplication configurationApplication) : base(sf, _securityApp)
		{
			_configApp = configurationApplication;
		}

		//private MSCRMServiceSoapClient crm = null;

		public void AfterPropertiesSet()
		{
			//if (!String.IsNullOrEmpty( ConfigurationApplication.LepCrmWebserviceUrl )) {
			//    try {
			//        crm = new MSCRMServiceSoapClient( new BasicHttpBinding(), new EndpointAddress( ConfigurationApplication.LepCrmWebserviceUrl ) );
			//    } catch (Exception ex) {
			//        Log.Error( ex.Message );
			//    }
			//}
		}

		#endregion Constructors

		#region Properties

		public IOrderApplication OrderApplication { get; set; }

		private IConfigurationApplication _configApp { get; set; }

		#endregion Properties

		#region Public Methods

		public bool CalculatePromotion(IOrder order, StringBuilder stepsFollowed)
		{
			return CalculatePromotion(order, stepsFollowed, false);
		}

		private bool CalculatePromotion(IOrder order, StringBuilder stepsFollowed, bool ignoreOrderStatus)
		{
			if (/*!order.Enable ||*/ order.IsQuote ||
				(order.Status != OrderStatusOptions.Open && order.Status != OrderStatusOptions.Submitted &&
				 !ignoreOrderStatus))
			{
				return false;
			}

			decimal benefitAmount = 0;

			if (IsPromotionValid(order, stepsFollowed, out benefitAmount))
			{
				order.PromotionJobPriceBenefit = benefitAmount;
				return true;
			}
			else
			{
				order.Promotion = null;
				order.PromotionJobPriceBenefit = 0;
				return false;
			}
		}

		public List<lep.promotion.CustomerOffer> ValidPromotionsForCustomer(int customerId)
		{
			var now = DateTime.Now.Date;
			var offers = Session.Query<CustomerOffer>()
							.Where(x => x.Customer.Id == customerId
									 && x.DateOffered.Date <= now && x.DateOfferEnds.Date >= now
									&& ((!x.AllowReuse && x.DateTakenUp == null) || (x.AllowReuse))
							).ToList();

			return offers;
		}
		public List<lep.promotion.CustomerOffer> ValidPromotionsForCustomer(string username)
		{
			var now = DateTime.Now.Date;
			var offers = Session.Query<CustomerOffer>()
							.Where(x => x.Customer.Username == username
									 && x.DateOffered.Date <= now && x.DateOfferEnds.Date >= now
									&& ((!x.AllowReuse && x.DateTakenUp == null) || (x.AllowReuse))
							).ToList();

			return offers;

			//return new List<lep.promotion.CustomerOffer>();

			//       using (
			//           var crm = new MSCRMServiceSoapClient(new BasicHttpBinding() {
			//},
			//               new EndpointAddress(ConfigurationApplication.LepCrmWebserviceUrl))) {
			//           try {
			//               var customerOffers = crm.RetrieveCustomerOffers(username);
			//               return customerOffers;
			//           } catch (Exception ex) {
			//log.Warn("Failed to contact CRM  RetrieveCustomerOffers " + username);
			//log.Warn(ex.Message ?? "");
			//               return new List<CustomerOffer>();
			//           }
			//       }
		}

		public bool IsPromotionValid(IOrder order, StringBuilder stepsFollowed, out decimal benifitAmount)
		{
			benifitAmount = 0;

			var now = DateTime.Now.Date;

			var promo = order.Promotion;

			decimal price = 0;
			if (!order.PriceOfOnlyJobs.HasValue)
			{
				stepsFollowed.AppendLine(OrderDoesNotHavePrice);
				return false;
			}
			else
			{
				price = (decimal)order.PriceOfOnlyJobs;
			}

			if (promo == null)
			{
				stepsFollowed.AppendLine(PromotionCodeDoesNotExist);
				return false;
			}
			stepsFollowed.AppendLine(promo.ShortDescription);
			stepsFollowed.AppendLine(promo.Vaild);

			if (order.Status == OrderStatusOptions.Open)
			{
				#region Check Basic flags like Active CanUseOnce OnlyFirstOrder

				if (!promo.Active)
				{
					stepsFollowed.AppendLine(PromotionIsNotActive);
					return false;
				}

				if (promo.CanUseOnce)
				{
					if (IsPromotionUsed(order))
					{
						stepsFollowed.AppendLine(PromotionAlreadyUsedUp);
						return false;
					}
				}

				if (promo.OnlyFirstOrder)
				{
					if (OrderApplication.GetOrdersCountByCustomer(order.Customer) > 0)
					{
						stepsFollowed.AppendLine(ThisPromotionIsMeantForFirstOrderEverByACustomer);
						return false;
					}
				}

				#endregion Check Basic flags like Active CanUseOnce OnlyFirstOrder

				//if (!OrderApplication.Render(order).Contains("Rejected") &&
				//    !OrderApplication.Render(order).Contains("Requires Approval")
				//) ;

				if (!order.Jobs.Any(j => j.HasReject || j.NeedApproval))
				{
					#region Open WS and get Offer details if any

					CustomerOffer offer = null;
					try
					{
						List<CustomerOffer> customerOffers = ValidPromotionsForCustomer(order.Customer.Username); // crm.RetrieveCustomerOffers(order.Customer.Username);

						//Bug Fix: the CRM is storing time in UTC times but returning date.Kind
						if (customerOffers != null)
						{
							foreach (var co in customerOffers)
							{
								co.DateOffered = co.DateOffered.ToLocalTime().Date;
								co.DateOfferEnds = co.DateOfferEnds.ToLocalTime().Date;
							}
						}

						stepsFollowed.AppendLine("\nCRM has the following Offers for this customer");

						if (customerOffers != null)
						{
							foreach (var co in customerOffers)
							{
								stepsFollowed.AppendFormat("{0} - {1} -{2} - From {3:d} {4}",
									co.Promotion.PromotionCode, co.Customer.Name, co.Customer.Username, co.DateOffered,
									co.DateOfferEnds == DateTime.MinValue
										? ""
										: " to " + co.DateOfferEnds.ToString("d")).AppendLine();
							}

							offer =
								customerOffers.FirstOrDefault(
									custOffer => custOffer.Promotion.PromotionCode.ToLower() == promo.PromotionCode.ToLower());
							if (offer != null)
							{
								stepsFollowed.AppendLine("\nMatching Offer");
								stepsFollowed.AppendFormat("{0} - {1} -{2} - From {3:d} {4}",
									offer.Promotion.PromotionCode, offer.Customer.Name, offer.Customer.Username, offer.DateOffered,
									offer.DateOfferEnds == DateTime.MinValue
										? ""
										: " to " + offer.DateOfferEnds.ToString("d")).AppendLine();
							}
							else
							{
								stepsFollowed.AppendLine("No matching Offer found");
							}
						}
					}
					catch (Exception ex)
					{
						Log.Error(ex.Message);
						stepsFollowed.AppendLine(GettingOfferFail);
						return false;
					}

					#endregion Open WS and get Offer details if any

					stepsFollowed.AppendLine();
					stepsFollowed.AppendLine("checking dates");

					switch (promo.LifeSpan)
					{
						case PromotionLifeSpan.Absolute:
							// Walters,Rob: because an absolute date range promotion may not necessarily be available to all customers...
							// hence we will decide which promotions need to be checked against crm...
							if (promo.CheckCustomerAgainstCampaign)
							{
								// if thats required
								if (offer == null)
								{
									// then see if an offer exists
									stepsFollowed.AppendLine(CustomerNotAssignedCampaign);
									return false;
								}

								if (!(now >= offer.DateOffered))
								{
									stepsFollowed.AppendFormat("LEP CRM States only valid from {0:d}", offer.DateOffered)
										.AppendLine();
									return false;
								}

								if (offer.DateOfferEnds != DateTime.MinValue)
								{
									if (!(now <= offer.DateOfferEnds))
									{
										stepsFollowed.AppendFormat("LEP CRM States only valid till {0:d}",
											offer.DateOfferEnds).AppendLine();
										return false;
									}
								}
							}

							// for offers that didnt need check against CRM, ie offered to all customer, we just check the date range validity
							if (!(now >= promo.DateValidStart.Value.Date && now <= promo.DateValidEnd.Value.Date))
							{
								stepsFollowed.AppendFormat(ValidInBetweenLEPOnline, promo.DateValidStart,
									promo.DateValidEnd, now).AppendLine();
								return false;
							}
							break;

						case PromotionLifeSpan.Windowed:
							// for windowed promotions, we need to check agains CRM anyways as we need a offer.
							if (offer == null)
							{
								stepsFollowed.AppendLine(CustomerNotAssignedCampaign + " or " +
														 PromotionCodeDoesNotExist);
								return false;
							}

							var validTill = offer.DateOffered.AddDays(promo.Window).Date;
							if (!(now >= offer.DateOffered && now <= validTill))
							{
								stepsFollowed.AppendFormat(
									"LEP Online Date rule : Valid for only {0} days, from {1:d} to {2:d} today is {3:d}",
									promo.Window,
									offer.DateOffered,
									validTill,
									now).AppendLine();
								return false;
							}

							if (offer.DateOfferEnds != DateTime.MinValue)
							{
								if (!(now <= offer.DateOfferEnds))
								{
									stepsFollowed.AppendFormat("LEP CRM States only valid till {0:d}",
										offer.DateOfferEnds).AppendLine();
									return false;
								}
							}
							break;
					}
				}
			}

			if (price < promo.MinOrderPrice || price > promo.MaxOrderPrice)
			{
				stepsFollowed.AppendFormat(OrderPriceNotBetween, price, promo.MinOrderPrice, promo.MaxOrderPrice)
					.AppendLine();
				return false;
			}

			// sort out the most expensive BC benifit
			IJob mostExpensiveBC = null;
			decimal mostExpensiveBCjobsPrice = 0;
			decimal benifitOfBC = 0;

			if (promo.FreeBusinessCard)
			{
				#region finding the most expensive BC

				foreach (var j in order.Jobs)
				{
					// || j.Template.Id == (int) JobTypeOptions.DoubleBusinessCard
					// Free Bcard offer should only applies to standard size bcards (ie: 90x54mm and 90x55mm bcard)
					if ((j.Template.Id == (int)JobTypeOptions.BusinessCard |
						 j.Template.Id == (int)JobTypeOptions.BusinessCardSdd |
						 j.Template.Id == (int)JobTypeOptions.BusinessCardNdd) &&
						(j.FinishedSize.PaperSize.Id == 1) | (j.FinishedSize.PaperSize.Id == 40))
					{
						var jobprice = decimal.Parse(j.Price);
						var extraCostInThatBC = CostOfRoundingDrillingSizingOfBCJob(j);

						if (extraCostInThatBC > 0)
						{
							stepsFollowed.AppendFormat("Not counting {0:c} for Rounding, Drilling, Sizing ",
								extraCostInThatBC).AppendLine();
						}

						jobprice -= extraCostInThatBC;

						if (jobprice > mostExpensiveBCjobsPrice)
						{
							mostExpensiveBCjobsPrice = jobprice;
							mostExpensiveBC = j;
						}
					}
				}

				// does not contain a Standard Business card then return invalid
				if (mostExpensiveBC == null)
				{
					stepsFollowed.AppendLine("Does not have a Standard Size BC");
					return false;
				}

				#endregion finding the most expensive BC
			}

			IList<IJob> applicableJobs = new List<IJob>();

			#region Find Which Jobs match promoted job templates

			foreach (var j in order.Jobs)
			{
				stepsFollowed.AppendLine("<br/>Checking Job - " + j.JobNr + " " + j.Name);

				decimal jobprice = 0;
				if (!decimal.TryParse(j.Price, out jobprice))
				{
					stepsFollowed.AppendLine(JobDoesNotHaveAPrice);
					continue;
				}

				var thisJobFoundInPromotedProducts = promo.PromotedProducts.Count(pp => pp.JobOptionId == j.Template.Id &&
															   pp.PaperSizeId == j.FinishedSize.PaperSize.Id &&
															   pp.StockId == j.FinalStock.Id) > 0;

				var InPromotedProducts = promo.PromotedProducts.Count(pp => pp.JobOptionId == j.Template.Id &&
						  pp.PaperSizeId == j.FinishedSize.PaperSize.Id &&
						  pp.StockId == j.FinalStock.Id);

				if (!thisJobFoundInPromotedProducts)
				{
					stepsFollowed.AppendLine(NotCoverdInPromotion);
					continue;
				}

				// See if job price is greater than min job price
				if (jobprice < promo.MinJobPrice || jobprice > promo.MaxJobPrice)
				{
					stepsFollowed.AppendFormat(JobPriceNotBetween, jobprice, promo.MinJobPrice, promo.MaxJobPrice)
						.AppendLine();
					continue;
				}

				if (!promo.FreeBusinessCard)
				{
					stepsFollowed.AppendLine("Applicable to Job #" + j.JobNr + " " + j.Name);
					applicableJobs.Add(j);
				}

				if (promo.FreeBusinessCard && j != mostExpensiveBC)
				{
					stepsFollowed.AppendLine("Applicable to Job #" + j.JobNr + " " + j.Name);
					applicableJobs.Add(j);
				}
			}

			#endregion Find Which Jobs match promoted job templates

			if (promo.PromotedProducts.Count == 0) // means there were no templates specified
			{
				// If nothing within the All Templates area is selected then
				// the Free Business Card tick box should apply to any Business Card ordered
				// whether by itself or on an Order with something else.

				if (promo.FreeBusinessCard && mostExpensiveBC != null)
				{
					stepsFollowed.AppendLine("Applicable to Job #" + mostExpensiveBC.JobNr + " " + mostExpensiveBC.Name);
					applicableJobs.Add(mostExpensiveBC);
				}
			}
			else
			{
				// Some templates were selected

				if (promo.FreeBusinessCard)
				{
					//if anything within the All Templates area is selected then
					//the Free Business Card tick box applies to
					//a set of Business Cards on any Order of that type.
					if (applicableJobs.Count == 0)
					{
						stepsFollowed.AppendLine(BCNotAccompaniedBySelectedTemplates);
					}
				}

				if (applicableJobs.Count == 0)
				{
					// if some templates were selected and no job is applicable then invalid promotion
					stepsFollowed.AppendLine(NoJobsInThisOrderAreCoveredByThisPromotion);
					return false;
				}
			}

			stepsFollowed.AppendFormat(PromotionCoversJobsInThisOrder, applicableJobs.Count).AppendLine();

			if (promo.FreeBusinessCard)
			{
				#region sort out the most expensive BC benifit

				//  Grab price of 1000 of the most Expensive BC's type
				if (mostExpensiveBC != null)
				{
					if (mostExpensiveBC.Quantity > 1000)
					{
						benifitOfBC = mostExpensiveBCjobsPrice / mostExpensiveBC.Quantity * 1000;
					}
					else
					{
						benifitOfBC = mostExpensiveBCjobsPrice;
					}
				}

				benifitAmount += benifitOfBC;

				if (mostExpensiveBCjobsPrice > 0)
				{
					stepsFollowed.AppendFormat("<br/>${0:0.00} off from the BC priced {1:0.00}, (Only 1000 of this)",
						benifitOfBC, mostExpensiveBCjobsPrice);
				}

				#endregion sort out the most expensive BC benifit
			}

			if (promo.FreeDelivery && order.PackDetail.Price.HasValue)
			{
				//free freight calculate included in order.PromotionBenefit
				stepsFollowed.AppendFormat("<br/>${0:0.00} off from the Freight cost", order.PackDetail.Price);
			}

			// take some percentage off the applicable jobs if its not a free BC promo
			if (promo.Discount > 0 && !promo.FreeBusinessCard)
			{
				// the old code should be chnage so he j.price is a decimal
				decimal totalPriceOfApplicableJobs = 0;
				foreach (var j in applicableJobs)
				{
					decimal tmp = 0;
					totalPriceOfApplicableJobs += decimal.TryParse(j.Price, out tmp) ? tmp : 0;
				}

				var amountTakenOutFromApplicableJobs = totalPriceOfApplicableJobs * promo.Discount / 100;

				if (amountTakenOutFromApplicableJobs > promo.MaxDiscount)
				{
					benifitAmount += promo.MaxDiscount;
					stepsFollowed.AppendFormat("<br/>max ${0:0.##} off from applicable jobs", promo.MaxDiscount);
				}
				else
				{
					benifitAmount += amountTakenOutFromApplicableJobs;
					if (amountTakenOutFromApplicableJobs > 0)
					{
						stepsFollowed.AppendFormat("<br/>{0:0}% ie. ${1:0.00} off from applicable jobs", promo.Discount,
							amountTakenOutFromApplicableJobs);
					}
				}
			}

			if (benifitAmount > promo.MaxDeduction)
			{
				benifitAmount = promo.MaxDeduction;
				stepsFollowed.AppendFormat("<br/>max job benefit amount ${0:0.##}", promo.MaxDeduction);
			}

			return true;
		}

		public decimal CostOfRoundingDrillingSizingOfBCJob(IJob job)
		{
			var modQtyPerThousand = Math.Ceiling((decimal)(job.Quantity / 1000.0));
			var priceRound = Convert.ToDecimal(_configApp.GetValue(Configuration.PriceRoundOption));
			var priceHoleDrilling = Convert.ToDecimal(_configApp.GetValue(Configuration.PriceHoleDrilling));
			var priceSizeAdjustment =
				Convert.ToDecimal(_configApp.GetValue(Configuration.PriceBCSizeAdjustment));
			decimal extras = 0;
			decimal temp = 0;

			//Size Adjust is just for BC
			if (job.Template.Id == (int)JobTypeOptions.BusinessCard |
				job.Template.Id == (int)JobTypeOptions.DoubleBusinessCard |
				job.Template.Id == (int)JobTypeOptions.BusinessCardNdd |
				job.Template.Id == (int)JobTypeOptions.BusinessCardSdd)
			{
				if (job.RoundOption != RoundOption.None)
				{
					temp = modQtyPerThousand * priceRound;
					extras += temp;
				}

				if (job.HoleDrilling != HoleDrilling.None)
				{
					temp = modQtyPerThousand * priceHoleDrilling;
					extras += temp;
				}

				var sizeAdjust = false;
				if (job.FinishedSize.PaperSize.Name == "Custom")
				{
					var higher = Math.Max(job.FinishedSize.Height, job.FinishedSize.Width);
					var lower = Math.Min(job.FinishedSize.Height, job.FinishedSize.Width);
					if (higher <= 90 && higher >= 30 && lower >= 30 && lower <= 54)
					{
						sizeAdjust = true;
					}
				}

				if (sizeAdjust)
				{
					temp = modQtyPerThousand * priceSizeAdjustment;
					extras += temp;
				}
			}

			return extras;
		}

		public bool ApplyPromotionToOrder(string promotionCode, IOrder order, StringBuilder stepsFollowed,
			bool ignoreOrderStatus)
		{
			order.Promotion = GetPromotion(promotionCode);

			var applied = CalculatePromotion(order, stepsFollowed, ignoreOrderStatus);
			//should not use orderapp.save(order) to avoid duplicate calculation, this is due to incorrect initial design
			base.Save<IOrder>(order);

			return applied;
		}

		public void UpdateUsedPromotion(IOrder order)
		{
			if (order.Promotion == null)
				return;
			var now = DateTime.Now.Date;
			var username = order.Customer.Username;
			var promocode = order.Promotion.PromotionCode;

			var offer = Session.Query<lep.promotion.CustomerOffer>()
					.Where(x => x.Customer.Username == username
							 && x.Promotion.PromotionCode == promocode
							 && x.DateOffered.Date <= now && x.DateOfferEnds.Date >= now
							&& ((!x.AllowReuse && x.DateTakenUp == null) || (x.AllowReuse))
					).OrderBy(x => x.Id).FirstOrDefault();

			if (offer == null)
			{
				Log.Information("cant find offer for {promocode}  for customer {username} to mark as used up");
				return;
			}

			offer.DateTakenUp = now;

			if (offer.OrderNumberUsedOn == null)
			{
				offer.OrderNumberUsedOn = new ListOfUsesOfOffer();
			}

			offer.OrderNumberUsedOn.Add(new OfferUseRecord { OrderId = order.Id, On = now });
			Save(offer);

			//using (
			//	var crm = new MSCRMServiceSoapClient(new BasicHttpBinding(),
			//		new EndpointAddress(ConfigurationApplication.LepCrmWebserviceUrl))) {
			//	try {
			//		crm.PromoUsed(order.Customer.Username, order.Promotion.PromotionCode);
			//	} catch (Exception ex) {
			//		Log.Error(String.Format("error setup used promotion: username {0} promocode {1}",
			//			order.Customer.Username, order.Promotion.PromotionCode));
			//		Log.Error(ex.Message);
			//	}
			//}
		}

		/// <summary>
		/// Copies a given promotion along with its promoted products into a new promotion.
		/// </summary>
		/// <param name="promotion">The srource promotion</param>
		/// <returns>Dest promotion</returns>
		public IPromotion DuplicatePromotion(IPromotion promotion)
		{
			// create a new promotion
			IPromotion newPromotion = new Promotion(promotion);
			newPromotion.PromotionCode = String.Format("{0} {1}", promotion.PromotionCode, DateTime.Now.Ticks);
			Save(newPromotion);

			// copy all the promoted product to new one
			foreach (var pp in promotion.PromotedProducts)
			{
				var newPromotedProduct = new PromotedProduct(pp);
				newPromotedProduct.PromotionId = newPromotion.Id;
				newPromotion.PromotedProducts.Add(newPromotedProduct);
			}
			Save(newPromotion);
			return newPromotion;
		}

		public IPromotion GetPromotion(string promoCode)
		{
			IPromotion p = null;

			var ps = Session.CreateCriteria(typeof(IPromotion))
				.Add(Restrictions.Eq("PromotionCode", promoCode))
				.List<IPromotion>();

			if (ps.Count > 0)
			{
				p = ps[0];
			}

			return p;
		}

		public List<PromotionInfo> GetPromotionInfo()
		{
			var list = Session.CreateCriteria(typeof(IPromotion), "p")
				.AddOrder(Order.Desc("p.Id"))
				.List<IPromotion>();

			var r = (from p in list
					 select new PromotionInfo()
					 {
						 Id = p.Id,
						 PromotionCode = p.PromotionCode,
						 Active = p.Active,
						 ShortDescription = p.ShortDescription,
						 LongDescription = p.LongDescription,
						 SalesDescription = p.SalesDescription,
						 Discount = p.Discount,
						 MaxJobPrice = p.MaxJobPrice,
						 MinJobPrice = p.MinJobPrice,
						 MaxOrderPrice = p.MaxOrderPrice,
						 MinOrderPrice = p.MinOrderPrice,
						 MinJobQuantity = p.MinJobQuantity,
						 LifeSpan = p.LifeSpan,
						 DateValidStart = p.DateValidStart,
						 DateValidEnd = p.DateValidEnd,
						 Window = p.Window,
						 CanUseOnce = p.CanUseOnce,
						 FreeBusinessCard = p.FreeBusinessCard,
						 FreeDelivery = p.FreeDelivery,
						 OnlyFirstOrder = p.OnlyFirstOrder,
						 OnlyValidInCampaign = p.OnlyValidInCampaign,
						 CheckCustomerAgainstCampaign = p.CheckCustomerAgainstCampaign,
						 SalesCategoryLead = p.SalesCategoryLead,
						 SalesCategoryLapsed = p.SalesCategoryLapsed,
						 SalesCategoryProspect = p.SalesCategoryProspect,
						 SalesCategoryCustomer = p.SalesCategoryCustomer,
						 DateCreated = p.DateCreated,
						 DateModified = p.DateModified
					 }).ToList();

			return r;
		}

		public IPromotion GetPromotion(int promotionId)
		{
			return Get<IPromotion>(promotionId);
		}

		public ICriteria GetPromotionsCriteria(string freeText)
		{
			var criteria = Session.CreateCriteria(typeof(IPromotion), "p");

			if (!string.IsNullOrEmpty(freeText))
			{
				criteria.Add(Restrictions.Disjunction()
					.Add(Restrictions.Like("p.PromotionCode", freeText, MatchMode.Anywhere))
					.Add(Restrictions.Like("p.SalesDescription", freeText, MatchMode.Anywhere))
					.Add(Restrictions.Like("p.ShortDescription", freeText, MatchMode.Anywhere))
					.Add(Restrictions.Like("p.LongDescription", freeText, MatchMode.Anywhere))
					);
			}

			return criteria;
		}

		public IPromotion NewPromotion()
		{
			//AssertPermission( "promotion.create" );
			var p = new Promotion();
			return p;
		}

		private bool IsPromotionUsed(IOrder order)
		{
			return Convert.ToInt32(Session.CreateCriteria(typeof(IOrder))
					   .Add(Restrictions.Eq("Customer.Id", order.Customer.Id))
					   .Add(Restrictions.Eq("Promotion.Id", order.Promotion.Id))
					   .Add(Restrictions.Not(Restrictions.Eq("Id", order.Id)))
					   .SetProjection(Projections.RowCount())
					   .UniqueResult()) > 0;
		}

		public int PromoCodeCount(string p)
		{
			var count = (Int32)Session.CreateCriteria(typeof(IPromotion))
				.Add(Restrictions.Eq("PromotionCode", p))
				.SetProjection(Projections.RowCount())
				.UniqueResult();
			return count;
		}

		public void Save(IPromotion p)
		{
			base.Save<IPromotion>(p);
		}

		#endregion Public Methods
	}
}
