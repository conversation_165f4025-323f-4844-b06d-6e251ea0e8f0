using System.Text.RegularExpressions;

namespace lep.job.csv
{
    public class JobOptionVerifySchemaHandler : BaseVerifySchemaHandler
    {
        public JobOptionVerifySchemaHandler()
            : base()
        {
            fileType = "Spec Type File ";
            headers = new string[]
            {
                "print-type", "job-type", "size", "stock", "fid", "rid", "pid", /*"magnet", */"min-magnet"
			};
            regexs = new Regex[]
            {
                new Regex(@"^(D||O||W||N)$"), new Regex(@"^(\w+)||(\d+)$"), new Regex(@"^(\w+)||(\d+)$"),
                new Regex(@"^(\w+)||(\d+)$"), new Regex(@"^(\d+)||\-$"), new Regex(@"^(\d+)||\-$"),
                new Regex(@"^(\d+)||\-$"), /*new Regex(@"^(Y||N)$"),*/new Regex(@"^(\d+)||\-$")
			};
        }
    }
}