using lep.job.impl;

using Serilog;
using lumen.csv;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;

namespace lep.job.csv
{
	public class JobOptionImportHandler : BaseHandler
	{
		//private const string StockScriptFormat =
		//	"jobOptionSpecStock.push( {{ printType: \"{1}\", name: \"{2}\", \t\t\t id: {3}, jobtype: {4}, papersize: {5}, rid: {6}, pid: {7}, fid: '{8}', magnet:{9} }} );";

		// private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		private Dictionary<string, int> fieldMap = new Dictionary<string, int>();

		private IDictionary<KeyValuePair<string, string>, IJobOptionSpecSize> result =
			new Dictionary<KeyValuePair<string, string>, IJobOptionSpecSize>();

		private int rowNumber;
		private bool seenFirstRow;

		public int NumRowAdded => result.Count;

		public JobApplication _jobApp { get; set; }

		public IDictionary<int, IJobOptionSpecStock> SpecPrint { get; set; }

		public IDictionary<int, IJobOptionSpecQuantity> SpecRange { get; set; }

		public IDictionary<int, IList<IPaperSize>> SpecFolds { get; set; }

		public IList<IJobTemplate> Templates { get; set; }

		public StringBuilder CacheScript { get; set; }

		//public void CreateOptionScript()
		//{
		//	return;
		//	CacheScript.AppendLine("var jobOptionSpecStock = new Array();");

		//	var tmpSb = new StringBuilder();
		//	foreach (var key in SpecFolds.Keys)
		//	{
		//		if (tmpSb.Length > 0)
		//		{
		//			tmpSb.Append(", ");
		//		}
		//		var sizelist = String.Join(",", SpecFolds[key].Select(d => "'" + d.Id.ToString() + "'").ToArray());
		//		tmpSb.AppendLine(String.Format(" {0} : [{1}]", key, sizelist));
		//	}
		//	//custom folding
		//	tmpSb.AppendLine(String.Format(", 9999 : [{0}]",
		//		String.Join(",", JobApplication.ListPaperSize().Select(s => "'" + s.Id.ToString() + "'").ToArray())));

		//	tmpSb.Append(" };");
		//	CacheScript.Append("var SpecFold = { ");
		//	CacheScript.AppendLine(tmpSb.ToString());

		//	tmpSb = new StringBuilder();
		//	foreach (var key in SpecRange.Keys)
		//	{
		//		if (tmpSb.Length > 0)
		//		{
		//			tmpSb.Append(", ");
		//		}
		//		tmpSb.AppendLine(
		//			String.Format(" {0} : {{ minimum: {1}, step1: {2}, change: {3}, step2: {4}, change2: {5} }}", key,
		//				SpecRange[key].Minium, SpecRange[key].Step1, SpecRange[key].Change, SpecRange[key].Step2,
		//				SpecRange[key].Change2));
		//	}
		//	tmpSb.Append(" };");
		//	CacheScript.Append("var SpecRange = { ");
		//	CacheScript.AppendLine(tmpSb.ToString());

		//	tmpSb = new StringBuilder();
		//	foreach (var key in SpecPrint.Keys)
		//	{
		//		if (tmpSb.Length > 0)
		//		{
		//			tmpSb.Append(", ");
		//		}
		//		tmpSb.AppendLine(String.Format(" {0} : {{ front: [{1}], back: [{2}], cello: [{3}] }}", key,
		//			RenderColorOption(SpecPrint[key].FrontPrintOptions),
		//			RenderColorOption(SpecPrint[key].BackPrintOptions), RenderCelloOption(SpecPrint[key].CelloOptions)));
		//	}

		//	//custom print
		//	tmpSb.AppendLine(
		//		@",  9999 : { front: [ { id: 'Unprinted', name: 'Unprinted' }, { id: 'Colour', name: 'Colour' }, { id: 'BW', name: 'Black & White' }], back: [ { id: 'Unprinted', name: 'Unprinted' }, { id: 'Colour', name: 'Colour' }, { id: 'BW', name: 'Black & White' }], cello: [ { id: 'NN', name: 'None', presentation_name: 'None' }, { id: 'GN', name: 'Gloss front only', presentation_name: 'Gloss Outside only' }, { id: 'MN', name: 'Matt front only', presentation_name: 'Matt Outside only' }, { id: 'GG', name: 'Gloss front & back', presentation_name: 'Gloss Outside & Inside' }, { id: 'MM', name: 'Matt front & back', presentation_name: 'Matt Outside & Inside' }] }");

		//	tmpSb.Append(" };");
		//	CacheScript.Append("var SpecPrint = { ");
		//	CacheScript.AppendLine(tmpSb.ToString());
		//}

		//private string RenderColorOption(IList<JobPrintOptions> options)
		//{
		//	var sep = " ";
		//	var sb = new StringBuilder();
		//	foreach (var op in options)
		//	{
		//		sb.Append(sep)
		//			.Append("{ id: '").Append(op.ToString())
		//			.Append("', name: '").Append(op.ToDescription())
		//			.Append("' }");
		//		sep = ", ";
		//	}
		//	return sb.ToString();
		//}

		//private string RenderCelloOption(IList<CelloOption> options)
		//{
		//	options = _jobApp.SortCelloOption(options);

		//	var sep = " ";
		//	var sb = new StringBuilder();
		//	foreach (var op in options)
		//	{
		//		sb.Append(sep)
		//			.Append("{ id: '")
		//			.Append(op.CelloFront.ToString()[0].ToString() + op.CelloBack.ToString()[0].ToString())
		//			.Append("', name: '").Append(CelloUtils.GetCelloName(op.CelloFront, op.CelloBack, true))
		//			.Append("', presentation_name: '").Append(CelloUtils.GetCelloName(op.CelloFront, op.CelloBack, false))
		//			.Append("' }");
		//		sep = ", ";
		//	}
		//	return sb.ToString();
		//}

		public override void RowData(string[] values)
		{
			if (!seenFirstRow)
			{
				seenFirstRow = true;

				//Map column names to column numbers
				fieldMap = new Dictionary<string, int>(values.Length);
				for (var i = 0; i < values.Length; i++)
				{
					fieldMap[values[i].ToLower()] = i;
				}

				// CreateOptionScript();
			}
			else
			{
				rowNumber++;
				try
				{
					//IJobTemplate template = new JobTemplate();

					var csvJobType = values[fieldMap["job-type"]];
					var csvSize = values[fieldMap["size"]];

					var pt = values[fieldMap["print-type"]];
					var csvPrintType = PrintType.O;
					if (pt == "D") csvPrintType = PrintType.D;
					else if (pt == "O") csvPrintType = PrintType.O;
					else if (pt == "W") csvPrintType = PrintType.W;
					else if (pt == "N") csvPrintType = PrintType.N;

					var csvStock = values[fieldMap["stock"]];

					var template = Templates.FirstOrDefault(tmp => tmp.Name == csvJobType);

					var jobOptionSpecSize = _jobApp.ListSizeOptions(template)  .FirstOrDefault(size => size.PaperSize.Name == csvSize);

					if (jobOptionSpecSize == null)
					{
						jobOptionSpecSize = new JobOptionSpecSize()
						{
							PaperSize = _jobApp.GetPaperSize(csvSize),
							JobTemplate = template
						};
						_jobApp.Save(jobOptionSpecSize);
						
						//template.SizeOptions.Add(jobOptionSpecSize);
					}

					var jobOptionSpecStock = jobOptionSpecSize.StockOptions
						.FirstOrDefault(stock => stock.Stock.Name == csvStock && stock.PrintType == csvPrintType);

					if (jobOptionSpecStock == null)
					{
						var stock = _jobApp.GetStock(csvStock);
						var mm = 0;
						if (values[fieldMap["min-magnet"]] != "-")
							mm = int.Parse(values[fieldMap["min-magnet"]]);

						jobOptionSpecStock = new JobOptionSpecStock()
						{
							//Magnet = values[fieldMap["magnet"]] == "Y",
							MinMagnet = mm,
							Stock = stock,
							JobOptionSpecSize = jobOptionSpecSize,
							PrintType = csvPrintType,
						};

						jobOptionSpecSize.StockOptions.Add(jobOptionSpecStock);
					}

					if ("-" != values[fieldMap["fid"]])
					{
						var fid = Int32.Parse(values[fieldMap["fid"]]);
						var folds = SpecFolds[fid];

						folds.ForEach(paperSize => jobOptionSpecStock.FoldOptions.Add(paperSize));
					}

					if ("-" != values[fieldMap["rid"]])
					{
						var rid = Int32.Parse(values[fieldMap["rid"]]);
						jobOptionSpecStock.QuantityOption = SpecRange[rid];
					}

					if ("-" != values[fieldMap["pid"]])
					{
						var pid = Int32.Parse(values[fieldMap["pid"]]);
						var specPrintObj = SpecPrint[pid];
						jobOptionSpecStock.FrontPrintOptions = specPrintObj.FrontPrintOptions;
						jobOptionSpecStock.BackPrintOptions = specPrintObj.BackPrintOptions;

						specPrintObj.CelloOptions.ForEach(op => jobOptionSpecStock.CelloOptions.Add(new CelloOption()
						{
							CelloFront = op.CelloFront,
							CelloBack = op.CelloBack,
							JobOptionSpecStock = jobOptionSpecStock
						}));
					}

					//CacheScript.AppendLine(String.Format(StockScriptFormat,
					//    0,
					//    jobOptionSpecStock.PrintType.ToString(),
					//    jobOptionSpecStock.Stock.Name,
					//    jobOptionSpecStock.Stock.Id,
					//    template.Id,
					//    jobOptionSpecStock.JobOptionSpecSize.PaperSize.Id,
					//    values[fieldMap["rid"]],
					//    values[fieldMap["pid"]],
					//    values[fieldMap["fid"]],
					//    jobOptionSpecStock.Magnet.ToString().ToLower()
					//));
				}
				catch (Exception ex)
				{
					//Wrap the exception in another that gives the line number
					var s = string.Format("Invalid value in row {0} : {1}", rowNumber, ex.Message);
					Log.Error(s, ex);
					//throw new CsvParseException(s);
					//throw ex;
				}
			}

			base.RowData(values);
		}

		public override void EndDocument()
		{
			base.EndDocument();
			return;
			//// SA: TODO understand why the following bit exists

			////add custom type stock/size combination, use papersize id =0 for rendering dropdown, otherwise the stock/size combination generate a lot duplicate data in cache file
			//foreach (var stock in JobApplication.ListStock())
			//{
			//	CacheScript.AppendLine(String.Format(StockScriptFormat,
			//		0,
			//		"D",
			//		stock.Name,
			//		stock.Id,
			//		Convert.ToInt32(JobTypeOptions.Custom),
			//		0,
			//		0,
			//		9999,
			//		9999,
			//		"true"
			//	));
			//}
		}
	}
}
