namespace lep.courier.impl
{
	using System;
	using System.Text;

	public class StarTrackRate
	{
		public virtual int Id { get; set; }
		public virtual string AccountNumber { get; set; }
		public virtual string ServiceCode { get; set; }
		public virtual string OriginZone { get; set; }
		public virtual string DestinationZone { get; set; }

		public virtual decimal BasicCharge { get; set; }
		public virtual decimal BreakRate { get; set; }
		public virtual decimal CubicConvFactor { get; set; }
		public virtual int Quantity { get; set; }
	}
}
