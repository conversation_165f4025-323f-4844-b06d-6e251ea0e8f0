using lep.job;
using lep.order;
using lep.run;
using lep.security;
using NHibernate;
using NHibernate.Criterion;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using lep.despatch.impl.label;

namespace lep.configuration.impl
{
	/// <summary>
	///
	/// </summary>
	public class ConfigurationApplication : BaseApplication, IConfigurationApplication
	{
		public ConfigurationApplication(ISession sf, ISecurityApplication securityApp) : base(sf, securityApp)
		{
		}


		public string GetValue(Configuration name)
		{
			var cv = Get<ConfigurationValue>(name);
			return cv == null ? null : cv.Value;
		}

		public void SetValue(Configuration name, string value)
		{
			//AssertPermission("configuration.update");
			var cv = Get<ConfigurationValue>(name);
			if (cv == null)
			{
				cv = new ConfigurationValue();
				cv.Code = name;
			}
			cv.Value = value;

			Save<ConfigurationValue>(cv);
		}



		//public DirectoryInfo ArtworkDirectoryDigitalHotFolder()
		//{
		//	var indigo = new DirectoryInfo($"{GetValue(Configuration.DigitalHPIndigoHotFolder)}\\");
		//	// change as per your requiremeents of get from config
		//	return indigo;
		//}


	}
}
