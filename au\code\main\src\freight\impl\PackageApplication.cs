using lep.configuration;
using lep.extensionmethods;
using lep.job;
using lep.order;
using lep.security;
using lep.src.freight.impl;
using lep.run;
using Serilog;
using NHibernate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using static lep.freight.impl.CartonCode;
using static lep.job.CutOptions;
using static lep.job.JobTypeOptions;
using static lep.job.JobCelloglazeOptions;
using static lep.src.freight.impl.Packager;

using System.Diagnostics;
using System.Runtime.CompilerServices;
using System.Text;


namespace lep.freight.impl
{
	/// <summary>
	///
	/// </summary>
	public class PackageApplication : BaseApplication, IPackageApplication
	{
		// private static readonly ILog log = 
		// LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);
		private IJobApplication _jobApp;

		private IConfigurationApplication _configApp;

		private const int A4SizeCartonVolume = 172 * 230 * 321;
		private const int BCStStdCartonVolumne = 72 * 200 * 305;
		private const int MinimumLevel10Number = 4;
		private const int MinimumLevel9Number = 2;
		public const int PresentationFolders = 3;

		private const int A3Turduken = 9;
		private const int Skids = 10;

		private const decimal PresentationFolderWeightMultiplier = 2.7m;
		private const decimal MagnetPaperWeightMultiplier = 0.0015m;
		private const int MaxWeightPerCarton = 22;
		private const int MaxWeightPerCartonWithoutPallet = 11;
		private const string brownPaperWrap = "BrownPaperWrap";
		private const string NONE = "None";
		private const string MATT = "Matt";
		private const string GLOSS = "Gloss";
		private const string VELVET = "Velvet";
		private const string MATTAntiScuff = "MattAntiScuff";

		public string PadStockName { get; set; } = "578 GSM P/Back";

		public bool LORD1254 { get; set; } = true;

		FoldFactorFinder _foldFactorFinder;
		CartonFinder _cartonFinder;
		private dynamic _wiroValues;

		public PackageApplication(
			ISession sf,
			ISecurityApplication _securityApp,
			IConfigurationApplication configApp,
			IJobApplication jobApp,
			FoldFactorFinder foldFactorFinder,
			CartonFinder cartonFinder,
			WiroMagazineValues wiroValues
			) : base(sf, _securityApp)
		{
			_configApp = configApp;
			_jobApp = jobApp;
			_foldFactorFinder = foldFactorFinder;
			_cartonFinder = cartonFinder;
			_wiroValues = wiroValues.GetWiroMagazineValues();
		}

		public bool IsSkidPossible(IOrder order)
		{

			if (!order.Jobs.Any() || !AllowSkidDelivery(order))
				return false;

			var fgPackages1 = GetPackage(order, Facility.FG);
			if (fgPackages1.Any())
			{
				var packsThatCanGetPackedFuther2 = PackItems(fgPackages1, true);
				var packagesFinal = new List<Package>();
				packagesFinal.AddRange(packsThatCanGetPackedFuther2);

				if (packagesFinal.Any(p => p.Carton.Contains("Skid")))
					return true;

			}

			var pmPackages1 = GetPackage(order, Facility.PM);
			if (pmPackages1.Any())
			{
				var packsThatCanGetPackedFuther2 = PackItems(pmPackages1, true);
				var packagesFinal = new List<Package>();
				packagesFinal.AddRange(packsThatCanGetPackedFuther2);

				if (packagesFinal.Any(p => p.Carton.Contains("Skid")))
					return true;
			}

			return false;
		}

		public void SetPackage(IOrder order)
		{
			if (!order.Jobs.Any())
				return;

			try
			{
				if (order.PackDetail == null)
				{
					order.PackDetail = new PackDetail();
				}

				var allowSkidDelivery = AllowSkidDelivery(order);

				if (order.PackWithoutPallets)
					allowSkidDelivery = false;


				var fgPackages1 = GetPackage(order, Facility.FG);
				if (fgPackages1.Any())
				{
					//var fgPackages2 = PackItems(fgPackages1, allowSkidDelivery);
					//order.PackDetail.SetPackages(fgPackages2, Facility.FG);

					//var packsThatCantGetPackedFuther = fgPackages1.Where(_ => _.StopPackingFurther).ToList();
					//var packsThatCanGetPackedFuther = fgPackages1.Where(_ => !_.StopPackingFurther).ToList();

					var packsThatCanGetPackedFuther2 = PackItems(fgPackages1, allowSkidDelivery);

					var packagesFinal = new List<Package>();
					//packagesFinal.AddRange(packsThatCantGetPackedFuther);
					packagesFinal.AddRange(packsThatCanGetPackedFuther2);

					order.PackDetail.SetPackages(packagesFinal, Facility.FG);
				}
				else
				{
					order.PackDetail.SetPackages(null, Facility.FG);
				}

				var pmPackages1 = GetPackage(order, Facility.PM);
				if (pmPackages1.Any())
				{
					//var packsThatCantGetPackedFuther = pmPackages1.Where(_ => _.StopPackingFurther).ToList();
					//var packsThatCanGetPackedFuther = pmPackages1.Where(_ => !_.StopPackingFurther).ToList() ;
					var packsThatCanGetPackedFuther2 = PackItems(pmPackages1, allowSkidDelivery);

					var packagesFinal = new List<Package>();
					//packagesFinal.AddRange(packsThatCantGetPackedFuther);
					packagesFinal.AddRange(packsThatCanGetPackedFuther2);

					order.PackDetail.SetPackages(packagesFinal, Facility.PM);
				}
				else
				{
					order.PackDetail.SetPackages(null, Facility.PM);
				}

				order.SetDispatchLabels();
			}
			catch (Exception ex)
			{
				Log.Error(ex.Message, ex);
			}
		}

		public bool AllowSkidDelivery(IOrder order)
		{
			//if (!order.Courier.IsNone && (order.Courier.IsFastWay || order.Courier.IsCourierPleaseVic))
			//	return false;

			if ((order.DeliveryAddress?.Address1 ?? "").ToUpper().Contains("POBOX"))
				return false;

			return true;
		}

		// retruns orders packages by facility
		public IList<Package> GetPackage(IOrder order, Facility? facility)
		{

			var x = order.Jobs.OrderBy(_ => _.Id)
				.Where(j => (!facility.HasValue || j.Facility == facility) && (j.Freight?.Packages?.Count > 0))
				.SelectMany(j => j.Freight.Packages).ToList();
			return x;
		}

		public string SimplifiedCello(IJob job)
		{
			var x = new List<string>();

			string frontCello = job.FinalFrontCelloglaze.ToString();
			if (frontCello.Contains(NONE))
				x.Add(NONE);
			else if (frontCello.Contains(MATT))
				x.Add(MATT);
			else if (frontCello.Contains(GLOSS))
				x.Add(GLOSS);
			else if (frontCello.Contains(VELVET))
				x.Add(VELVET);
			else if (frontCello.Contains(MATTAntiScuff))
				x.Add(MATTAntiScuff);

			string backCello = job.FinalBackCelloglaze.ToString();
			if (backCello.Contains(NONE))
				x.Add(NONE);
			else if (backCello.Contains(MATT))
				x.Add(MATT);
			else if (backCello.Contains(GLOSS))
				x.Add(GLOSS);
			else if (backCello.Contains(VELVET))
				x.Add(VELVET);
			else if (backCello.Contains(MATTAntiScuff))
				x.Add(MATTAntiScuff);

			return String.Join("/", x.ToArray());

		}



		public SingleJobDetails GetSingleJobThicknessWeight(IJob j)
		{
			decimal width, height, thickness, weight;
			string finishedSize;

			thickness = 0m;
			weight = 0m;

			finishedSize = j.FinishedSize?.PaperSize?.Name ?? "";
			var foldedSize = "";
			//supplied flat
			if (!j.IsBusinessCard() && !j.Template.Is(Postcard, GolfScoreCards))
			{
				foldedSize = j.FoldedSize?.PaperSize?.Name ?? "";
				if (foldedSize == "Custom")
				{
					var candts = _jobApp.ListPaperSize()
						.Where(_ => _.Size.CanFit(j.FoldedSize.Width, j.FoldedSize.Height)).ToList();
					var exCustom = candts
						.OrderBy(_ => _.Name.Length).FirstOrDefault();
					if (exCustom != null)
					{
						foldedSize = exCustom.Name;
					}
				}
			}

			var foldedSize2 = foldedSize;
			foldedSize2 = foldedSize2.Replace(" (Crash Folded)", "");
			var foldFactor = _foldFactorFinder.Get(j.FinalStock.GSM, finishedSize, foldedSize2);
			var x = foldFactor;
			// if (finishedSize == "Custom" || foldedSize == "Custom") ;

			height = j.FinishedSize?.Height ?? 0m;
			width = j.FinishedSize?.Width ?? 0m;

			if (width == 0 || height == 0)
			{
				height = j.FinishedSize?.PaperSize?.Size.Height ?? 0m;
				width = j.FinishedSize?.PaperSize?.Size.Width ?? 0m;
			}

			// if there is a fold then foldFactor will be > 1
			// in that case we pack folded
			if (foldFactor > 1 || foldedSize != "")
			{
				finishedSize = foldedSize;
				height = j.FoldedSize?.Height ?? 0m;
				width = j.FoldedSize?.Width ?? 0m;
				finishedSize = foldedSize;
			}

			var nameStock = j.FinalStock.Name;
			var gsm = j.FinalStock.GSM;

			var paperThickness = (decimal)j.FinalStock.Thickness;

			if (paperThickness == 0)
			{
				paperThickness = 0.09m;
			}

			// start off with these

			// get number of printed paper
			var qtyPrintedPaper = 1;
			var pagesPerJob = 1;
			if (j.Template.HasMultiplePages() && j.IsMagazine())
			{
				pagesPerJob = j.Pages / 2;
				if (pagesPerJob == 0)
					pagesPerJob = 1;
				qtyPrintedPaper = qtyPrintedPaper * pagesPerJob;
			}

			else if (j.Template.HasMultiplePages())
			{
				pagesPerJob = j.Pages;
				if (pagesPerJob == 0)
					pagesPerJob = 1;
				qtyPrintedPaper = qtyPrintedPaper * pagesPerJob;
			}


			thickness = paperThickness * pagesPerJob * foldFactor;

			if (thickness == 0.00m)
			{
				throw new Exception("Thickness cant be 0");
			}

			if (LORD1254)
				if (j.Template.Is(BusinessCard, BusinessCardNdd, BusinessCardSdd)
					&& _cartonFinder.Has500BCThicknessDataFor(j.FinalStock.Name))
				{
					var gsmS = j.FinalStock.GSM;
					var celloS = SimplifiedCello(j);
					decimal thicknes500 = _cartonFinder.Get500BCThickness(j.FinalStock.Name, celloS);
					thickness = thicknes500 / 500.0m;
				}


			//if (j.IsMagazine())
			//{
			//	qtyPrintedPaper = j.Pages / 2;
			//	thickness =  paperThickness * qtyPrintedPaper;
			//}

			if (j.IsMagazineSeparate())
			{
				qtyPrintedPaper += 1;
			}

			// get base weight
			weight = GetPaperWeight(j.FinalStock, j.FinishedSize, qtyPrintedPaper);

			if (j.Template.Is(Notepads))
			{
				//increase weight and thickness  by considering back of the notepad
				var padStock = _jobApp.GetStock(PadStockName);
				weight += GetPaperWeight(padStock, j.FinishedSize, 1);
				thickness += padStock.Thickness;
			}

			if (j.IsEnvelope())
			{
				weight *= 2;
				thickness *= 2;
			}

			if (j.Template.Is(PresentationFolder, PresentationFolderNDD))
			{
				// for presentation folder multiply by 2.7
				weight *= PresentationFolderWeightMultiplier;
				//thickness *= PresentationFolderWeightMultiplier;
			}

			if (j.IsMagazineSeparate())
			{
				// for magazine separate add cover weight and thickness
				var covertStock = _jobApp.GetStock(j.StockForCover.Name);
				weight += GetPaperWeight(covertStock, j.FinishedSize, 1);
				thickness += covertStock.Thickness * 2;
			}

			if (j.NumberOfMagnets > 0)
			{
				//magnet can't attach to magazine
				//var magnetWeight = 0.0015m; // rev
				var magnetWeight = 0.00175m;
				weight += (magnetWeight * j.NumberOfMagnets);
				thickness += 0.7m - 0.351m;// - 0.087m;
			}

			if (j.Template.Is(DuplicateNCRBooks, TriplicateNCRBooks, QuadruplicateNCRBooks))
			{
				// these have multiple page + a writing mat
				var multiplier = 1;
				if (j.Template.Is(DuplicateNCRBooks)) multiplier = 2;
				else if (j.Template.Is(TriplicateNCRBooks)) multiplier = 3;
				else if (j.Template.Is(QuadruplicateNCRBooks)) multiplier = 4;
				thickness *= multiplier;
				weight *= multiplier;

				var padBack = _jobApp.GetStock("300 GSM Uncoated");
				thickness += padBack.Thickness;
				weight += GetPaperWeight(padBack, j.FinishedSize, 1);
			}

			if (j.Template.Is(PullUpBannerStandardStand, PullUpBannerPremiumStand))
			{
				var name = j.FinishedSize?.PaperSize?.Name ?? "";
				if (name.Contains("Small"))
				{
					weight += 6;
				}
				else if (name.Contains("Medium"))
				{
					weight += 8;
				}
				else if (name.Contains("Large"))
				{
					weight += 10;
				}
			}


			// if wiro magazine, cater for innner and out cover thickness and weight
			if (j.Template.Is(WiroMagazines) && j.WiroInfo != null)
			{
				var w = j.WiroInfo;
				if (w.OuterFront != null)
				{
					var outerCover = _wiroValues.OuterCover[w.OuterFront];
					if (outerCover != null)
					{
						thickness += (decimal)outerCover?.Thickness.Value;
						weight += (decimal)outerCover?.Weight.Value;
					}
				}

				if (w.OuterBack != null)
				{
					var outerCover = _wiroValues.OuterCover[w.OuterBack];
					if (outerCover != null)
					{
						weight += (decimal)outerCover?.Weight.Value;
						thickness += (decimal)outerCover?.Thickness.Value;
					}
				}


				if (w.InnerFrontStockForCover != null)
				{
					thickness += (decimal)w.InnerFrontStockForCover.Thickness;
					weight += GetPaperWeight(w.InnerFrontStockForCover, j.FinishedSize, 1);

					if (w.InnerFrontCello != null)
					{
						thickness += 0.02m;
						weight += 0.02m;
						if (w.InnerFrontCello.Contains("front & back"))
						{
							thickness += 0.02m;
							weight += 0.02m;
						}
					}
				}

				if (w.InnerBackStockForCover != null)
				{
					thickness += (decimal)w.InnerBackStockForCover.Thickness;
					weight += GetPaperWeight(w.InnerBackStockForCover, j.FinishedSize, 1);

					if (w.InnerBackCello != null)
					{
						thickness += 0.02m;
						weight += 0.25m;
						if (w.InnerBackCello.Contains("front & back"))
						{
							thickness += 0.02m;
							weight += 0.02m;
						}
					}
				}

			}
			Debug.WriteLine($"1 Jobs weight    is: {weight}.    {j.Quantity}'s weight    is {weight * j.Quantity}");
			Debug.WriteLine($"1 Jobs thickness is: {thickness}. {j.Quantity}'s thickness is {thickness * j.Quantity}");

			return new SingleJobDetails
			{
				w = width,
				h = height,
				weight = weight,
				depthOf1Job = thickness,
				finishedSize = finishedSize
			};


		}


		public void GetSingleJobThicknessWeight2(
			IJob j,
			out decimal w,
			out decimal h,
			out decimal thickness,
			out decimal weight,
			out string finishedSize)
		{
			IJobTemplate template = j.Template;
			ISize finishedSize1 = j.FinishedSize;
			ISize foldedSize1 = j.FoldedSize;
			IStock stock = j.FinalStock;
			bool magnet = j.NumberOfMagnets > 0;
			int pagesPerJob = j.Pages;
			if (pagesPerJob == 0)
				pagesPerJob = 1;
			IStock stockForCover = j.StockForCover;

			GetSingleJobThicknessWeight2(template, finishedSize1, foldedSize1, stock, pagesPerJob, stockForCover, magnet, out w, out h, out thickness, out weight, out finishedSize);
		}

		public void GetSingleJobThicknessWeight2(
			IJobTemplate template,
			ISize finishedSize1,
			ISize foldedSize1,
			IStock stock,
			int pagesPerJob,
			IStock stockForCover,
			bool magnet,
			out decimal w,
			out decimal h,
			out decimal thickness,
			out decimal weight,
			out string finishedSize)
		{
			thickness = 0m;
			weight = 0m;



			finishedSize = finishedSize1?.PaperSize?.Name ?? "";
			var foldedSize = foldedSize1?.PaperSize?.Name ?? "";

			if (foldedSize == "Custom")
			{
				var candts = _jobApp.ListPaperSize()
					.Where(_ => _.Size.CanFit(foldedSize1.Width, foldedSize1.Height)).ToList();
				var exCustom = candts
					.OrderBy(_ => _.Name.Length).FirstOrDefault();
				if (exCustom != null)
				{
					foldedSize = exCustom.Name;
				}
			}

			var foldFactor = _foldFactorFinder.Get(stock.GSM, finishedSize, foldedSize);

			// if (finishedSize == "Custom" || foldedSize == "Custom") ;

			h = finishedSize1?.Height ?? 0m;
			w = finishedSize1?.Width ?? 0m;

			if (w == 0 || h == 0)
			{
				h = finishedSize1?.PaperSize?.Size.Height ?? 0m;
				w = finishedSize1?.PaperSize?.Size.Width ?? 0m;
			}

			// if there is a fold then foldFactor will be > 1
			// in that case we pack folded
			if (foldFactor > 1 || foldedSize != "")
			{
				finishedSize = foldedSize;
				h = foldedSize1?.Height ?? 0m;
				w = foldedSize1?.Width ?? 0m;
				finishedSize = foldedSize;
			}

			var nameStock = stock.Name;
			var gsm = stock.GSM;

			var paperThickness = (decimal)stock.Thickness;

			if (paperThickness == 0)
			{
				paperThickness = 0.09m;
			}

			// start off with these

			// get number of printed paper
			var qtyPrintedPaper = 1;

			if (template.HasMultiplePages())
			{

				if (pagesPerJob == 0)
					pagesPerJob = 1;
				qtyPrintedPaper = qtyPrintedPaper * pagesPerJob;
			}

			thickness = paperThickness * pagesPerJob * foldFactor;

			if (thickness == 0.00m)
			{
				throw new Exception("Thickness cant be 0");
			}


			if (template?.Is(Magazine, MagazineNDD, MagazineSeparate, A4CalendarSelfCover, A4CalendarSeparateCover) ?? false)
			{
				qtyPrintedPaper = pagesPerJob / 2;
				thickness = paperThickness * qtyPrintedPaper;
			}

			if (template?.Is(MagazineSeparate) ?? false)
			{
				qtyPrintedPaper += 1;
			}

			// get base weight
			weight = GetPaperWeight(stock, finishedSize1, qtyPrintedPaper);

			if (template.Is(Notepads))
			{
				//increase weight and thickness  by considering back of the notepad
				var padStock = _jobApp.GetStock(PadStockName);
				weight += GetPaperWeight(padStock, finishedSize1, 1);
				thickness += padStock.Thickness;
			}

			if (template.Is(EnvelopeBlack, Envelope1Pms, Envelope2Pms, EnvelopeCmyk))
			{
				weight *= 2;
				thickness *= 2;
			}

			if (template.Is(PresentationFolder, PresentationFolderNDD))
			{
				// for presentation folder multiply by 2.7
				weight *= PresentationFolderWeightMultiplier;
				//thickness *= PresentationFolderWeightMultiplier;
			}

			if (template?.Is(MagazineSeparate) ?? false)
			{
				// for magazine separate add cover weight and thickness
				var covertStock = _jobApp.GetStock(stockForCover.Name);
				weight += GetPaperWeight(covertStock, finishedSize1, 1);
				thickness += covertStock.Thickness * 2;
			}

			if (magnet)
			{
				//magnet can't attach to magazine
				weight += 0.0015m;
				thickness += (decimal)0.7;
			}

			if (template.Is(DuplicateNCRBooks, TriplicateNCRBooks, QuadruplicateNCRBooks))
			{
				// these have multiple page + a writing mat
				var multiplier = 1;
				if (template.Is(DuplicateNCRBooks)) multiplier = 2;
				else if (template.Is(TriplicateNCRBooks)) multiplier = 3;
				else if (template.Is(QuadruplicateNCRBooks)) multiplier = 4;
				thickness *= multiplier;
				weight *= multiplier;

				var padBack = _jobApp.GetStock("300 GSM Uncoated");
				thickness += padBack.Thickness;
				weight += GetPaperWeight(padBack, finishedSize1, 1);
			}

			if (template.Is(PullUpBannerStandardStand, PullUpBannerPremiumStand))
			{
				var name = finishedSize1?.PaperSize?.Name ?? "";
				if (name.Contains("Small"))
				{
					weight += 6;
				}
				else if (name.Contains("Medium"))
				{
					weight += 8;
				}
				else if (name.Contains("Large"))
				{
					weight += 10;
				}
			}
		}


		public int GetPackingLevelFromJob(IJob j)
		{
			if (j.Template.Is(PresentationFolder, PresentationFolderNDD))
				return 4;
			else if (j.Template.Is(BacklitPosters, PosterMattArt, PosterCanvas))
				return 5;
			else if (j.Template.Is(Poster) && j.IsWideFormat())
				return 6;
			else if (j.Template.Is(Poster) && !j.IsWideFormat())
				return 5;
			else if (j.Template.Is(Postcard))
				return 3;
			return 0;
		}


		public List<Cost> FindBest(IJob j, SingleJobDetails jd, BrochureDistPackInfo brochureDistPackInfo)
		{
			bool abnormalPacking = false;

			var w = jd.w;
			var h = jd.h;
			var weight = jd.weight;
			var depthOf1Job = jd.depthOf1Job;
			var finishedSize = jd.finishedSize;



			//int capacity = 1;
			var packLevel = 1;

			var candidates = _cartonFinder.ListCarton(null).AsEnumerable();

			var isBC = j.Template.Is(BusinessCard, BusinessCardNdd, BusinessCardSdd, DoubleBusinessCard);
			var dblBC = j.FinishedSize.PaperSize.Id.Is(2, 20);

			if (j.Template.Is(PresentationFolder, PresentationFolderNDD))
			{
				packLevel = 4;
				candidates = candidates.Where(c => c.Level == packLevel);
			}
			else if (j.Template.Is(Poster, BacklitPosters, PosterMattArt, PosterCanvas))
			{

				packLevel = 5;

				if (j.Template.Is(Poster) && j.IsWideFormat())
				{
					packLevel = 6;
				}

				candidates = candidates.Where(c => c.Level == packLevel);
			}
			else if (j.Template.Is(WiroMagazines))
			{
				packLevel = -70;
				candidates = _cartonFinder.ListCarton(packagingLevel: -70);
			}
			else
			{

				if (isBC)
				{
					if (dblBC)
					{
						isBC = false;
					}
				}

				if (!isBC)
				{
					packLevel = 3;
				}

				if (j.Template.Is(Postcard))
				{
					packLevel = 3;
				}


				if (j.IsEnvelope())
				{
					packLevel = -60;
				}

				if (j.Template.Is(RigidSigns))
				{
					packLevel = -120;
				}

				candidates = _cartonFinder.Get((int)w, (int)h, packLevel);

				if (!candidates.Any())
				{
					abnormalPacking = true;
					candidates = _cartonFinder.ListCarton(null)
						.Where(_ => _.Level >= packLevel &&
									   _.Level < 9 &&
									   _.CanFit((float)w, (float)h));
				}

				if (candidates.Any(_ => _.Code.Contains("A4")))
				{
					if (jd.finishedSize.Contains("A4"))
					{
						candidates = candidates.Where(c => c.Code.Contains("A4"));
					}
				}

				if (jd.finishedSize.Contains("A2"))
				{
					candidates = candidates.Where(c => c.Code.Contains("A2"));
				}

				if ((new List<string>() { "A4", "A5", "DL", "DL Roll Fold (97:100:100)", "DL Roll Fold (98:99:100)" }).Contains(finishedSize)
				|| ((w <= 210 && h <= 297) || (w <= 297 && h <= 210)))
					candidates = candidates.Where(c => !c.Code.Contains("A3"));

				if (j.IsMagazine())
				{
					candidates = candidates.Where(c => !c.Code.Contains("A4 T-Mailer"));
				}
			}

			#region BC pack selection

			if (j.Template.Is(BusinessCard, BusinessCardNdd, BusinessCardSdd) && !abnormalPacking && !dblBC)
			{
				if (LORD1254 && j.FinalStock.GSM.Is(310, 360, 420))
				{  // new code


					candidates = candidates.Where(c => c.Level == 1);
				}
				else
				{
					//candidates = candidates.Where(c => c.PredicateOnJob?.Invoke(j) ?? false);

					candidates = candidates.Where(c => c.Level == 1);
					var numOfCelloSide = 0;
					if (j.FinalFrontCelloglaze != JobCelloglazeOptions.None) numOfCelloSide++;
					if (j.FinalBackCelloglaze != JobCelloglazeOptions.None) numOfCelloSide++;

					if (j.Quantity <= 100)
					{
						candidates = candidates.Where(c => c.Code.StartsWith("D100"));  //capacity = 100;
					}
					else if (j.Quantity <= 250)
					{
						candidates = candidates.Where(c => c.Code.StartsWith("D250"));  //capacity = 250;
					}
					else //capacity = 500;
					{
						int gsmX = j.FinalStock.GSM;

						// hack: put 285 GSM Loyalty cards in to H Boxes
						if (gsmX == 285)
						{
							gsmX = 400;
						}

						if (gsmX >= 400)
						{
							candidates = candidates.Where(c => c.Code.StartsWith("H"));
							if (numOfCelloSide > 0)
								candidates = candidates.Where(c => c.Code.Contains($"{numOfCelloSide}"));
						}
						else if (gsmX < 400 && gsmX > 310)
						{
							candidates = candidates.Where(c => c.Code.StartsWith("DC500"));
						}
						else if (gsmX <= 310)
						{
							candidates = candidates.Where(c => c.Code.StartsWith("L"));
							if (numOfCelloSide > -1)
								candidates = candidates.Where(c => c.Code.Contains($"{numOfCelloSide}"));
						}
					}
				}
			}






			#endregion BC pack selection

			#region Presentation folder carton selection business rules

			else if (j.Template.Is(PresentationFolder, PresentationFolderNDD))
			{
				var style = j.DieCutType;
				var trim = j.FinishedSize.PaperSize.Name;
				var sgsm = j.FinalStock.GSM;

				var r = PresentationFolderPF300;

				if (style.Is(StyleA, StyleF, StyleH, StyleK, StyleL, StyleM, StyleN, StyleO))
				{
					r = PresentationFolderPFOS;
				}

				if (style.Is(SingleGusset, DoubleGusset) && trim == "A5")
				{
					r = "A3";
				}

				if (style.Is(SingleGusset, DoubleGusset, StyleB, StyleC, StyleD, StyleE, StyleG, StyleI, StyleJ))
				{
					r = sgsm >= 350 ? PresentationFolderPF400 : PresentationFolderPF300;
				}

				if (style.Is(SingleGusset, DoubleGusset) && trim == "A4")
				{
					r = sgsm >= 350 ? PresentationFolderPF400 : PresentationFolderPF300;
				}

				candidates = candidates.Where(c => c.Code == r);
				//capacity = 250;
			}

			#endregion Presentation folder carton selection business rules

			else if (j.Template.Is(PullUpBannerStandardStand, PullUpBannerPremiumStand))
			{
				candidates = _cartonFinder.ListCarton(packagingLevel: 6);
			}

			else if (j.Template.Is(BacklitPosters, MeshBanner, VinylOutdoor,
				PosterMattArt, PosterCanvas, RemovableWallDecals, VinylStickerOutdoor)
				&& !((w <= 297 && h <= 420) || (h <= 297 && w <= 420)))
			{
				candidates = _cartonFinder.ListCarton(packagingLevel: 6);
			}

			else if (j.Template.Is(WiroMagazines))
			{

				candidates = _cartonFinder.ListCarton(packagingLevel: -70);
			}


			//if (j.IsEnvelope())
			//{
			//	packLevel = 200; // some arbitrary number so it cant be found
			//					 //DL Envelope Carton, Qty 500, Dimensions 280 x 230 x 120mm Gross Weight 2.25kg
			//					 //C6 Envelope Carton, Qty 500, Dimensions 260 x 170 x 120mm Gross Weight 1.85kg
			//					 //C4 Envelope Carton, Qty 250, Dimensions 330 x 250 x 160mm Gross Weight 10.9kg
			//	if (j.FinishedSize.PaperSize.Name.Contains("DL"))
			//	{
			//		candidates = new List<Carton>() { new Carton() { Code = "DL Envelope Carton", Capacity = 500, 
			//			Width= 280,
			//			Height = 230, 
			//			Depth = 120, Weight = 2.25m } };
			//	}
			//	else if (j.FinishedSize.PaperSize.Name.Contains("C6"))
			//	{
			//		candidates = new List<Carton>() { new Carton() { Code = "C6 Envelope Carton", Capacity = 500, Width = 260, Height = 170, Depth = 120, Weight = 1.85m } };
			//	}
			//	if (j.FinishedSize.PaperSize.Name.Contains("C4"))
			//	{
			//		candidates = new List<Carton>() { new Carton() { Code = "C4 Envelope Carton", Capacity = 250, 
			//			Width = 330, IWidth = 330,
			//			Height = 250,  IHeight = 250,
			//			Depth = 160, IDepth = 160,
			//			Weight = 0.1m, CanGetFurhterWrapped = false } };
			//	}
			//}




			var _getInBundlesOf = GetInBundlesOf(j, jd, brochureDistPackInfo);

			var weightLimit = MaxWeightPerCarton;



			if (brochureDistPackInfo?.MailHouse != null)
			{
				var mh = brochureDistPackInfo?.MailHouse;
				weightLimit = mh.Name.Contains("PMP") ? 10 : 16;

				//if (mh.InBundlesOf > 0)
				//{
				//	_getInBundlesOf = mh.InBundlesOf;
				//}
			}


			if (brochureDistPackInfo?.MailHouse != null || brochureDistPackInfo?.PackingInstruction != 0)
			{
				var totoalDepth = _getInBundlesOf * depthOf1Job;
				while (totoalDepth > 80)
				{
					_getInBundlesOf = _getInBundlesOf - (int)brochureDistPackInfo?.MailHouse?.InBundlesOf;
					totoalDepth = _getInBundlesOf * depthOf1Job;
				};
			}



			/*if (j.IsBrochure() && (brochureDistPackInfo?.MailHouse?.InBundlesOf !=  null ||  j.BrochureDistPackInfo?.BundlesOf !=null) )
			{
				var bundleThickness = _getInBundlesOf * depthOf1Job;
				if(bundleThickness > 80)
				{
					throw new Exception("Bundle thickness > 80mm");
				}
			}*/



			if (!candidates.Any())
			{
				var x = new List<ICarton>();

				int _w = (int)Math.Ceiling(w);
				int _h = (int)Math.Ceiling(h);

				int _howManyTo20Kilo = (int)(((decimal)20.0) / weight);

				int _d = (int)Math.Ceiling(depthOf1Job * _howManyTo20Kilo);

				var c = new Carton()
				{
					Width = _w,
					IWidth = _w,
					Height = _h,
					IHeight = _h,
					Depth = _d,
					IDepth = _d,
					Capacity = null,
					Code = brownPaperWrap,
					Weight = 0,
					Level = 11
				};

				x.Add(c);

				candidates = x;
			}

			var costList = new List<Cost>();
			// calculate cost for each candidate packages
			candidates.ForEach(_ =>
			{
				var getInBundlesOf = _getInBundlesOf;
				var howManyFitsInside = 0;
				var fitsByDimention = Box.Fits(w, h, depthOf1Job, _.IWidth, _.IHeight, _.IDepth);

				if (j.Order.PackWithoutPallets)
				{
					if (_.Level == 3 && _.Code.Contains("A4"))
					{
						weightLimit = MaxWeightPerCartonWithoutPallet;
					}
				}


				howManyFitsInside = _.Capacity ?? fitsByDimention;

				if (LORD1254 && j.IsBusinessCard())
					howManyFitsInside = Math.Min(fitsByDimention, (_.Capacity ?? fitsByDimention));

				if (_.Capacity != null && _.Level == 5 // Like A1 A2 Poster carton
					&& _.Capacity > fitsByDimention)
				{
					howManyFitsInside = fitsByDimention;
				}

				if (fitsByDimention == 0)
				{
					if (_.Level != 6)
						return;
				}

				if (j.Template.Is(PullUpBannerStandardStand, PullUpBannerPremiumStand))
				{
					howManyFitsInside = 1;
				}

				if (howManyFitsInside == 0)
					return;

				if (!j.IsWideFormat())
				{
					var howManyFitsRoundedDown = (howManyFitsInside / getInBundlesOf) * getInBundlesOf;
					if (howManyFitsRoundedDown <= howManyFitsInside && howManyFitsRoundedDown != 0)
					{
						//if (howManyFitsRoundedDown == 0)
						//	return;
						howManyFitsInside = howManyFitsRoundedDown;
					}
				}

				//var maxInTermsOfWeight = (int) ( weightLimit / weight);
				//maxInTermsOfWeight = (maxInTermsOfWeight / getInBundlesOf) * getInBundlesOf;

				//if (maxInTermsOfWeight < howManyFitsInside)
				//{
				//	howManyFitsInside = maxInTermsOfWeight;
				//}

				//var howManyCanfitInsideCurrent2 = (int)Math.Floor(_.IDepth / depthOf1Job);
				var noofcartons = (int)Math.Ceiling(j.Quantity / (decimal)howManyFitsInside);

				if ((howManyFitsInside < getInBundlesOf) && j.IsBrochure())
				{
					getInBundlesOf = 50;
					howManyFitsInside = howManyFitsInside - howManyFitsInside % 50;
				}

				var cweight = (howManyFitsInside * weight) + _.Weight;
				if (cweight > weightLimit)
				{
					// weight is getting more than 23kg so reudce howManyFitsInside
					do
					{
						howManyFitsInside -= getInBundlesOf;
					} while ((howManyFitsInside * weight) + _.Weight > weightLimit);
				}
				if (howManyFitsInside <= 0)
					return;
				noofcartons = (int)Math.Ceiling(j.Quantity / (decimal)howManyFitsInside);
				var cost = new Cost()
				{
					Carton = _,
					QtyInEachCartons = howManyFitsInside,
					NumberOfCartons = noofcartons,
					Slack = _.InternalVolume - (w * h * (decimal)depthOf1Job * howManyFitsInside),
					TVol = noofcartons * _.Width * _.Height * _.Depth,
					InBundlesOf = getInBundlesOf,
				};
				costList.Add(cost);
			});

			return costList;
		}

		private List<Package> ListPackagesFrom(List<Cost> options, int totalQty, decimal singleJobsWeight, IJob job, BrochureDistPackInfo brochureDistPackInfo)
		{
			var haveput = 0;
			var result = new List<Package>();
			Cost bestOption = null;

			var costList = options
				.OrderBy(_ => _.NumberOfCartons)
				.ThenBy(_ => _.TVol)
				.ThenBy(_ => _.Slack * _.NumberOfCartons);

			bestOption = costList.First();
			var n = (int)Math.Ceiling(bestOption.NumberOfCartons);
			var perPack = (int)Math.Ceiling(bestOption.QtyInEachCartons);

			while (haveput < totalQty)
			{
				var left = (totalQty - haveput);

				var nextBestOption = costList.Skip(1).FirstOrDefault();
				// switch to the next best option if
				if (haveput > 0 //  we have put something in the first box
					&& nextBestOption != null // and there are multiple options
					&& (left <= nextBestOption.QtyInEachCartons) // and amount left is less than next options max
					&& (bestOption.QtyInEachCartons != nextBestOption.QtyInEachCartons)
					&& (nextBestOption.Carton.InternalVolume < bestOption.Carton.InternalVolume)
				)
				{
					bestOption = costList.Skip(1).First();
					n = (int)Math.Ceiling(bestOption.NumberOfCartons);
					perPack = (int)Math.Ceiling(bestOption.QtyInEachCartons);
				}

				if (perPack > left)
				{
					perPack = left;
				}

				haveput += perPack;

				decimal weight = RoundUp(bestOption.Carton.Weight + (singleJobsWeight * perPack));
				if (job.IsEnvelope())
				{
					weight = bestOption.Carton.Weight;
				}
				result.Add(new Package()
				{
					CartonCode = bestOption.Carton.Code,
					Level = bestOption.Carton.Level,
					JobQty = perPack,
					Items = null,
					Depth = bestOption.Carton.Depth,
					Width = bestOption.Carton.Width,
					Height = bestOption.Carton.Height,
					Weight = weight,
					JobId = job.Id,
					StopPackingFurther = ((brochureDistPackInfo?.MailHouse?.Id ?? 0) > 0),


				});
			}

			var xx = result.Select(_ => _.JobQty).ToList();
			haveput = result.Sum(_ => _.JobQty);

			return result;
		}

		// pack level 2 up wards
		public IList<Package> PackItems0(IList<Package> items, bool allowSkidDelivery)
		{
			if (items == null)
				return null;

			if (items.Count > 1)
			{
				//items = PutInto(items, cartonFinder.ListCarton(3)).OrderBy(_ => _.Weight).ToList();
				IList<ICarton> lev3Cartons = _cartonFinder.ListCarton(3);
				var items2 = PutInto(items, lev3Cartons).OrderBy(_ => _.Weight).ToList();
				if (items2.Count == 1)
				{
					items = items2;
				}
			}

			var tmppackages = items.OrderByDescending(_ => _.Dimention).ToList();

			var packages = new List<Package>();

			var oversizedA3Cartons = _cartonFinder.ListCarton(A3Turduken);
			var skidCartons = _cartonFinder.ListCarton(Skids);

			// 8/9 BCSTD -> Turducken
			// 9>  Skid

			long vol = A4SizeCartonVolume;
			var limit2 = MinimumLevel10Number;
			var limit1 = MinimumLevel9Number;

			if (tmppackages.Count > 0 && tmppackages.All(_ => _.CartonCode == "BC Std T-Mailer"))
			{
				vol = BCStStdCartonVolumne;
				limit2 = 10;
				limit1 = 2;
			}
			if (tmppackages.Count > 0 && tmppackages.All(_ => _.CartonCode == "Tube"))
			{
				var t0 = tmppackages.OrderByDescending(_ => _.Depth).First();
				var skid = _cartonFinder.GetCarton("Skid - Small");
				if (t0.Depth > skid.IDepth)
				{
					skid = _cartonFinder.GetCarton("Skid - Medium");
					if (t0.Depth > skid.IDepth)
					{
						skid = _cartonFinder.GetCarton("Skid - Large");
					}
				}
				vol = tmppackages.First().Dimention;

				// find how many of the the biggest Tube fits in the smallest skid
				limit2 = Box.Fits(t0.Width, t0.Height, t0.Depth, skid.Width, skid.Height, skid.Depth);

				// we dont want tubes in A3 turduken
				limit1 = 999999999;
			}

			while (true)
			{
				Package pack = null;

				var count = tmppackages.Count(_ => _.Depth * _.Width * _.Height >= vol);

				if ((count >= limit2) && allowSkidDelivery)
				{
					pack = PackWithoutWeightRestriction(tmppackages, skidCartons);
				}
				else if (count >= limit1)
				{
					pack = PackWithWeightRestriction(tmppackages, oversizedA3Cartons);
				}

				if (pack != null)
				{
					packages.Add(pack);
				}

				if (pack == null || !tmppackages.Any())
				{
					break;
				}
			}

			packages.AddRange(tmppackages);

			return packages;
		}

		// pack level 2 up wards
		public IList<Package> PackItems(IList<Package> items, bool allowSkidDelivery)
		{
			if (items == null)
				return null;

			if (items.Count <= 1)
				return items;


			if (items.All(_ => _.StopPackingFurther))
			{
				allowSkidDelivery = false;
			}


			var oversizedA3Cartons = _cartonFinder.ListCarton(null).Where(_ => _.Level == 3 || _.Level == 9).ToList();
			var skidCartons = _cartonFinder.ListCarton(Skids);

			if (items.Count > 1)
			{
				//items = PutInto(items, cartonFinder.ListCarton(3)).OrderBy(_ => _.Weight).ToList();
				var items2 = PutInto(items, _cartonFinder.ListCarton(3)).OrderBy(_ => _.Weight).ToList();
				if (items2.Count == 1)
				{
					items = items2;
				}
			}

			var tmppackages0 = items.OrderByDescending(_ => _.Dimention).ToList();
			var tmppackages1 = items.OrderByDescending(_ => _.Dimention).ToList();

			var skidsSolution = new List<Package>();
			var turdukenSolution = new List<Package>();


			//var oversizedA3Cartons = cartonFinder.ListCarton(A3Turduken);



			Package packSkid = null;
			while (allowSkidDelivery)
			{
				if (tmppackages0.Count < 4)
					break;

				packSkid = PackWithoutWeightRestriction(tmppackages0, skidCartons);
				if (packSkid != null)
				{
					skidsSolution.Add(packSkid);
				}

				if (packSkid == null || !tmppackages0.Any())
				{
					break;
				}
			}
			skidsSolution.AddRange(tmppackages0);

			Package packTurduken = null;
			var packsThatCantGetPackedFuther = tmppackages1.Where(_ => _.StopPackingFurther).ToList();
			var packsThatCanGetPackedFuther = tmppackages1.Where(_ => !_.StopPackingFurther).OrderBy(_ => _.Weight).ToList();
			while (true)
			{
				packTurduken = PackWithWeightRestriction(packsThatCanGetPackedFuther, oversizedA3Cartons);
				if (packTurduken != null)
				{
					turdukenSolution.Add(packTurduken);
				}
				if (packTurduken == null || !packsThatCanGetPackedFuther.Any())
				{
					break;
				}
			}
			turdukenSolution.AddRange(packsThatCanGetPackedFuther);
			turdukenSolution.AddRange(packsThatCantGetPackedFuther);

			var turdukenCount = turdukenSolution.Count(); // (_ => _.CartonCode == TurduckenOversizedA3);

			var solution = items;

			if (turdukenCount > 0 && turdukenCount < items.Count && (turdukenSolution.Count <= 3 || !allowSkidDelivery))
				solution = turdukenSolution;
			else
			// if both have same count return turduken
			if (skidsSolution.Count == turdukenSolution.Count && turdukenCount != 0)
				solution = turdukenSolution;
			else

			if (allowSkidDelivery && items.Count >= 4)
				solution = skidsSolution;
			if (solution[0]?.Items != null)
			{
				solution[0].Items = solution[0].Items.OrderBy(_ => _.JobId).ToList();
			}


			return solution;
		}

		private List<Item> GetItemsWithinWeight(List<Item> items, decimal allowedWeight)
		{
			decimal currentWeight = 0;
			var tmpList = new List<Item>();

			foreach (var item in items)
			{
				if (currentWeight + item.weight <= allowedWeight)
				{
					tmpList.Add(item);
					currentWeight += item.weight;
				}
			}

			return tmpList;
		}

		private Package PackWithWeightRestriction(IList<Package> items, IList<ICarton> target)
		{
			// from given Items create a list of items for packager
			var leftover = items.Select((t, i) => new Item
			{
				width = t.Width,
				depth = t.Depth,
				height = t.Height,
				itemIndex = i,
				weight = t.Weight
			}).ToList();

			// initialize
			Container lastPackedOption = null;
			var leftoverVolume = leftover.Sum(l => (long)l.width * l.height * l.depth);

			foreach (var targetCarton in target)
			{
				var tmpLeftOver = leftover;

				var allowWeight = MaxWeightPerCarton - targetCarton.Weight;

				if (allowWeight > 0)
				{
					tmpLeftOver = GetItemsWithinWeight(leftover, allowWeight);
				}

				var currentPackOption = new Packager().Pack(targetCarton, tmpLeftOver);
				if (currentPackOption.items.Count <= 1)
				{
					//can only pack one item, skip this choice
					continue;
				}
				else if (lastPackedOption == null || currentPackOption.items.Count > lastPackedOption.items.Count)
				{
					lastPackedOption = currentPackOption;
					if (lastPackedOption.items.Count == leftover.Count)
					{
						break;
					}
				}
			}

			if (lastPackedOption != null)
			{
				var package = new Package
				{
					Depth = lastPackedOption.carton.Depth,
					Width = lastPackedOption.carton.Width,
					Height = lastPackedOption.carton.Height,
					CartonCode = lastPackedOption.carton.Code,
					Items = new List<Package>()
				};
				foreach (var itemIndex in lastPackedOption.items.OrderByDescending(i => i))
				{

					package.Items.Add(items[itemIndex]);
					items.RemoveAt(itemIndex);
				}
				if (package.Items.Any())
				{
					package.JobId = package.Items.First().JobId;
					package.JobQty = package.Items.Sum(i => i.JobQty);
					package.Weight = package.Items.Sum(i => i.Weight) + lastPackedOption.carton.Weight; // Include the weight of the carton
				}

				return package;
			}

			return null;
		}
		public static int RectFits(int largerWidth, int largerHeight, int smallerWidth, int smallerHeight)
		{
			// Calculate the total number of smaller rectangles that can fit in the larger rectangle in both orientations
			int rectanglesPerLargerWidth1 = largerWidth / smallerWidth;
			int rectanglesPerLargerHeight1 = largerHeight / smallerHeight;
			int totalRectangles1 = rectanglesPerLargerWidth1 * rectanglesPerLargerHeight1;

			int rectanglesPerLargerWidth2 = largerWidth / smallerHeight;
			int rectanglesPerLargerHeight2 = largerHeight / smallerWidth;
			int totalRectangles2 = rectanglesPerLargerWidth2 * rectanglesPerLargerHeight2;

			return Math.Max(totalRectangles1, totalRectangles2);
		}
		private Package PackWithoutWeightRestriction(IList<Package> items, IList<ICarton> target)
		{
			// from given Items create a list of items for packager
			var leftover = new List<Item>();
			for (var i = 0; i < items.Count; i++)
			{
				leftover.Add(new Item
				{
					width = items[i].Width,
					depth = items[i].Depth,
					height = items[i].Height,
					itemIndex = i,
					weight = items[i].Weight
				});
			}

			// initialize
			Container lastPackedOption = null;
			var leftoverVolume = leftover.Sum(l => (long)l.width * l.height * l.depth);

			foreach (var targetCarton in target)
			{
				var tmpLeftOver = leftover;

				var currentPackOption = new Packager().Pack(targetCarton, tmpLeftOver);
				if (currentPackOption.items.Count <= 1)
				{
					//can only pack one item, skip this choice
					continue;
				}
				else if (lastPackedOption == null || currentPackOption.items.Count > lastPackedOption.items.Count)
				{
					lastPackedOption = currentPackOption;
					if (lastPackedOption.items.Count == leftover.Count)
					{
						break;
					}
				}
			}

			if (lastPackedOption != null)
			{
				var package = new Package()
				{
					Depth = lastPackedOption.carton.Depth,
					Width = lastPackedOption.carton.Width,
					Height = lastPackedOption.carton.Height,
					CartonCode = lastPackedOption.carton.Code,
					Items = new List<Package>()
				};
				foreach (var itemIndex in lastPackedOption.items.OrderByDescending(i => i))
				{
					package.Items.Add(items[itemIndex]);
					items.RemoveAt(itemIndex);
				}

				// Calculate Skid depth
				if (package.CartonCode.Contains("Skid"))
				{
					int depth = 135; // 135 mm for pallet
					var groupedPackages = package.Items.GroupBy(_ =>
					{
						if (_.CartonCode.Contains("A4"))
							return "A4";
						return _.CartonCode;
					}).ToList();

					foreach (var group in groupedPackages)
					{
						var c = _cartonFinder.GetCarton(group.Key);
						int canFit = RectFits(package.Width, package.Height, c.Width, c.Height);
						int packageCount = group.Count();
						var layerCount = (int)Math.Ceiling(packageCount / (float)canFit);
						int depth1Layer = group.Max(_ => _.Depth);
						depth += (int)(layerCount * depth1Layer);
					}
					package.Depth = depth;
				}



				package.Weight = package.Items.Sum(i => i.Weight);
				package.Weight += lastPackedOption.carton.Weight;

				var uniqJobIds = package.Items.Select(i => i.JobId).Distinct().ToList();
				if (uniqJobIds.Count == 1)
				{
					package.JobId = uniqJobIds[0];
					package.JobQty = package.Items.Sum(i => i.JobQty);
				}
				return package;
			}

			return null;
		}

		private int IncreaseDepth(Package package, string cartonCode, int mult)
		{
			IEnumerable<Package> packsagesInside = package.Items.Where(p => p.CartonCode == cartonCode);
			int count = packsagesInside.Count();
			if (count > 0)
			{
				var layerCount = (int)Math.Ceiling(count / (float)mult);
				return (int)(layerCount * packsagesInside.First().Depth);
			}
			return package.Depth;
		}

		private IList<Package> PutInto(IList<Package> items, IList<ICarton> target)
		{
			var cartons = _cartonFinder.ListCarton(null)
				.ToDictionary(c => c.Code + c.Level, v => v);
			var packages = target.Select(c => new KeyValuePair<ICarton, decimal>(c, c.InternalVolume))
				.OrderBy(v => v.Value).ToList();

			var leftover = new List<Item>();
			var packed = new List<Package>();

			for (var i = 0; i < items.Count; i++)
			{
				string v = items[i].CartonCode + items[i].Level;
				var c = cartons[v];
				leftover.Add(new Item
				{
					width = c.Width,
					depth = c.Depth,
					height = c.Height,
					itemIndex = i,
					weight = items[i].Weight
				});
			}

			Container lastPackedOption = null;
			while (leftover.Count > 1)
			{
				var leftoverVolume = leftover.Sum(l => (long)l.width * (long)l.height * (long)l.depth);

				for (var i = 0; i < packages.Count; i++)
				{
					if (packages[i].Value >= leftoverVolume || i == packages.Count - 1)
					{
						//try package if this is the largest container or if container volume larger than total volume
						var currentPackOption = new Packager().Pack(packages[i].Key, leftover);
						if (currentPackOption.items.Count <= 1)
						{
							//can only pack one item, skip this choice
							continue;
						}
						else if (lastPackedOption == null ||
								 currentPackOption.items.Count > lastPackedOption.items.Count)
						{
							lastPackedOption = currentPackOption;
							if (lastPackedOption.items.Count == leftover.Count)
							{
								break;
							}
						}
					}
				}

				if (lastPackedOption != null)
				{
					var package = new Package()
					{
						CartonCode = lastPackedOption.carton.Code,
						Level = lastPackedOption.carton.Level,
						Depth = lastPackedOption.carton.Depth,
						Width = lastPackedOption.carton.Width,
						Height = lastPackedOption.carton.Height,
						Items = new List<Package>()
					};

					foreach (var itemIndex in lastPackedOption.items.OrderByDescending(i => i))
					{
						package.Items.Add(items[itemIndex]);

						if (leftover.Count > 0)
							leftover.RemoveAt(0);
					}

					package.JobQty += package.Items.Sum(i => i.JobQty);
					package.Weight = package.Items.Sum(i => i.Weight) + lastPackedOption.carton.Weight; // Include the weight of the carton
					packed.Add(package);
				}
				else
				{
					//can't fit into any
					break;
				}
			}

			leftover.ForEach(l => { packed.Add(items[l.itemIndex]); });
			return packed;
		}

		public IList<Package> SetPackage(IJob job)
		{
			try
			{
				if (job.Freight == null)
					job.Freight = new Freight();

				job.Freight.IsCustom = false;

				IList<Package> packages = GetPackages(job, job.Quantity, job.BrochureDistPackInfo);
				if (packages != null)
				{
					job.Freight.Packages = new ListOfPackages();
					packages.ForEach(c => job.Freight.Packages.Add(c));
				}

				if (job.HasSplitDelivery)
				{
					job.Splits.ForEach(part =>
					{
						packages = GetPackages(job, part.Quantity, part.BrochureDistPackInfo);
						if (packages != null)
						{
							part.Packages = new ListOfPackages();
							packages.ForEach(c => part.Packages.Add(c));
						}
					});
				}
			}
			catch (Exception ex)
			{
				var m = ex.Message;
				Log.Error($"Job {job.Id}");
				Log.Error(m, ex);
			}

			return job.Freight.Packages;
		}

		private Package PackageFromCarton(ICarton c)
		{
			return new Package()
			{
				CartonCode = c.Code,
				Level = c.Level,
				Width = c.Width,
				Height = c.Height,
				Depth = c.Depth,
				Weight = c.Weight,
				Items = new List<Package>()
			};
		}

		/// <summary>
		/// Every job based on template and size is bundled in discrete quantities like 25, 50, 100 ,250, 500
		/// This function implements LEP standard bundling quantities instructions
		/// </summary>
		/// <param name="j">Job which we neeed to bundle</param>
		/// <returns>bundle size, ie number of jobs that should be bundled together. </returns>
		public int GetInBundlesOf(IJob j, SingleJobDetails jd, BrochureDistPackInfo brochureDistPackInfo)
		{
			var size = j.FinishedSize.PaperSize.Name;
			if (j.Template.Is(Stationery, StationerySDD, Letterhead, LetterheadSDD, LetterheadNDD
				, Compliments, ComplimentsNDD, ComplimentsSDD))
			{
				return 500;
			}

			if (j.IsMagazine())
			{
				if (j.FinalStock.GSM <= 130 || j.Pages <= 16)
					return 50;

				return 25;
			}

			if (j.Template.Is(Postcard))
			{
				return 250;
			}

			if (j.IsBrochure())
			{
				//GetSingleJobThicknessWeight(j, out var w, out var h, out var depthOf1Job, out var singleJobsWeight, out var finalSize);
				int result = 250;


				if (brochureDistPackInfo?.MailHouse?.MaxInBundle != null && j.BrochureDistPackInfo?.MailHouse?.MaxInBundle != 0)
				{
					result = (int)brochureDistPackInfo?.MailHouse?.MaxInBundle;
				}
				else if (brochureDistPackInfo?.PackingInstruction != PackingInstruction.Standard &&
						brochureDistPackInfo?.BundlesOf != null
						&& brochureDistPackInfo?.BundlesOf != 0)
				{
					result = (int)brochureDistPackInfo?.BundlesOf;
				}
				else
				{
					if (j.FoldedSize != null)
						result = 100;
					else if (size.Is("A6", "A5", "A4", "A3", "DL"))
					{
						if (j.FinalStock.GSM < 200) result = 500;
						if (j.FinalStock.GSM >= 200) result = 250;
					}
					else if (size.Is("Double DL"))
					{
						if (j.FinalStock.GSM <= 130) result = 200;
						if (j.FinalStock.GSM <= 250) result = 100;
						if (j.FinalStock.GSM <= 350) result = 50;
					}

					if (j.NumberOfMagnets > 0)
						result = 10;
				}

				// todo: move this to some where else
				if (brochureDistPackInfo?.MailHouse == null && j.BrochureDistPackInfo.PackingInstruction == null)
				{
					brochureDistPackInfo.PackingInstruction = PackingInstruction.Standard;
				}
				// todo end

				// to do reddice bundling size to be < 80mm
				if (brochureDistPackInfo?.MailHouse != null)
				{
					int xx = 0;
					var totoalDepth = result * jd.depthOf1Job;
					while (totoalDepth > 80 && xx < 100)
					{

						result = result - (int)(brochureDistPackInfo?.MailHouse?.InBundlesOf ?? 1);
						totoalDepth = result * jd.depthOf1Job;
						xx++;
					};
				}

				return result;
			}

			if (j.Template.Is(Notepads))
			{
				return 1;
			}

			if (j.Template.Is(WiroMagazines))
			{
				return 5;
			}
			return 50;
		}

		/// <summary>
		/// Calculates and returns pacakges for a Job
		/// Does not mutate, job.Freight
		/// </summary>
		/// <param name="job"></param>
		/// <returns></returns>
		public IList<Package> GetPackages(IJob job, int jobQuantity, BrochureDistPackInfo brochureDistPackInfo)
		{

			var requiredType =
				(JobTypeOptions)Enum.Parse(typeof(JobTypeOptions), job.Template.Id.ToString());

			//if (requiredType == JobTypeOptions.Custom) {
			//	return null;
			//}

			//GetSingleJobThicknessWeight(job, out var w, out var h, out var depthOf1Job, out var singleJobsWeight, out var finalSize);

			var jd = GetSingleJobThicknessWeight(job);

			//var jobQuantity = job.Quantity + GetExtraPrintByType(requiredType);

			var options = FindBest(job, jd, brochureDistPackInfo);
			if (options.Count == 0)
			{
				return null;
			}
			//var roundDown = !job.IsBusinessCard();
			//var inBundlesOf = GetInBundlesOf(job);
			var packages = ListPackagesFrom(options, jobQuantity, jd.weight, job, brochureDistPackInfo);

			if (job.IsEnvelope())
			{
				packages.ForEach(_ => _.StopPackingFurther = true);
				return packages;
			}

			if (job.IsBusinessCard() && (packages.Count > 0) && packages.All(_ => _.Level == 1))
			{
				//todo: do BC Extra logic
				List<Package> listOfConsolidatedPackages = WrapLevel1BCBoxes(packages);
				packages = listOfConsolidatedPackages;
			}

			return packages;
		}

		private List<Package> WrapLevel1BCBoxes(List<Package> packages)
		{
			var items2 = new List<Package>();
			var target = _cartonFinder.ListCarton(2).OrderBy(_ => _.InternalVolume).ToList();

			var totalPackages = packages.Count;

			var costList2 = new List<Cost>();
			target.ForEach(_ =>
			{
				var howManyCanfitInsideCurrent = Box.Fits(packages[0].Width, packages[0].Height, packages[0].Depth,
					_.IWidth, _.IHeight, _.IDepth);
				if (howManyCanfitInsideCurrent == 0)
					return;
				var noofcartons = (int)Math.Ceiling(totalPackages / (decimal)howManyCanfitInsideCurrent);
				var cost = new Cost()
				{
					Carton = _,
					NumberOfCartons = noofcartons,
					QtyInEachCartons = howManyCanfitInsideCurrent,
					Slack = _.InternalVolume - (howManyCanfitInsideCurrent * packages[0].Dimention),
					TVol = noofcartons * _.Width * _.Height * _.Depth
				};

				costList2.Add(cost);
			});

			costList2 = costList2
				.OrderBy(_ => _.NumberOfCartons)
				.ThenBy(_ => _.TVol)
				.ThenBy(_ => _.Slack * _.NumberOfCartons).ToList();

			var listOfConsolidatedPackages = new List<Package>();

			var bestOption = costList2.First();

			while (packages.Count > 0)
			{
				var nextBestOption = costList2.Skip(1).FirstOrDefault();
				// switch to the next best option if
				if (listOfConsolidatedPackages.Count > 0 //  we have put something in the first box
					&& nextBestOption != null // and there are multiple options
					&& (packages.Count <= nextBestOption.QtyInEachCartons) // and amount left is less than next options max
					&& (bestOption.QtyInEachCartons != nextBestOption.QtyInEachCartons)
				)
				{
					bestOption = nextBestOption;
				}

				var bestCarton = bestOption.Carton;

				var p0 = packages[0];
				var howMany = Box.Fits(p0.Width, p0.Height, p0.Depth, bestCarton.IWidth, bestCarton.IHeight, bestCarton.IDepth);

				var p = PackageFromCarton(bestCarton);
				var itemsToPut = packages.Take(howMany);
				p.Items.AddRange(itemsToPut);
				p.Weight += itemsToPut.Sum(_ => _.Weight);
				p.JobId = itemsToPut.First().JobId;
				p.JobQty += itemsToPut.Sum(_ => _.JobQty);
				listOfConsolidatedPackages.Add(p);

				for (var i = 0; i < howMany; i++)
				{
					packages.RemoveAt(0);
					if (packages.Count == 0) break;
				}
			}

			return listOfConsolidatedPackages;
		}

		public IList<Package> GetPackagesConsolidated(IJob job, int jobQuantity, bool allowSkidDelivery, BrochureDistPackInfo brochureDistPackInfo)
		{
			var packages = GetPackages(job, jobQuantity, brochureDistPackInfo);
			if (packages == null)
			{
				return null;
			}

			var packagesConsolidated = PackItems(packages, allowSkidDelivery);

			return packagesConsolidated;


		}

		private decimal GetPaperWeight(IStock stock, ISize size, int qty)
		{
			var w = size.Width;
			var h = size.Height;
			if (w == 0 || h == 0)
			{
				w = size.PaperSize.Size.Width;
				h = size.PaperSize.Size.Height;
			}

			try
			{
				decimal stockWeight = 0;
				stockWeight = stock.GSM / 1000m;
				return (Convert.ToDecimal(w * h) / 1000000) * stockWeight * qty;
			}
			catch (Exception ex)
			{
				var m = ex.Message;
			}

			return 0;
		}

		private int GetExtraPrint()
		{
			if (_configApp == null) return 0;
			return Convert.ToInt32(_configApp.GetValue(Configuration.FreightExtraPrint));
		}

		private int GetExtraPrintByType(JobTypeOptions jobType)
		{
			if (jobType != BusinessCard && jobType != BusinessCardNdd && jobType != BusinessCardSdd && jobType != PresentationFolder)
			{
				return GetExtraPrint();
			}

			return 0;
		}

		public static decimal RoundUp(decimal input, int places = 1)
		{
			var multiplier = Convert.ToDecimal(Math.Pow(10, Convert.ToDouble(places)));
			return Math.Ceiling(input * multiplier) / multiplier;
		}
	}

	public class Box
	{
		public static int Fits(string s)
		{
			// 
			var t = s.Split(new[] { ' ', '\t', ',' }, StringSplitOptions.RemoveEmptyEntries);
			var x = t.Select(z => Int32.Parse(z)).ToArray();
			return Fits(x[0], x[1], x[2], x[3], x[4], x[5]);
		}

		public static int Fits(decimal a, decimal b, decimal c, decimal d, decimal e, decimal f)
		{
			try
			{
				IEnumerable<T[]> GetPermutations2<T>(T x, T y, T z) where T : IComparable
				{
					yield return new[] { x, y, z };
					yield return new[] { x, z, y };

					yield return new[] { y, x, z };
					yield return new[] { y, z, x };

					yield return new[] { z, x, y };
					yield return new[] { z, y, x };
				}

				int _(decimal x, decimal y)
				{
					//$"{x} {y} {(int)Math.Floor(x / (decimal)y)}".Dump();
					return (int)Math.Floor(x / (decimal)y);
				}

				var q = from y in GetPermutations2(a, b, c)
						from x in GetPermutations2(d, e, f)
						select _(x[0], y[0]) * _(x[1], y[1]) * _(x[2], y[2]);

				var r = q.Max();
				return r;
			}
			catch (Exception ex)
			{
				var m = ex.Message;
				throw;
			}
		}
	}
}
