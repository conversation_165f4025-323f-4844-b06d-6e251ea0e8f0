using lep.email;
using lep.job;
using lep.user;

using Serilog;
using NHibernate;
using System;
using System.Collections.Generic;
using System.Reflection;
using static lep.job.JobTypeOptions;
using System.Linq;
using lep.configuration;

namespace lep.run.impl
{
	public class RunEngine : IInitializingObject
	{
		private IEmailApplication emailApplication;
		private bool initialised;
		private IJobApplication jobApplication;
		private IRunApplication runApplication;
		private IUserApplication userApp;

		private ISession session;
		// private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

 		public RunEngine(IEmailApplication emailApplication, IJobApplication jobApplication, IRunApplication runApplication, IUserApplication userApp, ISession session)
		{
			this.emailApplication = emailApplication;
			this.jobApplication = jobApplication;
			this.runApplication = runApplication;
			this.userApp = userApp;
			this.session = session;

			AfterPropertiesSet();
		}

 
		public IRunApplication RunApplication
		{
			set { runApplication = value; }
		}

		public IJobApplication JobApplication
		{
			set { jobApplication = value; }
		}

		public IEmailApplication EmailApplication
		{
			set { emailApplication = value; }
		}

		public void AfterPropertiesSet()
		{
			if (runApplication == null)
			{
				throw new ArgumentNullException("runApplication");
			}
			if (jobApplication == null)
			{
				throw new ArgumentNullException("jobApplication");
			}
			initialised = true;
		}

		private void SaveJob(IJob job)
		{
			var run = GetMatchRun(job, runApplication.FindRunsForJob(job));

			// if no run found or usable, create a new one from this job
			if (run == null)
			{
				run = runApplication.NewRun(job);
			}

			run.AddJob(job, userApp.GetSystemUser());

			// save run and job
			runApplication.Save(run);
		}

		private IRun GetMatchRun(IJob job, IList<IRun> runs)
		{
			if (runs == null) return null;

			foreach (var r in runs)
			{
				if (r.CanAccept(job, out var needforce, out var oversize, out var message))
				{
					if (!needforce)
					{
						return r;
					}
				}
			}

			return null;
		}

		/// <summary>
		/// This method handles creating and filling Runs with Jobs.
		/// It is called by a scheduler.
		///
		/// Runs are filled with Jobs with identical parameters or stock, celloglazing etc.
		/// </summary>
		///

		public void CronTask()
		{
			if (!initialised)
			{
				throw new ApplicationException("RunEngine not initialised");
			}
			try
			{
				Log.Information("RunEngine Auto allocating jobs to Run");
				FixJobsWithMissingRun();

				// if currrent time is 2.15 
				// then change a flag somewhere to disable the rest of the code
				// with a button 
				// to 6pm then skip
				
				// Insert Into [dbo].[Configuration] values('AutoRunAllocationEnabled', 'Y')

				//var now = DateTime.Now.TimeOfDay;
				//if ((now > TimeSpan.Parse("14:15")) && (now < TimeSpan.Parse("14:25")))
				//{
				//	session.CreateSQLQuery("Update Configuration set DefaultValue = 'N' where code = 'AutoRunAllocationEnabled'").ExecuteUpdate();
				//}
			 
				bool isAutoRunAllocationEnabled = session.CreateSQLQuery("select DefaultValue from Configuration where code = 'AutoRunAllocationEnabled'").UniqueResult<string>() == "Y";
				if (!isAutoRunAllocationEnabled)
				{
					Log.Information("Skipping auto run allocation");
					return;
				}
					

				AllocateJobs(Facility.FG);
				AllocateJobs(Facility.PM);
			}
			catch (Exception ex)
			{
				var m = ex.Message;
				Log.Error(m, ex);
			}
		}

		private void FixJobsWithMissingRun()
		{
			try
			{
				string fixSql = @"Update Job Set[Status] = 'PreflightDone'
							Where Id in (
							SELECT  Job.Id
							FROM    Job LEFT OUTER JOIN
								RunJob ON Job.Id = RunJob.JobId
							WHERE RunJob.RunId is null
							and job.Status = 'InRun'
							and job.[StatusDate] >  DATEADD(day, -1, getdate())
							)";

				session.CreateSQLQuery(fixSql).ExecuteUpdate();
			}
			catch (Exception ex)
			{
				Log.Error(ex.Message);
			}
		}

		private void AllocateJobs(Facility facility)
		{
			// locate any Jobs ready to go into Runs.
			var readyjobs = jobApplication.FindReadyJobs(facility, null, null);

			readyjobs = readyjobs.Where(_ => _.IsBusinessCard()).ToList();

			if (readyjobs == null)
			{
				return;
			}

			var sysUser = userApp.GetSystemUser();

			Log.Information(facility.ToString() + " found " + (readyjobs.Count).ToString() + " Jobs");

			var bcRuns = new Dictionary<int, IList<IRun>>();
			var otherjobs = new List<IJob>();

			// Process each Job to attempt to find an existing run it can be added
			// to or create a new Run matching the Jobs properties.
			foreach (var job in readyjobs)
			{
				if (job.Template.Is(BusinessCard, BusinessCardNdd, BusinessCardSdd, Postcard))
				{

					if (job.IsSDD())
						continue;

					IRun matchR = null;
					if (bcRuns.ContainsKey(job.Id))
					{
						matchR = GetMatchRun(job, bcRuns[job.Id]);
					}
					else
					{
						bcRuns.Add(job.Id, new List<IRun>());
					}
					if (matchR == null)
					{
						matchR = runApplication.NewRun(job);
						bcRuns[job.Id].Add(matchR);
					}
					matchR.AddJob(job, sysUser, addComment: false);
					// addComment: false for LORD-661 Job status oscillating between in run and preflight done

				}
				//else {
				//if (job.Template.IsNot(Notepads, Magazine, MagazineSeparate)
				//	 && job.Stock.Name.Is("80 GSM Uncoated", "90 GSM Uncoated", "100 GSM Uncoated" ,
				//						  "80 GSM Recycled Uncoated", "120 GSM Uncoated", "Magnet Back")
				//	 && job.FinishedSize.PaperSize.Name.IsNot("8pp A4", "6pp A4", "8pp DL")
				//	) {
				//	otherjobs.Add(job);
				//}
				//}
			}

			IList<IList<IJob>> splitedLists = new List<IList<IJob>>();

			foreach (var runs in bcRuns.Values)
			{
				foreach (var r in runs)
				{
					IList<IJob> jobs = new List<IJob>();
					while (r.Jobs.Count > 0)
					{
						var j = r.Jobs[0];
						r.RemoveJob(j, sysUser, addComment: false);
						// addComment: false for LORD-661 Job status oscillating between in run and preflight done

						jobs.Add(j);
					}
					splitedLists.Add(jobs);
				}
			}

			foreach (var jobs in splitedLists)
			{
				if (jobs.Count == 1)
				{
					SaveJob(jobs[0]);
				}
				else
				{
					var slots = 0;
					foreach (var j in jobs)
					{
						slots += j.CalculateJobSize();
					}
					IRun finalR = null;
					foreach (var possibleRun in runApplication.FindRunsForJob(jobs[0]))
					{
						if (possibleRun.CalculateUsedBC() + slots <= 42)
						{
							finalR = possibleRun;
							break;
						}
					}
					if (finalR == null)
					{
						finalR = runApplication.NewRun(jobs[0]);
					}
					foreach (var j in jobs)
					{
						finalR.AddJob(j, sysUser);
					}
					runApplication.Save(finalR);
				}
			}

			foreach (var job in otherjobs)
			{
				// attempt to locate a Run to put this in
				SaveJob(job);
			}
		}
	}
}
