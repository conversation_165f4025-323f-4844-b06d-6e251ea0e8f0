﻿namespace lep.jobmonitor.impl
{
	/* public class JobBoardQueueHelper
	 {
		 static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		 public string JobBoardEventsQueueName { protected get; set; }

		 public MessageQueue GetQueueForSending()
		 {
			 try
			 {
				 MessageQueue messageQueue = null;

				 if (MessageQueue.Exists(JobBoardEventsQueueName))
				 {
					 messageQueue = new MessageQueue(JobBoardEventsQueueName, QueueAccessMode.Send);
				 }
				 else
				 {
					 messageQueue = MessageQueue.Create(JobBoardEventsQueueName);
				 }

				 messageQueue.Formatter = new XmlMessageFormatter(new Type[] {typeof(JobBoardEvent)});
				 return messageQueue;
			 }
			 catch (Exception ex)
			 {
				 Log.Error(ex.Message, ex);
				 return null;
			 }
		 }

		 public MessageQueue GetQueueForReading()
		 {
			 try
			 {
				 MessageQueue messageQueue = null;

				 if (MessageQueue.Exists(JobBoardEventsQueueName))
				 {
					 messageQueue = new MessageQueue(JobBoardEventsQueueName, QueueAccessMode.Receive);
				 }
				 else
				 {
					 messageQueue = MessageQueue.Create(JobBoardEventsQueueName);
				 }

				 messageQueue.Formatter = new XmlMessageFormatter(new Type[] {typeof(JobBoardEvent)});

				 return messageQueue;
			 }
			 catch (Exception ex)
			 {
				 Log.Error(ex.Message, ex);
				 return null;
			 }
		 }
	 }
	 */
}