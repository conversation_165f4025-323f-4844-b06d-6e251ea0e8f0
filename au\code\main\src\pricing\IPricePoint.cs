using lep.job;
using lep.user;
using System;

namespace lep.pricing
{
	public interface IPricePoint
    {
        int Id { get; set; }
        IJobTemplate Template { get; set; }
        IStock Stock { get; set; }
        IPaperSize PaperSize { get; set; }
        int NumPages { get; set; }
        int NumColourSides { get; set; }
        string Celloglazing { get; set; }
        int Quantity { get; set; }
        decimal Price { get; set; }
        string MYOB { get; set; }
        IUser ChangeBy { get; set; }
        SiteLocation SiteLocation { get; set; }
        PrintType PrintType { get; set; }
        DateTime DateCreated { get; set; }
        DateTime DateModified { get; set; }
    }
}