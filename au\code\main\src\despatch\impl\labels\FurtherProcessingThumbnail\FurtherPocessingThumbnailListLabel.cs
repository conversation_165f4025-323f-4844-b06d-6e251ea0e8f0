using lep.configuration;
using lep.extensionmethods;
using lep.job;
using RoundedRectangles;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Printing;
using System.Drawing.Text;
using System.IO;
using System.Linq;
using System.Reflection;
using Serilog;

namespace lep.despatch.impl.label
{
	public class FurtherPocessingThumbnailListLabel : BaseFurtherProcessingLabel
	{
		public IList<IJob> Jobs { get; set; }



		#region Constructors

		public FurtherPocessingThumbnailListLabel(IList<IJob> jobs, string printerAndTray, IConfigurationApplication configurationApplication, string filename)
		{
			this.Jobs = jobs;
			this.PrinterAndTray = printerAndTray;
			this.ConfigurationApplication = configurationApplication;
			this.PrintFileName = filename;
		}

		#endregion Constructors

		#region Protected Methods

		private int currentJob = 0;

		protected override void OnPrintPage(PrintPageEventArgs e)
		{
			if (!specialInstructionsOnNextPage)
			{
				Job = Jobs[currentJob++];
				pageNumber = 0;
			}

			base.OnPrintPage(e);
			var g = e.Graphics;

			// Set graphics unit to millimeters
			g.PageUnit = GraphicsUnit.Millimeter;

			SetupGraphics(g);

			pageNumber++;
			if (pageNumber == 1)
			{
				#region title

				var title = Job.GetJobTemplateName();
				g.DrawString(title, titleFont, Brushes.Black, rTitle, titleAlignment);

				#endregion title

				#region print further processing instruction in top part

				var strMiddle = processingText.ToString();

				var hMiddle = (int)g.MeasureString(strMiddle, defaultFont).Height;
				var rMiddle = new Rectangle(left, top + hTitle + 10, width, (270 - top - hTitle - 10));
				g.DrawString(strMiddle, defaultFont, Brushes.Black, rMiddle, middleFormat);

				#endregion print further processing instruction in top part

				#region draw basic info like cust, job, order

				var basicJobInformation = basicInformation.ToString();

				//Rectangle rBasicInfo = new Rectangle(40, 284, 310, 100);
				//g.DrawRectangle(penBlack3, rBasicInfo);

				var hBasicInfo = (int)g.MeasureString(basicJobInformation, defaultFont).Height;

				g.DrawString(basicJobInformation, defaultFont, Brushes.Black, rBasicInfoTxt, middleFormat);

				#endregion draw basic info like cust, job, order

				DrawRoundedCorndersBox(g);
				DrawThumbnail(g);
				DrawEDD(g);
				DrawBarcode(g);
			}

			#region draw special instructions

			DrawSpecialInstructions(e, g);

			#endregion draw special instructions

			if (currentJob == Jobs.Count && !specialInstructionsOnNextPage)
				e.HasMorePages = false;
			else
			{
				e.HasMorePages = true;
			}
		}

		#endregion Protected Methods

		#region Private Methods

		public override void FormatPrintContent()
		{
			basicInformation.Clear();
			processingText.Clear();
			strInstructions = "";
			try
			{
				// Strings to Print from Job Proerties
				FormatBasicInformation();
				processingText.AppendFormat(fmt, "Courier", Job.Order.Courier).AppendLine();
				if (Job.SendSamples == true)
				{
					processingText.AppendLine("*** Send Samples ***");
				}

				if (Job.Runs.Count > 0)
				{
					processingText.AppendFormat(fmt, "Run #", Job.Runs[0].RunNr).AppendLine();
				}

				processingText.AppendFormat(fmt, "Size", Job.GetJobSize()).AppendLine();

				if (Job.HoleDrilling != HoleDrilling.None)
				{
					processingText.AppendFormat(fmt, "Hole Drilling ", Job.HoleDrilling.ToDescription()).AppendLine();
					if (Job.NumberOfHoles != null && Job.NumberOfHoles > 0)
					{
						processingText.AppendFormat(fmt, "Number Of Holes", Job.NumberOfHoles).AppendLine();
					}
				}

				if (Job.RoundOption != RoundOption.None)
				{
					processingText.AppendFormat(fmt, "Round Corner", Job.RoundOption.ToDescription()).AppendLine();

					if (Job.RoundOption == RoundOption.Custom)
					{
						processingText.AppendLine(Job.CustomDieCut);
					}
					else if (Job.RoundDetailOption != RoundDetailOption.None)
					{
						processingText.AppendLine(Job.RoundDetailOption.ToDescription());
					}
				}

				if (Job.DieCutType != CutOptions.None)
				{
					processingText.AppendFormat(fmt, "Diecut", Job.DieCutType.ToDescription()).AppendLine();
				}

				//if (Job.Scoring) {
				//    processingText.AppendFormat(fmt, "Scoring", Job.ScoringInstructions).AppendLine();
				//}

				if (Job.FoldedSize != null)
				{
					var size = Job.FoldedSize.Height.ToString() + "x" + Job.FoldedSize.Width.ToString();
					var title = Job.IsBusinessCard() ? "Scoring" : "Folding";
					processingText.AppendFormat(fmt, title, size).AppendLine();
				}

				if ( Job.NumberOfMagnets > 0)
				{
					processingText.AppendFormat(fmt, "Magnet", Job.NumberOfMagnets).AppendLine();
				}

				FormatCommonSpecialInstructions();

				// Add list-specific special instructions
				if(Job.Facility == Facility.FG)
				{
					strInstructions += (Job.Freight?.Packages?.ToString() ?? "");
				}

				string t = Job.GetDigitalJobMailHouseInstuctions();
				strInstructions = strInstructions + t;

				if (Job.Order.PackWithoutPallets)
				{
					strInstructions += "\n***Pack Loose Cartons***";
				}
			}
			catch (Exception ex)
			{
				Log.Error(ex.Message, ex);
				throw;
			}
		}

		#endregion Private Methods
	}
}
