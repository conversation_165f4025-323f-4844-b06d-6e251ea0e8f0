using lep.job;
using lep.run;
using System;
using System.Collections.Generic;
using System.Linq;
using static lep.job.JobStatusOptions;
using static lep.job.JobTypeOptions;

namespace lep.jobmonitor
{
	using JSO = JobStatusOptions;
	using JT = JobTypeOptions;
	using RoutingLookupTableEx = Dictionary<JobTypeOptions, List<JobStatusOptions>>;

	/// <summary>
	/// Standard Routing Class
	/// All Jobs go through a list predetermined steps in the production line defined by their template and processings involved.
	/// Some steps can be skipped if a job does not require it.
	/// </summary>
	public sealed class StandardRouting
	{
		// static holder for instance, need to use lambda to construct since constructor private
		private static readonly Lazy<StandardRouting> _instance = new Lazy<StandardRouting>(() => new StandardRouting());

		// accessor for instance
		public static StandardRouting Instance => _instance.Value;

		private StandardRouting()
		{
		}

		private JSO GetNextRouteForAGivenTemplateStatusAndFolding(JT type, JSO status, bool folding)
		{
			if (status == UnableToMeetPrice)
				return JSO.Open;
			if (status == RejectedVariation)
				return JSO.Open;


			var result = Complete;
			var routingChain = GetStandardRouteListForAGivenJobTemplateAndFolding(type, folding);
			var routingPointsToConsider = routingChain.SkipWhile(x => (int)x < (int)status).ToList();

			//if (printType == PrintType.D) {
			//    routingPointsToConsider = routingPointsToConsider.SkipWhile(x =>  x > JSO.PreflightDone && x < JSO.Packed).ToList();
			//}

			// remove the first in sequence if the first is given status
			if (routingPointsToConsider.First() == status)
			{
				routingPointsToConsider.RemoveAt(0);
			}

			while (routingPointsToConsider.Contains(status))
			{
				routingPointsToConsider.RemoveAt(0);
			}

			// above is done like that cos in the routing table some statuses occur out of sequence.
			// eg. for PresentationFolder/GolfScoreCards have Letterpressed then Cut which is out of default ordering of the enum

			result = routingPointsToConsider.FirstOrDefault();
			if (result == Open)
				result = Complete;
			return result;
		}

		#region Fields

		// routes for Brochure types with folding
		// routes for Brochure types with folding
		private readonly RoutingLookupTableEx _routeForBrochureWithFolding = new RoutingLookupTableEx
		{
			{ Brochure,        new List<JSO>{ Open,Submitted,PreflightDone,Filling,LayoutRequired,LayoutDone,ApprovedForPlating,PlatingDone,PressDone,DPCPreProduction,DPCPrinted,Celloglazed,Cut,Folded,DPCComplete,Outwork,Packed,Dispatched,Complete} },
			{ TentCalendars,   new List<JSO>{ Open,Submitted,PreflightDone,Filling,LayoutRequired,LayoutDone,ApprovedForPlating,PlatingDone,PressDone,DPCPreProduction,DPCPrinted,Celloglazed,Letterpressed,Cut,Folded,DPCComplete,Outwork,Packed,Dispatched,Complete} },
			{ DLCalendars,     new List<JSO>{ Open,Submitted,PreflightDone,Filling,LayoutRequired,LayoutDone,ApprovedForPlating,PlatingDone,PressDone,DPCPreProduction,DPCPrinted,Celloglazed,Cut,Folded,DPCComplete,Outwork,Packed,Dispatched,Complete} },
			{ GreetingCards,   new List<JSO>{ Open,Submitted,PreflightDone,Filling,LayoutRequired,LayoutDone,ApprovedForPlating,PlatingDone,PressDone,DPCPreProduction,DPCPrinted,Celloglazed,Cut,Folded,DPCComplete,Outwork,Packed,Dispatched,Complete} },

			{ BrochureSpecial, new List<JSO>{ Open,Submitted,PreflightDone,Filling,LayoutRequired,LayoutDone,ApprovedForPlating,PlatingDone,PressDone,DPCPreProduction,DPCPrinted,Celloglazed,Cut,Folded,DPCComplete,Outwork,Packed,Dispatched,Complete} },
			{ BrochureNDD,     new List<JSO>{ Open,Submitted,PreflightDone,Filling,LayoutRequired,LayoutDone,ApprovedForPlating,PlatingDone,PressDone,DPCPreProduction,DPCPrinted,Celloglazed,Cut,Folded,DPCComplete,Outwork,Packed,Dispatched,Complete} },
			{ BrochureSDD,     new List<JSO>{ Open,Submitted,PreflightDone,Filling,LayoutRequired,LayoutDone,ApprovedForPlating,PlatingDone,PressDone,DPCPreProduction,DPCPrinted,Celloglazed,Cut,Folded,DPCComplete,Outwork,Packed,Dispatched,Complete} },
		};

		// routes for Brochuere types with no folding
		private readonly RoutingLookupTableEx _routeForBrochureWithoutFolding = new RoutingLookupTableEx
		{
			{ Brochure,        new List<JSO> { Open,Submitted,PreflightDone,Filling,LayoutRequired,LayoutDone,ApprovedForPlating,PlatingDone,PressDone,DPCPreProduction,DPCPrinted,Celloglazed,Cut,DPCComplete,Outwork,Packed,Dispatched,Complete               }},
			{ TentCalendars,   new List<JSO> { Open,Submitted,PreflightDone,Filling,LayoutRequired,LayoutDone,ApprovedForPlating,PlatingDone,PressDone,DPCPreProduction,DPCPrinted,Celloglazed,Letterpressed,Cut,DPCComplete,Outwork,Packed,Dispatched,Complete }},
			{ DLCalendars,     new List<JSO> { Open,Submitted,PreflightDone,Filling,LayoutRequired,LayoutDone,ApprovedForPlating,PlatingDone,PressDone,DPCPreProduction,DPCPrinted,Celloglazed,Cut,DPCComplete,Outwork,Packed,Dispatched,Complete               }},
			{ GreetingCards,   new List<JSO> { Open,Submitted,PreflightDone,Filling,LayoutRequired,LayoutDone,ApprovedForPlating,PlatingDone,PressDone,DPCPreProduction,DPCPrinted,Celloglazed,Cut,DPCComplete,Outwork,Packed,Dispatched,Complete               }},

			{ BrochureSpecial,           new List<JSO> { Open,Submitted,PreflightDone,Filling,LayoutRequired,LayoutDone,ApprovedForPlating,PlatingDone,PressDone,DPCPreProduction,DPCPrinted,Celloglazed,Cut,DPCComplete,Outwork,Packed,Dispatched,Complete     }},
			{ BrochureNDD,               new List<JSO> { Open,Submitted,PreflightDone,Filling,LayoutRequired,LayoutDone,ApprovedForPlating,PlatingDone,PressDone,DPCPreProduction,DPCPrinted,Celloglazed,Cut,DPCComplete,Outwork,Packed,Dispatched,Complete     }},
			{ BrochureSDD,               new List<JSO> { Open,Submitted,PreflightDone,Filling,LayoutRequired,LayoutDone,ApprovedForPlating,PlatingDone,PressDone,DPCPreProduction,DPCPrinted,Celloglazed,Cut,DPCComplete,Outwork,Packed,Dispatched,Complete     }},
		};

		// routes for all job types other than folding
		private readonly RoutingLookupTableEx _routesForAllOtherJobTypes = new RoutingLookupTableEx
		{
			{ BusinessCard,              new List<JSO> { Open,Submitted,PreflightDone,Filling,LayoutRequired,LayoutDone,ApprovedForPlating,PlatingDone,PressDone,DPCPreProduction,DPCPrinted,Celloglazed,Cut,Drilled,Rounded,DPCComplete,Outwork,Packed,Dispatched,Complete                      }},
			{ BusinessCardNdd,           new List<JSO> { Open,Submitted,PreflightDone,Filling,LayoutRequired,LayoutDone,ApprovedForPlating,PlatingDone,PressDone,DPCPreProduction,DPCPrinted,Celloglazed,Cut,Drilled,Rounded,DPCComplete,Outwork,Packed,Dispatched,Complete                      }},
			{ BusinessCardSdd,           new List<JSO> { Open,Submitted,PreflightDone,Filling,LayoutRequired,LayoutDone,ApprovedForPlating,PlatingDone,PressDone,DPCPreProduction,DPCPrinted,Celloglazed,Cut,Drilled,Rounded,DPCComplete,Outwork,Packed,Dispatched,Complete                      }},
			{ DoubleBusinessCard,        new List<JSO> { Open,Submitted,PreflightDone,Filling,LayoutRequired,LayoutDone,ApprovedForPlating,PlatingDone,PressDone,DPCPreProduction,DPCPrinted,Celloglazed,Cut,Scored,Letterpressed,Drilled,Rounded,DPCComplete,Outwork,Packed,Dispatched,Complete }},
			{ DL,                        new List<JSO> { Open,Submitted,PreflightDone,Filling,LayoutRequired,LayoutDone,ApprovedForPlating,PlatingDone,PressDone,DPCPreProduction,DPCPrinted,Celloglazed,Cut,Folded,DPCComplete,Outwork,Packed,Dispatched,Complete                               }},
			{ DLSpecial,                 new List<JSO> { Open,Submitted,PreflightDone,Filling,LayoutRequired,LayoutDone,ApprovedForPlating,PlatingDone,PressDone,DPCPreProduction,DPCPrinted,Celloglazed,Cut,DPCComplete,Outwork,Packed,Dispatched,Complete                                      }},
			{ GolfScoreCards,            new List<JSO> { Open,Submitted,PreflightDone,Filling,LayoutRequired,LayoutDone,ApprovedForPlating,PlatingDone,PressDone,DPCPreProduction,DPCPrinted,Letterpressed,Cut,DPCComplete,Outwork,Packed,Dispatched,Complete                                    }},
			{ Letterhead,                new List<JSO> { Open,Submitted,PreflightDone,Filling,LayoutRequired,LayoutDone,ApprovedForPlating,PlatingDone,PressDone,DPCPreProduction,DPCPrinted,Cut,DPCComplete,Outwork,Packed,Dispatched,Complete                                                  }},
			{ LetterheadNDD,             new List<JSO> { Open,Submitted,PreflightDone,Filling,LayoutRequired,LayoutDone,ApprovedForPlating,PlatingDone,PressDone,DPCPreProduction,DPCPrinted,Cut,DPCComplete,Outwork,Packed,Dispatched,Complete                                                  }},
			{ LetterheadSDD,             new List<JSO> { Open,Submitted,PreflightDone,Filling,LayoutRequired,LayoutDone,ApprovedForPlating,PlatingDone,PressDone,DPCPreProduction,DPCPrinted,Cut,DPCComplete,Outwork,Packed,Dispatched,Complete                                                  }},
			{ Magazine,                  new List<JSO> { Open,Submitted,PreflightDone,Filling,LayoutRequired,LayoutDone,ApprovedForPlating,PlatingDone,PressDone,DPCPreProduction,DPCPrinted,Folded,Stitched,DPCComplete,Outwork,Packed,Dispatched,Complete                                      }},
			{ A4CalendarSelfCover,       new List<JSO> { Open,Submitted,PreflightDone,Filling,LayoutRequired,LayoutDone,ApprovedForPlating,PlatingDone,PressDone,DPCPreProduction,DPCPrinted,Folded,Stitched,DPCComplete,Outwork,Packed,Dispatched,Complete                                      }},
			{ MagazineNDD,               new List<JSO> { Open,Submitted,PreflightDone,Filling,LayoutRequired,LayoutDone,ApprovedForPlating,PlatingDone,PressDone,DPCPreProduction,DPCPrinted,Folded,Stitched,DPCComplete,Outwork,Packed,Dispatched,Complete                                      }},
			{ MagazineSeparate,          new List<JSO> { Open,Submitted,PreflightDone,Filling,LayoutRequired,LayoutDone,ApprovedForPlating,PlatingDone,PressDone,DPCPreProduction,DPCPrinted,Celloglazed,Folded,Stitched,DPCComplete,Outwork,Packed,Dispatched,Complete                          }},
			{ A4CalendarSeparateCover,   new List<JSO> { Open,Submitted,PreflightDone,Filling,LayoutRequired,LayoutDone,ApprovedForPlating,PlatingDone,PressDone,DPCPreProduction,DPCPrinted,Celloglazed,Folded,Stitched,DPCComplete,Outwork,Packed,Dispatched,Complete                          }},
			{ Notepads,                  new List<JSO> { Open,Submitted,PreflightDone,Filling,LayoutRequired,LayoutDone,ApprovedForPlating,PlatingDone,PressDone,DPCPreProduction,DPCPrinted,Cut,DPCComplete,Outwork,Packed,Dispatched,Complete                                                  }},
			{ Stationery,                new List<JSO> { Open,Submitted,PreflightDone,Filling,LayoutRequired,LayoutDone,ApprovedForPlating,PlatingDone,PressDone,DPCPreProduction,DPCPrinted,Cut,DPCComplete,Outwork,Packed,Dispatched,Complete                                                  }},
			{ StationerySDD,             new List<JSO> { Open,Submitted,PreflightDone,Filling,LayoutRequired,LayoutDone,ApprovedForPlating,PlatingDone,PressDone,DPCPreProduction,DPCPrinted,Cut,DPCComplete,Outwork,Packed,Dispatched,Complete                                                  }},
			{ Postcard,                  new List<JSO> { Open,Submitted,PreflightDone,Filling,LayoutRequired,LayoutDone,ApprovedForPlating,PlatingDone,PressDone,DPCPreProduction,DPCPrinted,Celloglazed,Cut,DPCComplete,Outwork,Packed,Dispatched,Complete                                      }},
			{ Poster,                    new List<JSO> { Open,Submitted,PreflightDone,Filling,LayoutRequired,LayoutDone,ApprovedForPlating,PlatingDone,PressDone,DPCPreProduction,DPCPrinted,Celloglazed,Cut,DPCComplete,Outwork,Packed,Dispatched,Complete                                      }},
			{ PresentationFolder,        new List<JSO> { Open,Submitted,PreflightDone,Filling,LayoutRequired,LayoutDone,ApprovedForPlating,PlatingDone,PressDone,DPCPreProduction,DPCPrinted,Celloglazed,Letterpressed,Cut,DPCComplete,Outwork,Packed,Dispatched,Complete                        }},
			{ Compliments,               new List<JSO> { Open,Submitted,PreflightDone,Filling,LayoutRequired,LayoutDone,ApprovedForPlating,PlatingDone,PressDone,DPCPreProduction,DPCPrinted,Cut,DPCComplete,Outwork,Packed,Dispatched,Complete                                                  }},
			{ ComplimentsNDD,            new List<JSO> { Open,Submitted,PreflightDone,Filling,LayoutRequired,LayoutDone,ApprovedForPlating,PlatingDone,PressDone,DPCPreProduction,DPCPrinted,Cut,DPCComplete,Outwork,Packed,Dispatched,Complete                                                  }},
			{ ComplimentsSDD,            new List<JSO> { Open,Submitted,PreflightDone,Filling,LayoutRequired,LayoutDone,ApprovedForPlating,PlatingDone,PressDone,DPCPreProduction,DPCPrinted,Cut,DPCComplete,Outwork,Packed,Dispatched,Complete                                                  }},
			{ PresentationFolderNDD,     new List<JSO> { Open,Submitted,PreflightDone,Filling,LayoutRequired,LayoutDone,ApprovedForPlating,PlatingDone,PressDone,DPCPreProduction,DPCPrinted,Celloglazed,Letterpressed,Cut,DPCComplete,Outwork,Packed,Dispatched,Complete                        }},
			{ FridgeMagnet,              new List<JSO> { Open,Submitted,PreflightDone,Filling,LayoutRequired,LayoutDone,ApprovedForPlating,PlatingDone,PressDone,DPCPreProduction,DPCPrinted,Celloglazed,Cut,DPCComplete,Outwork,Packed,Dispatched,Complete                                      }},
			{ PullUpBannerStandardStand, new List<JSO> { Open,Submitted,PreflightDone,WideFormatProduction,WideFormatComplete,Packed,Dispatched,Complete                                                                                                                           }},
			{ PullUpBannerPremiumStand,  new List<JSO> { Open,Submitted,PreflightDone,WideFormatProduction,WideFormatComplete,Packed,Dispatched,Complete                                                                                                                           }},
			{ BacklitPosters ,           new List<JSO> { Open,Submitted,PreflightDone,WideFormatProduction,WideFormatComplete,Packed,Dispatched,Complete                                                                                                                           }},
			{ MeshBanner ,               new List<JSO> { Open,Submitted,PreflightDone,WideFormatProduction,WideFormatComplete,Packed,Dispatched,Complete                                                                                                                           }},
			{ VinylOutdoor ,             new List<JSO> { Open,Submitted,PreflightDone,WideFormatProduction,WideFormatComplete,Packed,Dispatched,Complete                                                                                                                           }},
			{ PosterMattArt ,           new List<JSO> { Open,Submitted,PreflightDone,WideFormatProduction,WideFormatComplete,Packed,Dispatched,Complete                                                                                                                           }},
			{ PosterCanvas ,            new List<JSO> { Open,Submitted,PreflightDone,WideFormatProduction,WideFormatComplete,Packed,Dispatched,Complete                                                                                                                           }},
			{ VinylSticker ,            new List<JSO> { Open,Submitted,PreflightDone,WideFormatProduction,WideFormatComplete,Packed,Dispatched,Complete                                                                                                                           }},
			{ RemovableWallDecals ,     new List<JSO> { Open,Submitted,PreflightDone,WideFormatProduction,WideFormatComplete,Packed,Dispatched,Complete                                                                                                                           }},
			{ VinylStickerOutdoor ,     new List<JSO> { Open,Submitted,PreflightDone,WideFormatProduction,WideFormatComplete,Packed,Dispatched,Complete                                                                                                                           }},
			{ RigidSigns ,              new List<JSO> { Open,Submitted,PreflightDone,WideFormatProduction,WideFormatComplete,Packed,Dispatched,Complete                                                                                                                           }},
			{ DuplicateNCRBooks ,       new List<JSO> { Open,Submitted,PreflightDone,Outwork,Packed,Dispatched,Complete                                                                                                                                                           }},
			{ TriplicateNCRBooks ,      new List<JSO> { Open,Submitted,PreflightDone,Outwork,Packed,Dispatched,Complete                                                                                                                                                           }},
			{ QuadruplicateNCRBooks ,   new List<JSO> { Open,Submitted,PreflightDone,Outwork,Packed,Dispatched,Complete                                                                                                                                                           }},

			{ EnvelopeBlack ,  new List<JSO> { Open,Submitted,PreflightDone,Outwork,Packed,Dispatched,Complete                                                                                                                                                           }},
			{ Envelope1Pms ,  new List<JSO> { Open,Submitted,PreflightDone,Outwork,Packed,Dispatched,Complete                                                                                                                                                           }},
			{ Envelope2Pms ,   new List<JSO> { Open,Submitted,PreflightDone,Outwork,Packed,Dispatched,Complete                                                                                                                                                           }},
			{ EnvelopeCmyk ,   new List<JSO> { Open,Submitted,PreflightDone,Outwork,Packed,Dispatched,Complete                                                                                                                                                           }},

			{ Custom,                    new List<JSO> { Open,Submitted,PreflightDone,Filling,LayoutRequired,LayoutDone,ApprovedForPlating,PlatingDone,PressDone,DPCPreProduction,DPCPrinted,Celloglazed,Letterpressed,Cut,Folded,DPCComplete,Outwork,Packed,Dispatched,Complete                } }
		};



		private readonly RoutingLookupTableEx _routesForRunGangedDigital = new RoutingLookupTableEx
		{
			{ BusinessCard,              new List<JSO> { Open,Submitted,PreflightDone,Filling,LayoutRequired,LayoutDone,ApprovedForPlating,PlatingDone,PressDone,DPCPreProduction,DPCPrinted,Celloglazed,Cut,Drilled,Rounded,DPCComplete,Outwork,Packed,Dispatched,Complete                      }},
			{ BusinessCardNdd,           new List<JSO> { Open,Submitted,PreflightDone,Filling,LayoutRequired,LayoutDone,ApprovedForPlating,PlatingDone,PressDone,DPCPreProduction,DPCPrinted,Celloglazed,Cut,Drilled,Rounded,DPCComplete,Outwork,Packed,Dispatched,Complete                      }},
			{ BusinessCardSdd,           new List<JSO> { Open,Submitted,PreflightDone,Filling,LayoutRequired,LayoutDone,ApprovedForPlating,PlatingDone,PressDone,DPCPreProduction,DPCPrinted,Celloglazed,Cut,Drilled,Rounded,DPCComplete,Outwork,Packed,Dispatched,Complete                      }},
			{ DoubleBusinessCard,        new List<JSO> { Open,Submitted,PreflightDone,Filling,LayoutRequired,LayoutDone,ApprovedForPlating,PlatingDone,PressDone,DPCPreProduction,DPCPrinted,Celloglazed,Cut,Scored,Letterpressed,Drilled,Rounded,DPCComplete,Outwork,Packed,Dispatched,Complete }},
			{ Postcard,                  new List<JSO> { Open,Submitted,PreflightDone,Filling,LayoutRequired,LayoutDone,ApprovedForPlating,PlatingDone,PressDone,DPCPreProduction,DPCPrinted,Celloglazed,Cut,DPCComplete,Outwork,Packed,Dispatched,Complete                                      }},
		};

		//private readonly RoutingLookupTable routeForBrochureWithFolding;
		//// routes for Brochuere types with no folding
		//private readonly RoutingLookupTable routeForBrochureWithoutFolding;

		//// routes for all job types other than folding
		//private readonly RoutingLookupTable routesForAllOtherJobTypes;

		/// <summary>
		///  dictionary of predicates to check if a job needs processing at some status and if it can skip that status
		///		1. if a status is not in the dictionary then it can be skipped.
		///		2. if a status is in the dictionary then the predicate must satisfy in order to skip a status
		/// </summary>
		private readonly Dictionary<JSO, Predicate<IJob>> _statusCanBeSkipped = new Dictionary<JSO, Predicate<IJob>>
		{
			{ShrinkWrapped       , j => j.IsDigital()},
			{DPCPreProduction    , j => !j.IsDigitalAndRunGanged()},
			{DPCPrinted          , j => !j.IsDigital() ||  (j.IsDigitalAndRunGanged() && j.Status == JSO.Cut)},
			{DPCComplete         , j => !j.IsDigital()},
			{WideFormatProduction, j => !j.IsWideFormat()},
			{WideFormatComplete  , j => !j.IsWideFormat()},
			{Celloglazed         , j => j.Celloglaze == RunCelloglazeOptions.None},
			{Cut                 , j => j.IsMagazine() ||  j.IsDigitalAndRunGanged()},
			{Folded              , j => !j.IsMagazine() && j.FoldedSize == null},
			{Letterpressed       , j => j.DieCutType == CutOptions.None},
			{Stitched            , j => !j.IsMagazine()},
			{Drilled             , j => j.HoleDrilling == HoleDrilling.None},
			{Rounded             , j => j.RoundOption == RoundOption.None},
			{Outwork             , j => j.Status != Outwork },
			{PayMe               , j => j.Status != PayMe }
		};

		#endregion Fields

		#region Public Methods

		public JSO GetNextRouteForJob(IJob j)
		{
			var jTemplateId = (JT)j.Template.Id;
			var status = j.Status;
			var folding = j.FoldedSize != null;
			var isDigital = j.IsDigital();

			if (j.IsDigitalAndRunGanged() && j.Status == LayoutDone)
			{
				return JSO.DPCPreProduction;
			}

			return NextRouteForJob(j, status, jTemplateId, folding);
		}

		public List<JSO> GetAllRoutesForJob(IJob j)
		{
			var jTemplateId = (JT)j.Template.Id;
			var status = Open;
			var folding = j.FoldedSize != null;
			var isDigital = j.IsDigital();

			var result = new List<JSO>() { status };

			while (true)
			{
				status = NextRouteForJob(j, status, jTemplateId, folding);

				result.Add(status);
				if (status == Complete)//|| status == JSO.Outwork
					break;
			}
			//result.Insert(0, JSO.Open);
			return result;
		}

		public List<JSO> GetNextRoutesForJob(IJob j)
		{
			var jTemplateId = (JT)j.Template.Id;
			var status = j.Status;
			var folding = j.FoldedSize != null;

			var result = new List<JSO>() { status };

			while (true)
			{
				status = NextRouteForJob(j, status, jTemplateId, folding);

				result.Add(status);
				if (status == Complete)
					break;
			}

			return result;
		}

		private JobStatusOptions NextRouteForJob(IJob j, JobStatusOptions status, JobTypeOptions jTemplateId, bool folding)
		{
			//if (j.Status == JSO.Outwork) {
			//	return j.Status;
			//}

			//if (j.IsDigitalAndRunGanged())
			//{
			//	switch (status)
			//	{
			//		case PreflightDone: return Filling;
			//		case DPCPreProduction: return DPCPrinted;
			//		case DPCPrinted: return Celloglazed;
			//		case Celloglazed: return Cut;
			//		case Cut: return DPCComplete;
			//		case DPCComplete: return Packed;
			//		case Packed: return Dispatched;
			//	}
			//}
			//else if (j.IsDigital())
			//{
			//	switch (status)
			//	{
			//		case PreflightDone: return DPCPreProduction;
			//		case DPCPreProduction: return DPCPrinted;
			//		case DPCPrinted: return DPCComplete;
			//		case DPCComplete: return Packed;
			//		case Packed: return Dispatched;
			//	}
			//}

			if (j.IsWideFormat())
			{
				switch (status)
				{
					case PreflightDone: return Outwork;
					case Outwork: return Packed;
				}
			}

			while (true)
			{
				status = GetNextRouteForAGivenTemplateStatusAndFolding(jTemplateId, status, folding);

				if (status == Complete)
					break;

				if (_statusCanBeSkipped.ContainsKey(status) && _statusCanBeSkipped[status](j))
					continue;

				break;
			}

			return status;
		}

		private IEnumerable<JobStatusOptions> GetStandardRouteListForAGivenJobTemplateAndFolding(JT type, bool folding)
		{
			List<JSO> routeToUse = null;
			try
			{
				if (type.Is(Brochure, BrochureSpecial, BrochureNDD, BrochureSDD, TentCalendars, DLCalendars, GreetingCards))

				{
					routeToUse = folding ? _routeForBrochureWithFolding[type] : _routeForBrochureWithoutFolding[type];
				}
				else
				{
					routeToUse = _routesForAllOtherJobTypes[type];
				}
			}
			catch (Exception ex)
			{
				var m = type;
			}

			if (routeToUse == null)
			{
				var error = string.Format("No Route defined for Job template: {0}  and folding: {1}", type, folding);
				//throw new Exception(error);
				return _routeForBrochureWithFolding[Brochure];
				// revert above after releasing 1030

			}

			return routeToUse;
		}

		#endregion Public Methods
	}
}
