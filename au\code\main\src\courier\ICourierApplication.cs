
using lep.address;
using lep.freight;
using lep.job;
using lep.order;
using lep.user;

using System;
using System.Collections.Generic;

namespace lep.courier
{
	public interface ICourierApplication
	{
		IList<Rate> GetAvailableRatesBase(Facility? senderFacility,
									IPhysicalAddress deliveryAddress,
									IList<Package> packagesToSend,
									bool packWithoutPallets = false,
									ICustomerUser customer = null);
		void MakeLeastPricedRateZeroBasedOnMargin(double marginRate, List<Rate> ratesResult);

		IList<Rate> GetAvailableRates(IOrder order,
								Facility? facility,
								bool includePickup,
								bool includeOther);

		Dictionary<CourierType, decimal> GetAvailableRates2(IOrder order, Facility? facility);

		IList<CourierType> GetAllCouriers(SiteLocation siteLocation);

		void SetCustomerPreferredCourier(IOrder order, CourierType courier, decimal price);

		dynamic CreateConsignment(IOrder order, Facility? facility, string consignmentPrinterName);
		dynamic SyncConnotes(IOrder order, Facility? facility);

		decimal GetCustomerFreightMarginFromCode(String freightCode);
	}
}
