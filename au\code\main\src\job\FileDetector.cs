using System.Collections.Generic;
using System.IO;
using System.Text;

namespace lep.job
{
	public class FileDetector
    {
        private Dictionary<byte[], ArtworkTypeOptions> types;

        public FileDetector()
        {
            types = new Dictionary<byte[], ArtworkTypeOptions>();
            types.Add(Encoding.ASCII.GetBytes("%PDF-"), ArtworkTypeOptions.PDF);

            types.Add(new byte[] { 137, 80, 78, 71, 13, 10, 26, 10 }, ArtworkTypeOptions.PNG);

            //tiff  MM\x00\x2a
            types.Add(new byte[] { 77, 77, 0, 42 }, ArtworkTypeOptions.TIFF);
            //tiff II\x2a\x00
            types.Add(new byte[] { 73, 73, 42, 0 }, ArtworkTypeOptions.TIFF);

            //jpg \377\330\377\340
            types.Add(new byte[] { 255, 216, 255, 224 }, ArtworkTypeOptions.JPG);
            //jpg \377\330\377\356
            types.Add(new byte[] { 255, 216, 255, 238 }, ArtworkTypeOptions.JPG);

            var quark = Encoding.ASCII.GetBytes("IIXPR3");
            var quarkwithspace = new byte[quark.Length + 2];
            quarkwithspace[0] = 0;
            quarkwithspace[1] = 0;
            quark.CopyTo(quarkwithspace, 2);
            types.Add(quarkwithspace, ArtworkTypeOptions.Quark);

            quark = Encoding.ASCII.GetBytes("IIXPRa");
            quarkwithspace = new byte[quark.Length + 2];
            quarkwithspace[0] = 0;
            quarkwithspace[1] = 0;
            quark.CopyTo(quarkwithspace, 2);
            types.Add(quarkwithspace, ArtworkTypeOptions.Quark);

            quark = Encoding.ASCII.GetBytes("MMXPR3");
            quarkwithspace = new byte[quark.Length + 2];
            quarkwithspace[0] = 0;
            quarkwithspace[1] = 0;
            quark.CopyTo(quarkwithspace, 2);
            types.Add(quarkwithspace, ArtworkTypeOptions.Quark);

            quark = Encoding.ASCII.GetBytes("MMXPRa");
            quarkwithspace = new byte[quark.Length + 2];
            quarkwithspace[0] = 0;
            quarkwithspace[1] = 0;
            quark.CopyTo(quarkwithspace, 2);
            types.Add(quarkwithspace, ArtworkTypeOptions.Quark);

            var eps = Encoding.ASCII.GetBytes("%!PS-Adobe-");
            types.Add(eps, ArtworkTypeOptions.EPS);
            var eps2 = new byte[12];
            eps2[0] = 4;
            eps.CopyTo(eps2, 1);
            //eps \004%!PS-Adobe-
            types.Add(eps2, ArtworkTypeOptions.EPS);

            //esp3.0
            types.Add(new byte[] { 197, 208, 211, 198 }, ArtworkTypeOptions.EPS);

            // Adobe InDesign Document
            var InDesignSignature = new byte[]
            {
                0x06, 0x06, 0xED, 0xF5, // from unix magic file, lachlan
                0xD8, 0x1D, 0x46, 0xe5, //
                0xBD, 0x31, 0xEF, 0xE7, //
                0xFE, 0x74, 0xB7, 0x1D,
                0x44, 0x4F, 0x43, 0x55, 0x4D, 0x45, 0x4E, 0x54 // For DOCUMENT type
            };
            types.Add(InDesignSignature, ArtworkTypeOptions.InDesign);
        }

        public ArtworkTypeOptions GetFileType(Stream stream)
        {
            foreach (var format in types.Keys)
            {
                stream.Position = 0;
                var data = new byte[format.Length];
                stream.Read(data, 0, format.Length);
                if (Compare(format, data))
                {
                    return types[format];
                }
            }
            return ArtworkTypeOptions.Misc;
        }

        private static bool Compare(byte[] x, byte[] y)
        {
            if (x.Length != y.Length)
            {
                return false;
            }
            for (var i = 0; i < x.Length; i++)
            {
                if (x[i] != y[i])
                {
                    return false;
                }
            }
            return true;
        }
    }
}