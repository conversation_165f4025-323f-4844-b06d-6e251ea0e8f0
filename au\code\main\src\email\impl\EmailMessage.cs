namespace lep.email.impl
{
    using System;
    using System.Net.Mail;
    using System.Text;

    public class EmailMessage : IMailMessage
    {
        private int id;
        private string from;
        private string to;
        private string replyTo;
        private string subject;
        private string body;
        private string bCC;
        private bool isBodyHtml;
        private Encoding bodyEncoding = Encoding.Default;
        private MailPriority priority = MailPriority.Normal;

        public EmailMessage()
        {
        }

        public virtual MailMessage ToMailMessage()
        {
            //MailMessage message = new MailMessage( new MailAddress( from ), new MailAddress( to ));
            try
            {
                MailMessage message = new MailMessage();
                message.From = new MailAddress(from);

                to.Split(new char[] { ';' }, StringSplitOptions.RemoveEmptyEntries)
                    .ForEach(_ =>
                    {
                        try
                        {
                            message.To.Add(new MailAddress(_));
                        }
                        catch (Exception ex) { }
                    });

                if (!String.IsNullOrEmpty(bCC))
                {
                    message.Bcc.Add(bCC);
                }

                message.Subject = subject;
                message.Body = body;
                message.IsBodyHtml = isBodyHtml;
                message.BodyEncoding = bodyEncoding;
                message.Priority = priority;
                if (!String.IsNullOrEmpty(replyTo))
                {
                    message.ReplyToList.Add(new MailAddress(replyTo));
                }
                return message;
            }
            catch (Exception)
            {
            }

            return null;
        }

        public virtual int Id
        {
            set { id = value; }
            get { return id; }
        }

        public virtual string ReplyTo
        {
            set { replyTo = value; }
            get { return replyTo; }
        }

        public virtual string From
        {
            set { from = value; }
            get { return from; }
        }

        public virtual string BCC
        {
            set { bCC = value; }
            get { return bCC; }
        }

        public virtual string To
        {
            set { to = value; }
            get { return to; }
        }

        public virtual string Subject
        {
            set { subject = value; }
            get { return subject; }
        }

        public virtual string Body
        {
            set { body = value; }
            get { return body; }
        }

        public virtual bool IsBodyHtml
        {
            set { isBodyHtml = value; }
            get { return isBodyHtml; }
        }

        public virtual Encoding BodyEncoding
        {
            set { bodyEncoding = value ?? Encoding.Default; }
            get { return bodyEncoding; }
        }

        public virtual string BodyEncodingName
        {
            set { bodyEncoding = Encoding.GetEncoding(value); }
            get { return (bodyEncoding ?? Encoding.Default).WebName; }
        }

        public virtual MailPriority Priority
        {
            set { priority = value; }
            get { return priority; }
        }
    }
}