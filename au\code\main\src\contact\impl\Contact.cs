using lep.user;
using System;

namespace lep.contact.impl
{
    [Serializable]
    public class Contact : IContact
    {
        public Contact()
        {
        }

        public Contact(string name, string phone, string mobile, string fax, string areacode, string faxareacode, string email)
        {
            Name = name;
            Phone = phone;
            Mobile = mobile;
            Fax = fax;
            AreaCode = areacode;
            FaxAreaCode = faxareacode;
            Email = email;
        }

        public Contact(IUser user)
        {
            Name = user.FirstName;
            if (user.LastName != null)
            {
                Name = Name + " " + user.LastName;
            }
            Phone = user.Phone;
            Mobile = user.Mobile;
            Fax = "";
            FaxAreaCode = "";
            AreaCode = user.AreaCode;
        }

        #region IContact Members

        public virtual string Name { get; set; } = string.Empty;

        public virtual string Email { get; set; } = string.Empty;
        public virtual bool ReceiveMarketingEmails { get; set; }

        public virtual string Mobile { get; set; } = string.Empty;

        public virtual string AreaCode { get; set; } = string.Empty;
        public virtual string Phone { get; set; } = string.Empty;

		public string PhoneFull() => AreaCode + Phone;

        public virtual string FaxAreaCode { get; set; } = string.Empty;
        public virtual string Fax { get; set; } = string.Empty;

        #endregion IContact Members

        public override bool Equals(object obj)
        {
            if (obj == null) return false;

            Contact other = obj as Contact;

            if (Name == other.Name &&
                AreaCode == other.AreaCode &&
                FaxAreaCode == other.FaxAreaCode &&
                Phone == other.Phone &&
                Mobile == other.Mobile &&
                Fax == other.Fax &&
                Email == other.Email
                )
            {
                return true;
            }
            else
            {
                return false;
            }
        }

		public override int GetHashCode()
		{
			return HashCode.Combine(Name, Email, ReceiveMarketingEmails, Mobile, AreaCode, Phone, FaxAreaCode, Fax);
		}
	}
}
