using lep.email;
using lep.user;
using NHibernate;

namespace lep.quote
{
    /// <summary>
    /// 
    /// </summary>
    public interface IQuoteApplication
    {
        /// <summary>
        /// New Quote factory with minimum required fields.
        /// PRE:
        ///  User logged in - Customer
        ///  Description not null
        /// POST:
        /// </summary>
        /// <param name="customer">The customer requesting the quote</param>
        /// <param name="description">The description provided by the customer</param>
        /// <returns>an initialised IQuote</returns>
        IQuote NewQuote(ICustomerUser customer, string description);

        IQuote GetQuote(int Id);
        void Save(IQuote quote);
        void Delete(IQuote quote);

        ICriteria QuoteCriteria(bool ready, bool used, bool requested, bool rejected);

        ICriteria QuoteCriteria(string customer, string quoteNr, bool request, bool rejected, bool supplied,
            bool completed);

        IMailMessage GenerateNotice(IQuote quote, IMailMessage message);
    }
}