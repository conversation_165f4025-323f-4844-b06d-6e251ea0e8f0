<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2"
				   namespace="lep.run"
				   assembly="lep"
				   auto-import="true"
				   default-cascade="all">

  <class name="IRun" table="run" discriminator-value="null">
    <cache usage="read-write" />
    <id name="Id" type="Int32" unsaved-value="0">
      <generator class="identity" />
    </id>
    <discriminator column="Id" type="Int32" insert="false" />
    <timestamp name="DateModified" column="DateModified" />
    <property name="StartedDate" type="lumen.hibernate.type.DateTimeType, lumen" />
    <property name="Status" column="Status" type="lep.GenericEnum`1[lep.RunStatusOptions], lep" not-null="true" />
    <property name="Facility" column="Facility" type="lep.GenericEnum`1[lep.job.Facility], lep" not-null="true" />
    <property name="PrintType" type="lep.GenericEnum`1[lep.job.PrintType], lep"  />
      
    <many-to-one name="Operator" column="operatorid" class="lep.user.impl.Staff, lep" cascade="none" />
    <bag name="LayoutFiles"  table="runjob" cascade="all-delete-orphan" lazy="true">
      <key column="runid" />
      <one-to-many class="ILayout" />
    </bag>
    <bag name="Slots" table="RunSlot">
      <key column="runid" />
      <composite-element class="lep.run.impl.RunSlot, lep">
        <many-to-one name="Job" class="lep.job.IJob, lep" column="jobid" cascade="none" />
        <property name="Slot" column="Slot"  not-null="false"  type="Int32" />
      </composite-element>
    </bag>
    <property name="BackPrint" column="BackPrint" type="lep.GenericEnum`1[lep.job.JobPrintOptions], lep" not-null="true" />
    <property name="Celloglaze" column="Celloglaze" type="lep.GenericEnum`1[lep.run.RunCelloglazeOptions], lep" not-null="true" />
    <property name="Cmyk" type="YesNo" not-null="true" />
    <property name="IsBusinessCard" type="YesNo" not-null="true" />
    <many-to-one name="Stock" class="lep.job.IStock, lep" not-null="true" cascade="none" />
    <property name="Urgent" type="YesNo" not-null="true" />
    <property name="ManuallyManage" type="YesNo" not-null="true" />
    <property name="FileRemoved" type="YesNo" not-null="true" />
    <property name="FileMoveRequired" type="YesNo" not-null="true" />
    <property name="NumOfPressSheets" type="Int32" not-null="false" />
    <property name="QtyList" not-null="false" />
    <property name="TSlots" not-null="false" />
      
    <property name="ScanCount" type="Int32" not-null="true" />

    <bag name="Jobs" table="runjob" lazy="true" cascade="save-update">
      <key column="runid" />
      <many-to-many column="jobid" class="lep.job.IJob, lep" not-found="ignore" />
    </bag>
    <property name="DateCreated" column="DateCreated" type="lumen.hibernate.type.DateTimeType, lumen" not-null="false" update="false" insert="false" />
    <subclass name="lep.run.impl.Run, lep" proxy="IRun" discriminator-value="not null" />
  </class>

  <class name ="ILayout" table ="RunLayout" discriminator-value="null">
    <cache usage="read-write" />
    <id name="Id" type="Int32" unsaved-value="0">
      <generator class="identity" />
    </id>
    <discriminator column="Id" type="Int32" insert="false" />
    <property name="PageNo" column="PageNo"  not-null="false"  type="Int32" />
    <property name="FileName" column="FileName"  not-null="true" />
    <property name="Type" column="Type" type="lep.GenericEnum`1[lep.run.LayoutType], lep" not-null="true" />
    <subclass name="lep.run.impl.Layout, lep" proxy="IRun" discriminator-value="not null" />
  </class>
</hibernate-mapping>
