using Newtonsoft.Json;
using System.Collections.Generic;

namespace lep.job.impl
{
	public class JobOptionSpecStock : IJobOptionSpecStock
	{
		public JobOptionSpecStock()
		{
		}

		#region IJobOptionSpecStock Members

		public virtual int Id { get; set; }

		[JsonIgnore]
		public virtual IJobOptionSpecSize JobOptionSpecSize { get; set; }

		public virtual IStock Stock { get; set; }
		public bool Magnet => MinMagnet != 0;
		public virtual int MinMagnet { get; set; }
		public IList<JobPrintOptions> FrontPrintOptions { get; set; } = new List<JobPrintOptions>();
		public IList<JobPrintOptions> BackPrintOptions { get; set; } = new List<JobPrintOptions>();

		[JsonIgnore]
		public IList<CelloOption> CelloOptions { get; set; } = new List<CelloOption>();

		public IList<KeyValuePair<string, string>> CelloOptions2 { get; set; }

		public IJobOptionSpecQuantity QuantityOption { get; set; }
		public IList<IPaperSize> FoldOptions { get; set; } = new List<IPaperSize>();
		public PrintType PrintType { get; set; }

		public IList<int> QuantityOptions
		{
			set { }
			get {
				return QuantitiesByTemplate.Get((JobTypeOptions)JobOptionSpecSize.JobTemplate.Id, QuantityOption);
			}
		}

		#endregion IJobOptionSpecStock Members
	}
}
