﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Spring.Objects.Factory;
using System.ServiceModel;
using Spring.Objects.Factory;
using NHibernate;
using NHibernate.Criterion;
using lep.job;
using lep.order;

namespace lep.jobmonitor
{
	public class HealthChecker: BaseApplication,IScheduleApplication
	{



		/// <summary>
		/// called in by cron, say every 10 seconds
		/// </summary>
		public void UpdateAgeOfActiveJobsInProductionQueue()
		{

		}

		public void UpdateHealthStatusOfActiveJobsInProductionQueue()
		{

			ICriteria criteria = Session.CreateCriteria(typeof(IJob),"j").Add(Expression.IsNotNull("j.NextStatus"));
			IList<IJob> list = criteria.List<IJob>();

			foreach (IJob j in list) {
				//j.AgeInProductionQueue = JobAging.GetStandardAge(j);
				//ScheduleApplication.GetSchedulesByTemplate( j.Template
			
			}


		}

		public IScheduleApplication ScheduleApplication { get; set; }


	}
}
