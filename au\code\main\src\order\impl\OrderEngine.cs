using lep.configuration;
using System;

namespace lep.order.impl
{
	// moved cream to  OrderApp .. CronTask_ArchiveOrders

	public class OrderEngine : IInitializingObject
    {
        private IConfigurationApplication _configApp;
        private bool initialised;
        private IOrderApplication _orderApp;

        public OrderEngine()
        {
        }

        public IOrderApplication OrderApplication
        {
            set { _orderApp = value; }
        }

        public IConfigurationApplication ConfigurationApplication
        {
            set { _configApp = value; }
        }

        public void AfterPropertiesSet()
        {
            if (_orderApp == null)
            {
                throw new ArgumentNullException("orderApplication");
            }
            if (_configApp == null)
            {
                throw new ArgumentNullException("configurationApplication");
            }
            initialised = true;
        }

        public void CronTask()
        {
            if (!initialised)
            {
                throw new ApplicationException("OrderEngine not initialised");
            }

            var archiveday = 0;
            int.TryParse(_configApp.GetValue(Configuration.ArchiveOrdersPeriod), out archiveday);
            if (archiveday > 0)
            {
                foreach (var order in _orderApp.FindArchiveOrder(archiveday))
                {
                    //_orderApp.MoveOrderFile(order);
                    order.FileRemoved = true;
                    order.Status = OrderStatusOptions.Archived;
                    _orderApp.Save(order);
                }
            }

            if (DateTime.Now.Hour == 23)
            {
                foreach (var o in _orderApp.FindEmptyOrder())
                {
                    _orderApp.Delete(o);
                }
            }
        }
    }
}
