﻿using lep.address.impl;
using lep.freight;

//using lep.quote;
using lep.courier;
using System.Collections.Generic;

namespace lep.job
{
	public class JobSplit 
	{
		public int Version {get;set;} = 101;

		public int Quantity { get; set; }
		public ListOfPackages Packages { get; set; } = new ListOfPackages();
		public string PackagesStr { get; set; }

		public PhysicalAddress Address { get; set; } = new PhysicalAddress();
		public string FullAddress { get; set; }
		
		public string Courier { get; set; } 
		public decimal? CustomerCharge { get; set; } = 0m;
		
		public string RecipientName { get; set; }
		public string RecipientPhone { get; set; }
		public bool CustomerLogoRequired { get; set; } = false;

		public string CustomerLogoYourRef { get; set; }
		public string DeliveryInstructions { get; set; }

		public bool IsCustom { get; set; }

		public BrochureDistPackInfo BrochureDistPackInfo { get; set; }

		public bool IsValid()
		{
			return Address != null &&
				   string.IsNullOrEmpty(Address.Postcode);
		}




	}

	
	public class JobSplits : List<JobSplit>	
	{
	
	 

	}


	public class JobSplitEx1 : JobSplit
	{
		public List<Rate> Rates { get; set; } = new List<Rate>();


		public JobSplitEx1(JobSplit b)
		{
			FullAddress = b.FullAddress;
			Quantity = b.Quantity;
			Packages = b.Packages;
			Address = b.Address;
			Courier = b.Courier;
			CustomerCharge = b.CustomerCharge;
			RecipientName = b.RecipientName;
			RecipientPhone = b.RecipientPhone;
			CustomerLogoRequired = b.CustomerLogoRequired;
			CustomerLogoYourRef = b.CustomerLogoYourRef;
			DeliveryInstructions = b.DeliveryInstructions;
			BrochureDistPackInfo = b.BrochureDistPackInfo;
			Version = b.Version;
			IsCustom = b.IsCustom;
		}
	}

	public class JobSplitSuggestions
	{
		public Facility Facility { get; set; }

		public List<JobSplitEx1> Splits { get; set; } = new List<JobSplitEx1>();


	}
	

	
	
}
