﻿<?xml version="1.0" encoding="utf-8"?>
<ClassDiagram MajorVersion="1" MinorVersion="1">
  <Class Name="lep.jobmonitor.StandardRouting">
    <Position X="0.5" Y="3.5" Width="4.25" />
    <Compartments>
      <Compartment Name="Fields" Collapsed="true" />
    </Compartments>
    <TypeIdentifier>
      <HashCode>AAQABAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAACgQ=</HashCode>
      <FileName>src\jobmonitor\StandardRouting.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="lep.jobmonitor.JobBoardSelectionHelper">
    <Position X="0.5" Y="0.5" Width="3.25" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>src\jobmonitor\JobBoardSelectionHelper.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="lep.jobmonitor.impl.JobBoardEventsProducer">
    <Position X="5.25" Y="2.5" Width="2.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAACAAEAAAAAAACAAAAAgAAAEBAAAAAAAAQAA=</HashCode>
      <FileName>src\jobmonitor\impl\JobBoardEventsProducer.cs</FileName>
    </TypeIdentifier>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="lep.jobmonitor.impl.JobBoardEventsConsumer">
    <Position X="8" Y="2.5" Width="3" />
    <Compartments>
      <Compartment Name="Fields" Collapsed="true" />
    </Compartments>
    <TypeIdentifier>
      <HashCode>AAAAIAAAACAAEAAAAAAACAAAAEgAAAABAQAIAgAGQAA=</HashCode>
      <FileName>src\jobmonitor\impl\JobBoardEventsConsumer.cs</FileName>
    </TypeIdentifier>
    <Lollipop Position="0.2" />
  </Class>
  <Struct Name="lep.jobmonitor.JobBoardEvent">
    <Position X="8.25" Y="0.5" Width="2.5" />
    <TypeIdentifier>
      <HashCode>EAAAAAAAAAAAAAAAAAAAAAAAAACAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>src\jobmonitor\JobBoardEvent.cs</FileName>
    </TypeIdentifier>
  </Struct>
  <Interface Name="lep.jobmonitor.IJobBoardEventsProducer" Collapsed="true">
    <Position X="5" Y="0.5" Width="3" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAA=</HashCode>
      <FileName>src\jobmonitor\IIJobBoardEventsProducer.cs</FileName>
    </TypeIdentifier>
  </Interface>
  <Enum Name="lep.jobmonitor.JobBoardTypes" Collapsed="true">
    <Position X="0.5" Y="2.75" Width="3.25" />
    <TypeIdentifier>
      <HashCode>IAAEAAACAAABAAAAAgAAAAAAAgEAAAAAEAAAAAEgAAA=</HashCode>
      <FileName>src\jobmonitor\JobBoardTypes.cs</FileName>
    </TypeIdentifier>
  </Enum>
  <Font Name="Segoe UI" Size="9" />
</ClassDiagram>