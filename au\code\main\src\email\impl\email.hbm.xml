<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2"
				   namespace="lep.email.impl"
				   assembly="lep"
				   auto-import="true"
				   default-cascade="all">

	<class name="EmailMessage" table="EmailMessage">
		<cache usage="read-write" />
		<id name="Id" type="Int32" unsaved-value="0" access="field.camelcase">
			<generator class="identity" />
		</id>

		<property name="From" column="`from`" length="255" not-null="true" access="field.camelcase" />
		<property name="To" column="`to`" length="255" not-null="true" access="field.camelcase" />
		<property name="BCC" length="255" access="field.camelcase" />
		<property name="ReplyTo" length="255" access="field.camelcase" />
		<property name="Subject" length="255" not-null="true" access="field.camelcase" />
		<property name="Body" type="StringClob" not-null="true" access="field.camelcase" />
		<property name="IsBodyHtml" type="YesNo" not-null="true" access="field.camelcase" />
		<property name="BodyEncodingName" length="32" not-null="true" />
		<property name="Priority" type="lep.email.impl.MailPriorityEnum, lep" not-null="true" access="field.camelcase" />
	</class>
</hibernate-mapping>