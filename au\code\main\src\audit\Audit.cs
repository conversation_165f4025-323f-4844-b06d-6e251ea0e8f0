﻿using System;

namespace lep.audit
{
	public class Audit
	{
		public Audit()
		{
		}

		public virtual int Id { get; set; }
		public virtual string Entity { get; set; }
		public virtual int EntityId { get; set; }

		public virtual int UserId { get; set; }
		public virtual string UserName { get; set; }

		public virtual string Body { get; set; }

		public virtual DateTime EventDate { get; set; }
		public virtual bool IsStaff { get; set; }
	}
}
