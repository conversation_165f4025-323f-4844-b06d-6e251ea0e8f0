using lep.configuration;
using lep.job;
using lep.security;
using lep.user;

using Serilog;
using NHibernate;
using NHibernate.Criterion;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text.RegularExpressions;
using System.Xml.Linq;
using System.Xml.XPath;
using static lep.job.JobTypeOptions;
using static NHibernate.Criterion.Restrictions;

namespace lep.run.impl
{
	/// <summary>
	///
	/// </summary>
	public class RunApplication : BaseApplication, IRunApplication
	{
		// private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);
	
		private IJobApplication jobApp;
		private LepArtworkScriptFiles LepArtworkScriptFiles { get; }

		public RunApplication(
			ISession sf,
			ISecurityApplication _securityApp,
			IJobApplication jobApp,
				LepArtworkScriptFiles lepArtworkScriptFiles
			) : base(sf, _securityApp)
		{
			this.jobApp = jobApp;
				this.LepArtworkScriptFiles = lepArtworkScriptFiles;
		}


		public IJobApplication JobApplication
		{
			set { jobApp = value; }
		}

		public bool LegacyMacWorkflow { set; private get; }

		public IRun NewRun(IJob job)
		{
			//AssertPermission("run.create");
			IRun r =
				new Run(
					job.Template.Is(BusinessCard, Postcard, BusinessCardNdd, BusinessCardSdd),
					job.Celloglaze, job.IsCMYK, job.FinalStock, job.BackPrinting, job.Facility.Value);
			r.DateCreated = DateTime.Now;
			return r;
		}

		public IRun GetRun(int Id)
		{
			//AssertPermission("run.read");
			return Get<IRun>(Id);
		}

		public void Save(IRun run)
		{
			//AssertPermission("run.update");

			var createScript = false;
			if (run.Id == 0)
			{
				createScript = true;
			}
			run.FileMoveRequired = true;
			try
			{
				base.Save<IRun>(run);
			}
			catch (Exception ex)
			{
				Log.Error(ex.Message, ex);
			}

			if (run.Jobs.Count > 0)
			{
				if (createScript)
				{
					CreateArtworkScript(run);
				}

				var placeHolderFile = new FileInfo(LepGlobal.Instance.ArtworkDirectory(run).FullName + "\\file processing");
				if (!LepGlobal.Instance.ArtworkDirectory(run).Exists)
				{
					LepGlobal.Instance.ArtworkDirectory(run).Create();
				}
				if (!placeHolderFile.Exists)
				{
					using (var f = placeHolderFile.Create())
					{
						f.Close();
					}
				}

				if (createScript)
				{
					//save update info for new run, the run id cant be tracked in interceptor
					// if (HttpContext.Current.Application["runmodify"] == null) {
					//     HttpContext.Current.Application["runmodify"] = new Dictionary<int, string>();
					// }
					var user = CurrentUser is IStaff ? $"{((IStaff)CurrentUser).FirstName} {((IStaff)CurrentUser).LastName}" : "System";
					//((Dictionary<int, string>)HttpContext.Current.Application["runmodify"]).Add(run.Id,user);

					LepGlobal.Instance.RunModify.AddOrUpdate(run.Id, user, (k, v) => user);
				}

				CopyRunFile(run);
			}
			else
			{
				base.Delete<IRun>(run);
			}
		}

		public void Delete(IRun run)
		{
			//AssertPermission("run.delete");

			//to get rid of job's run flag
			IList<IJob> jobLst = new List<IJob>(run.Jobs);
			foreach (var j in jobLst)
			{
				run.RemoveJob(j, (IStaff)CurrentUser); //securityApp.GetSystemUser()
			}
			Save(run);
		}

		public IList<IRun> FindFileCopyRun()
		{
			return Session.CreateCriteria(typeof(IRun))
				.Add(Eq("FileMoveRequired", true))
				.Add(Eq("FileRemoved", false))
				.List<IRun>();
		}

		public IList<IRun> FindOldRun()
		{
			return Session.CreateCriteria(typeof(IRun))
				.Add(Eq("Status", RunStatusOptions.PackingDone))
				.Add(Eq("FileRemoved", false))
				.Add(Lt("DateModified", DateTime.Now.AddDays(-8)))
				.List<IRun>();
		}

		public void RemoveRunFile(IRun run)
		{
			try
			{
				var dirInfo = LepGlobal.Instance.ArtworkDirectory(run);
				if (dirInfo.Exists)
				{
					dirInfo.Delete(true);
				}
			}
			catch (Exception ex)
			{
				Log.Error("Error run file remove:" + ex.Message, ex);
			}
		}

		public void CopyRunFile(IRun run)
		{
			try
			{
				var runDir = LepGlobal.Instance.ArtworkDirectory(run);
				if (runDir.Exists)
				{
					IList<int> jobIds = run.Jobs.Select(j => j.Id).ToList();
					foreach (var info in runDir.GetDirectories())
					{
						var jobid = 0;
						int.TryParse(info.Name.Split('-')[0], out jobid);
						if (!jobIds.Contains(jobid))
						{
							info.Delete(true);
						}
					}
				}
				else
				{
					runDir.Create();
				}

				foreach (var job in run.Jobs)
				{
					if (!job.IsPresentationFolder())
					{
						continue;
					}

					try
					{
						var subInfo = LepGlobal.Instance.ArtworkDirectory(run, job);
						if (!subInfo.Exists)
						{
							subInfo.Create();
							var files = jobApp.GetListArtwork(job);
							foreach (var file in files)
							{
								try
								{
									file.CopyTo(String.Format("{0}/{1}", subInfo.FullName, file.Name), true);
								}
								catch (Exception innerE)
								{
									Log.Warning("Error job copy:" + innerE.Message, innerE);
								}
							}
							//copy customer file as well
							var runCustFileDir = new DirectoryInfo(subInfo.FullName + "\\from-customer");
							runCustFileDir.Create();
							var custFileDir = LepGlobal.Instance.ArtworkDirectory(job, true);
							if (custFileDir.Exists)
							{
								foreach (var file in custFileDir.GetFiles())
								{
									try
									{
										file.CopyTo(runCustFileDir.FullName + "\\" + file.Name, true);
									}
									catch (Exception innerE)
									{
										Log.Warning("Error job copy:" + innerE.Message, innerE);
									}
								}
							}
							
							try{
								var extraFilesDirJob = LepGlobal.Instance.ArtworkDirectory(job, false) + "\\ExtraFiles";
								if (Directory.Exists(extraFilesDirJob))
								{
									Copy(extraFilesDirJob, subInfo+ "\\ExtraFiles");
								} 
							}
							catch(Exception ex)
							{
								Log.Warning("Error job copy extra files:" + ex.Message, ex);
							}

						}
					}
					catch (Exception innerE)
					{
						Log.Error("Error job copy:" + innerE.Message, innerE);
					}
				}
			}
			catch (Exception ex)
			{
				Log.Error("Error run copy:" + ex.Message, ex);
			}
			run.FileMoveRequired = false;
			Save<IRun>(run);
			var placeHolderFile = new FileInfo(LepGlobal.Instance.ArtworkDirectory(run).FullName + "\\file processing");
			if (placeHolderFile.Exists)
			{
				try
				{
					placeHolderFile.Delete();
				}
				catch (Exception e)
				{
					Log.Error("Error delete placeholder file:" + e.Message);
				}
			}
		}


		void Copy(string sourceDir, string targetDir)
		{
			Directory.CreateDirectory(targetDir);

			foreach (var file in Directory.GetFiles(sourceDir))
				File.Copy(file, Path.Combine(targetDir, Path.GetFileName(file)));

			foreach (var directory in Directory.GetDirectories(sourceDir))
				Copy(directory, Path.Combine(targetDir, Path.GetFileName(directory)));
		}


		public ILayout NewLayout(LayoutType type, int pageNo, string fileName)
		{
			ILayout layout = new Layout();
			layout.FileName = fileName;
			layout.Type = type;
			layout.PageNo = pageNo;
			return layout;
		}

		public IRun SaveLayout(IRun run, ILayout layout, Stream input, string extension, IStaff prepressBy)
		{
			//AssertPermission("run.update");

			var dir = LepGlobal.Instance.ArtworkDirectory(run);
			if (!dir.Exists)
			{
				dir.Create();
			}
			input.Position = 0;
			CopyStream(input, new FileStream(string.Format("{0}/{1}", dir.FullName, layout.FileName), FileMode.Create));
			run.LayoutDone(layout, prepressBy);

			return run;
		}

		public void SetLayout(IRun run, RunStatusOptions status)
		{
			var dir = LepGlobal.Instance.ArtworkDirectory(run);
			if (!dir.Exists)
			{
				throw new Exception("run directory not found");
			}

			var runFiles = dir.GetFiles();

			foreach (var file in dir.GetFiles())
			{
				var filename = file.Name;
				if (filename.LastIndexOf('.') != -1)
				{
					filename = filename.Substring(0, filename.LastIndexOf('.'));
				}

				var stream = file.OpenRead();
				var type = LepArtworkScriptFiles.FileDetector.GetFileType(stream);
				stream.Close();

				if (type == ArtworkTypeOptions.Quark || type == ArtworkTypeOptions.InDesign)
				{
					ILayout layout = null;
					switch (filename.ToLower())
					{
						case "frontlayout":
							layout = NewLayout(LayoutType.FrontLayout, 0, file.Name);
							break;

						case "backlayout":
							layout = NewLayout(LayoutType.BackLayout, 0, file.Name);
							break;

						case "insideback":
							layout = NewLayout(LayoutType.InsideBack, 0, file.Name);
							break;

						case "insidefront":
							layout = NewLayout(LayoutType.InsideFront, 0, file.Name);
							break;

						case "outsideback":
							layout = NewLayout(LayoutType.OutsideBack, 0, file.Name);
							break;

						case "outsidefront":
							layout = NewLayout(LayoutType.OutsideFront, 0, file.Name);
							break;

						default:
							if (file.Name.ToLower().StartsWith("page"))
							{
								var mathReg = type == ArtworkTypeOptions.Quark
									? "page(\\d+)\\.tif"
									: "page(\\d+)\\.indd";
								var m = new Regex(mathReg).Match(file.Name);
								if (m.Success)
								{
									var num = 0;
									int.TryParse(m.Groups[1].Value, out num);
									layout = NewLayout(LayoutType.Page, num, file.Name);
								}
							}
							break;
					}
					if (layout != null)
					{
						run.LayoutDone(layout, (IStaff)Get<IUser>(1));
					}
				}
			}
			run.SetStatus(status, (IStaff)Get<IUser>(1));
			base.Save<IRun>(run);
		}

		public IList<IRun> FindRunsForJob(IJob job)
		{
			//AssertPermission("run.search");

			var criteria = Session.CreateCriteria(typeof(IRun));
			criteria.Add(Eq("Status", RunStatusOptions.Filling));
			criteria.Add(Eq("ManuallyManage", false));
			criteria.Add(Eq("Facility", job.Facility.Value));
			if (job.Template.Is(BusinessCard, BusinessCardNdd, BusinessCardSdd, Postcard))
			{
				if (job.Quantity > 500)
				{
					//look for 1000 run
					criteria.Add(
						Expression.Sql(
							"exists (select * from RunJob rj join Job j on rj.jobid = j.id where j.quantity > 500 and rj.runid = {alias}.id)"));
				}
				else
				{
					//look for 1000 run
					criteria.Add(
						Expression.Sql(
							"not exists (select * from RunJob rj join Job j on rj.jobid = j.id where j.quantity > 500 and rj.runid = {alias}.id)"));
				}
				criteria.Add(Eq("Celloglaze", job.Celloglaze));
				criteria.Add(Eq("IsBusinessCard", true));
			}
			else
			{
				criteria.Add(Eq("IsBusinessCard", false));
			}
			criteria.Add(Eq("Stock", job.FinalStock));
			criteria.AddOrder(Order.Desc("Urgent"));

			var rs =  criteria.List<IRun>().ToList();

			if(job.IsBusinessCard())
			{
				if(job.Quantity <= 250)
					rs = rs.Where(r => r.Jobs.All(j => j.Quantity <= 250)).ToList();
				if (job.Quantity > 250)
					rs = rs.Where(r => r.Jobs.All(j => j.Quantity > 250)).ToList();
			}

			return rs;
		}

		public ICriteria FindCurrentRunsCriteria(Facility facility, List<RunStatusOptions> status, string runNr, string orderNr, string jobNr, IStock stock,
		 RunSearchOptions searchOption, bool isOnHold, RunCelloglazeOptions? runCello = null)
		{
			var criteria = Session.CreateCriteria(typeof(IRun), "r")
						  //.SetFetchMode("Jobs", FetchMode.Join)
						  //.SetResultTransformer(new DistinctRootEntityResultTransformer())
						  ;

			criteria.CreateAlias("Stock", "s");
			criteria.Add(Eq("Facility", facility));
			if (String.IsNullOrEmpty(orderNr) && string.IsNullOrEmpty(jobNr) && String.IsNullOrEmpty(runNr))
			{
				var interval = -8;
				if(LepGlobal.Instance.TestBox)
					interval = -80;
				criteria.Add(Gt("r.DateModified", DateTime.Now.AddDays(interval)));
				criteria.Add(InG<RunStatusOptions>("r.Status", status));
			}
			else
			{
				if (!String.IsNullOrEmpty(jobNr))
				{
					var job = 0;
					int.TryParse(jobNr, out job);
					criteria.Add(
						Expression.Sql(
							String.Format(
								"exists (select * from runjob rj where rj.jobid = {0} and {{alias}}.id = rj.runid )",
								job)));
				}
				if (!String.IsNullOrEmpty(orderNr))
				{
					var order = 0;
					int.TryParse(orderNr, out order);
					criteria.Add(
						Expression.Sql(
							String.Format(
								"exists (select * from runjob rj join job jo on jo.id = rj.jobid where {{alias}}.id = rj.runid and jo.orderid = {0})",
								order)));
				}
				if (!String.IsNullOrEmpty(runNr))
				{
					var run = 0;
					int.TryParse(runNr, out run);
					criteria.Add(Eq("r.Id", run));
				}
			}

			if (stock != null)
			{
				criteria.Add(
					Expression.Sql(
						String.Format(
							"exists (select * from runjob rj join job j on j.id = rj.jobid and rj.runid = {{alias}}.id and j.stock ={0} )",
							stock.Id)));
			}

			if (runCello != null)
			{
				criteria.Add(Eq("r.Celloglaze", runCello));
			}

			if (isOnHold)
			{
				criteria.Add(
					Expression.Sql(
						"exists (select * from runjob rj join job j on j.id = rj.jobid and rj.runid = {alias}.id and j.ProofStatus ='OnHold')"));
			}

			switch (searchOption)
			{
				case RunSearchOptions.NonBC:
					criteria.Add(Eq("r.IsBusinessCard", false));
					break;

				case RunSearchOptions.BC:
					criteria.Add(Eq("r.IsBusinessCard", true));
					break;

				case RunSearchOptions.BC310:
					criteria.Add(Eq("r.IsBusinessCard", true));
					//criteria.Add(Eq("s.Name", "310 GSM LEP Deluxe ArtFboard"));
					criteria.Add(Eq("s.Id", 17 /* 310 GSM Deluxe Artboard */));
					break;

				case RunSearchOptions.BC360:
					criteria.Add(Eq("r.IsBusinessCard", true));
					//criteria.Add(Eq("s.Name", "310 GSM LEP Deluxe ArtFboard"));
					criteria.Add(Eq("s.Id", 93 /* 360 GSM Deluxe Artboard */));
					break;


				case RunSearchOptions.BC420:
					criteria.Add(Eq("r.IsBusinessCard", true));
					criteria.Add(Eq("s.Id", 91) /* 400 GSM Deluxe Artboard */  );
					break;



				case RunSearchOptions.BCLoyalty:
					criteria.Add(Eq("r.IsBusinessCard", true));
					criteria.Add(Eq("s.Id", 29) /* Loyalty card */  );
					break;

				case RunSearchOptions.BC350Recycled:
					criteria.Add(Eq("r.IsBusinessCard", true));
					criteria.Add(Eq("s.Id", 31) /* 350 GSM Recycled */  );
					break;
			}

			//  criteria.AddOrder(nhOrder);
			//criteria.SetMaxResults(30);
			return criteria;
		}

		public IList<IRun> FindCurrentRuns(Facility facility, List<RunStatusOptions> status, string runNr, string orderNr, string jobNr, IStock stock, RunSearchOptions searchOption, bool isOnHold)
		{
			var criteria = FindCurrentRunsCriteria(facility, status, runNr, orderNr, jobNr, stock, searchOption, isOnHold);
			return criteria.List<IRun>();
		}

		public string GenerateRunScript(IRun run)
		{
			if (LepArtworkScriptFiles.RunScriptTemplate != null && LepArtworkScriptFiles.RunScriptTemplate.Exists)
			{
				var reader = LepArtworkScriptFiles.RunScriptTemplate.OpenText();
				var templateString = reader.ReadToEnd();
				reader.Close();
				templateString = templateString.Replace("[[Run Dir]]",
					String.Format("{0:yyyyMMdd}/{1}", run.DateCreated, run.RunNr));
				return templateString;
			}
			return "";
		}

		private void CreateArtworkScript(IRun run)
		{
			var dir = LepGlobal.Instance.ArtworkDirectory(run);
			if (!dir.Exists)
			{
				dir.Create();
			}
			var url = LepGlobal.Instance.AbsolutePathURL + "/setrunstatus.aspx";

			StreamReader scriptReader = null;
			string script = null;
			string layoutdone = null;
			string pingurl = null;
			FileInfo f = null;

			// window
			if (LepArtworkScriptFiles.RunWinScript != null && LepArtworkScriptFiles.RunWinScript.Exists)
			{
				scriptReader = LepArtworkScriptFiles.RunWinScript.OpenText();
				script = scriptReader.ReadToEnd();
				layoutdone = string.Format("{0}/._layoutdone.vbs", dir.FullName);
				pingurl = @"{0}?id={1}&status={2}";
				f = new FileInfo(layoutdone);
				if (!f.Exists)
				{
					var writer = f.CreateText();
					var data = script.Replace("[[URL]]", String.Format(pingurl, url, run.Id, "layoutdone"));
					writer.Write(data.Replace("[[ScriptFileName]]", "._layoutdone.vbs"));
					writer.Close();
				}
				scriptReader.Close();
			}
			/*
			if (LegacyMacWorkflow)
			{
				if (LepArtworkScriptFiles.RunMacScript != null && LepArtworkScriptFiles.RunMacScript.Exists && LepArtworkScriptFiles.RunIcon != null && LepArtworkScriptFiles.RunIcon.Exists &&
					LepArtworkScriptFiles.RunMacPauseScript != null && LepArtworkScriptFiles.RunMacPauseScript.Exists && LepArtworkScriptFiles.PauseIcon != null && LepArtworkScriptFiles.PauseIcon.Exists)
				{
					pingurl = @"{0}?id={1}&amp;status={2}";
					scriptReader = LepArtworkScriptFiles.RunMacScript.OpenText();
					script = scriptReader.ReadToEnd();

					layoutdone = string.Format("{0}/layoutdone.wflow", dir.FullName);
					var scriptdata = script.Replace("[[RunNumber]]", run.RunNr);
					var dateCreated = run.DateCreated;
					if (dateCreated == DateTime.MinValue)
					{
						dateCreated = run.DateModified;
					}
					scriptdata = scriptdata.Replace("[[RunDate]]", string.Format("{0:yyyyMMdd}", dateCreated));
					f = new FileInfo(layoutdone);
					if (!f.Exists)
					{
						var writer = f.CreateText();
						writer.Write(scriptdata.Replace("[[URL]]", String.Format(pingurl, url, run.Id, "layoutdone")));
						writer.Close();
					}
					LepArtworkScriptFiles.RunIcon.CopyTo(string.Format("{0}/._layoutdone.wflow", dir.FullName), true);
					scriptReader.Close();

					scriptReader = LepArtworkScriptFiles.RunMacPauseScript.OpenText();
					script = scriptReader.ReadToEnd();

					var pause = string.Format("{0}/pause.wflow", dir.FullName);
					scriptdata = script.Replace("[[RunNumber]]", run.RunNr);
					if (dateCreated == DateTime.MinValue)
					{
						dateCreated = run.DateModified;
					}
					scriptdata = scriptdata.Replace("[[RunDate]]", string.Format("{0:yyyyMMdd}", dateCreated));
					f = new FileInfo(pause);
					if (!f.Exists)
					{
						var writer = f.CreateText();
						writer.Write(scriptdata);
						writer.Close();
					}
					LepArtworkScriptFiles.PauseIcon.CopyTo(string.Format("{0}/._pause.wflow", dir.FullName), true);
					scriptReader.Close();
				}
			}
			else
			{
				layoutdone = string.Format("{0}/layoutdone.webloc", dir.FullName);
				CreateMacScript(string.Format("lep:layoutdone:{0}", run.Id)).SaveWithOutBOM(layoutdone);
				new FileInfo(layoutdone).CopyMacIconFrom(LepArtworkScriptFiles.RunIcon);

				var pause = string.Format("{0}/pause.webloc", dir.FullName);
				CreateMacScript(string.Format("lep:pauserun:{0}", run.Id)).SaveWithOutBOM(pause);
				new FileInfo(pause).CopyMacIconFrom(LepArtworkScriptFiles.PauseIcon);
			}*/
		}

		private XDocument CreateMacScript(string url)
		{
			var doc =
				XDocument.Parse(
					"<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE plist PUBLIC \"-//Apple//DTD PLIST 1.0//EN\" \"http://www.apple.com/DTDs/PropertyList-1.0.dtd\"><plist version=\"1.0\"><dict><key>URL</key><string/></dict></plist>");
			var el = doc.XPathSelectElement("/plist/dict/string");
			el.Value = url;

			return doc;
		}

		private void CopyStream(Stream input, Stream output)
		{
			try
			{
				while (true)
				{
					var buffer = new byte[1024];
					var count = input.Read(buffer, 0, 1024);
					if (count == 0)
					{
						break;
					}
					output.Write(buffer, 0, count);
				}
			}
			finally
			{
				output.Close();
			}
		}

		public void CronTask_RunFileRemove()
		{
			foreach (var run in FindOldRun())
			{
				RemoveRunFile(run);
				run.FileRemoved = true;
				//base.Save(run);
				Save(run);
			}
		}

		public void CronTask_RunFileCopy()
		{
			foreach (var run in FindFileCopyRun())
			{
				CopyRunFile(run);
			}
		}
	}
}
