namespace lep.promotion
{
	public enum PromotionLifeSpan
    {
        [Description("Promotions is valid from a specific date range")] Absolute,

        // The window is measured in days and is exact. The start of the window is set to the date that the customer has
        // assigned to the campaign with this offer. Only Valid In Campaign will be forced set to true.
        // If the window is 90 days, then it is exactly 90 days, not 3 months.
        [Description("Promotion is valid in a window after the customer was assigned in a campaign")] Windowed
    }
}