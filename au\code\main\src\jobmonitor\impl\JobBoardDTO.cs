﻿using lep.job;
using System;

namespace lep.jobmonitor.impl
{
	[Serializable]
    public class JobDetailsDTO
    {
        public JobDetailsDTO()
        {
        }

        public int Id { get; set; }
        public string JobName { get; set; }
        public string Template { get; set; }
        public string Stock { get; set; }
        public string Cello { get; set; }

        public string TrimSize { get; set; } // Finished Size
        public string Folding { get; set; }
        public int Quantity { get; set; }
        public string PressDetailPaperSize { get; set; } // 0R , 1J
        public int PressSheets { get; set; }

        public string SubmittedDate { get; set; }
        public string DespatchByDate { get; set; }
        public DateTime? StatusDate { get; set; }

        public string CurrentStatus { get; set; }
        public string NextStatus { get; set; }

        public int RunId { get; set; }
        public bool RunIsBC { get; set; }
        public string RunNumber { get; set; }
        public string RowType { get; set; } // 0R , 1J

        public Facility Facility { get; set; }

        public string ExtraData { get; set; }
        //public string RunIsBCard { get; set; }

        //public string Grouping { get; set; }

        public int OrderId { get; set; }
        public string CustomerName { get; set; }

        public TimeSpan Age { get; set; }
        public long? HD { get; set; } = null;

        public string Color { get; set; }
        public int Health { get; set; }
        public int HealthOfRun { get; set; }
        public DateTime UpddateAt { get; set; }
        public long Version { get; set; }

        public JobBoardTypes[] BoardsToAppear { get; set; }

        public virtual DateTime? OrderDispatchEst { get; set; }

		public DateTime? PrintByDate { get; set; }
    }
}
