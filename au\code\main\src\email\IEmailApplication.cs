using lep.content;
using lep.job;
using lep.onlineTxn;
using lep.order;
using lep.user;
using System.Net.Mail;

namespace lep.email
{
	public interface IEmailApplication
	{
		IMailMessage NewHtmlMessage(SiteLocation loc);

		IMailMessage NewTextMessage(SiteLocation loc);

		void ErrorMessageAddressing(IMailMessage message);

		void Send(IMailMessage message);
		void Send(MailMessage message);

		void SendNotification(IOrder order, ContentType contentType);

		void SendNotification(IJob job, ContentType contentType);

		void SendCustomerNotification(ICustomerUser customer, ContentType notificationType);

		void SendCustomerDetailChangeNotification(ICustomerUser customer);

		void SendOnlinePaymentNotification(IOnlineTxn tx);

		bool SendForgottenPassword(IUser user, string newPassword);

		void SendPendingMessages();

		void SendRaisePurchaseOrderForJobToAcounts(IJob job);

		void SendPayMeOrder(IOrder order);


		string ConNoteMacroReplace2(IOrder order, string body);
		void SendRejectionOfVariations(IOrder order);
	}
}
