using System.Collections.Generic;
using System.Text;

namespace lep.job.csv
{
    public class JobOptionCsvReader : BaseCsvReader
    {
        private JobOptionImportHandler importHandler;

        private JobOptionVerifyForeignKey verifyForeignKeyHandler;
        private JobOptionVerifySchemaHandler verifyHandler;

        public JobOptionCsvReader()
            : base()
        {
            verifyHandler = new JobOptionVerifySchemaHandler();
            verifyForeignKeyHandler = new JobOptionVerifyForeignKey();
            importHandler = new JobOptionImportHandler();

            skipHandler.Handler = verifyHandler;
            verifyHandler.Handler = verifyForeignKeyHandler;
            verifyForeignKeyHandler.Handler = importHandler;
        }

        public int NumRowAdded { get; set; } = 0;

        public IDictionary<int, IJobOptionSpecStock> SpecPrint { get; set; }

        public IDictionary<int, IJobOptionSpecQuantity> SpecRange { get; set; }

        public IDictionary<int, IList<IPaperSize>> SpecFolds { get; set; }

        public IList<IJobTemplate> Templates
        {
            get { return importHandler.Templates; }
            set {
                importHandler.Templates = value;
                verifyForeignKeyHandler.Templates = value;
            }
        }

        public StringBuilder CacheScript
        {
            get { return importHandler.CacheScript; }
            set { importHandler.CacheScript = value; }
        }

        public override void StartDocument()
        {
            verifyForeignKeyHandler.JobApplication = jobApp;
            verifyForeignKeyHandler.SpecFolds = SpecFolds;
            verifyForeignKeyHandler.SpecPrint = SpecPrint;
            verifyForeignKeyHandler.SpecRange = SpecRange;
            importHandler._jobApp = jobApp;
            importHandler.SpecFolds = SpecFolds;
            importHandler.SpecPrint = SpecPrint;
            importHandler.SpecRange = SpecRange;

            base.StartDocument();
        }

        public override void EndDocument()
        {
            NumRowAdded = importHandler.NumRowAdded;
            importHandler.EndDocument();
            base.EndDocument();
        }
    }
}