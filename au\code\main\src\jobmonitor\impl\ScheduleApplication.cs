using lep.job;
using lep.security;
using NHibernate;
using NHibernate.Criterion;
using System;
using System.Collections.Generic;
using System.Linq;

namespace lep.jobmonitor.impl
{
	/// <summary>
	/// Schedule Appplication
	///		handles scheduling and settings for diffrent production timings
	///			Standard Jobs
	///			Prioritised  Jobs [not requested by LEP as of yet]
	///			Guranteed	 Jobs [not requested by LEP as of yet]
	/// </summary>
	public class ScheduleApplication : BaseApplication, IScheduleApplication
    {
 
        public ScheduleApplication(ISession sf, ISecurityApplication _securityApp) : base(sf, _securityApp)
        {
        }
 

        #region Private Methods

        private static bool IsJobFoldingDepandant(IJob job)
        {
            var folding = false;

            if (job.IsMagazine())
                return true;

            if (job.Template.Id == (int)JobTypeOptions.Brochure ||
                job.Template.Id == (int)JobTypeOptions.BrochureSpecial)
            {
                folding = job.FoldedSize == null ? false : true;
            }
            else
            {
                folding = false;
            }
            return folding;
        }

        #endregion Private Methods

        #region Constants

        private const string CACHE_REGION = "ScheduleApplication";

        #endregion Constants

        #region Public Methods

        public void AddSchedule(Facility facility, IJobTemplate jobTemplate, ProductionTiming pt,
            JobStatusOptions status, int amber, int red, bool folding)
        {
            var s = new Schedule()
            {
                Template = jobTemplate,
                Status = status,
                Amber = amber,
                Red = red,
                ScheduleType = pt,
                DateCreated = DateTime.Now,
                Folding = folding,
                Facility = facility
            };

            Save(s);
        }

        public void DeleteById(int id)
        {
            Delete<ISchedule>(base.Get<ISchedule>(id));
        }

        public ISchedule Get(int id)
        {
            return base.Get<ISchedule>(id);
        }

        public ISchedule GetNextStandardSchedule(IJob job)
        {
            var folding = IsJobFoldingDepandant(job);
            var nextSchedulesForThisJob = GetSchedulesByTemplate(job.Facility.Value, job.Template,
                ProductionTiming.Standard, folding);
            var nextSchedulesForThisJob2 = nextSchedulesForThisJob.Where(s => s.Status == job.NextStatus).ToList();
            return nextSchedulesForThisJob2.Count == 0
                ? new Schedule() { Id = -1 }
                : (Schedule)nextSchedulesForThisJob2[0];
        }

        // new code
        public List<ISchedule> GetNextStandardSchedules(IJob job)
        {
            var folding = IsJobFoldingDepandant(job);
            var nextSchedulesForThisJob =
                GetSchedulesByTemplate(job.Facility.Value, job.Template, ProductionTiming.Standard, folding).ToList();
            return nextSchedulesForThisJob;
        }

        public IList<ISchedule> GetAllStandardSchedules()
        {
            var list = Session
                .CreateCriteria(typeof(ISchedule), "s")
                .Add(Restrictions.Eq("s.ScheduleType", ProductionTiming.Standard))
                .AddOrder(Order.Desc("s.Id"))
                .SetCacheable(true)

                .List<ISchedule>();
            return list;
        }

        public IList<ISchedule> GetSchedulesByTemplate(Facility facility, IJobTemplate jobTemplate, ProductionTiming pt,
            bool folding)
        {
            //if (!(jobTemplate.Id == (int)JobTypeOptions.Brochure || jobTemplate.Id == (int)JobTypeOptions.BrochureSpecial)) {
            //	folding = false;
            //}

            var list = Session
                .CreateCriteria(typeof(ISchedule), "s")
                .Add(Restrictions.Eq("Template", jobTemplate))
                .Add(Restrictions.Eq("Facility", facility))
                .Add(Restrictions.Eq("s.ScheduleType", pt))
                .Add(Restrictions.Eq("s.Folding", folding))
                .AddOrder(Order.Asc("s.StatusInt"))
				.SetCacheable(true)
				.List<ISchedule>();
            return list;
        }

        public IList<ISchedule> GetSchedulesByTemplate(Facility facility, JobTypeOptions jobType, ProductionTiming pt,
            bool folding)
        {
            //if (!(jobTemplate.Id == (int)JobTypeOptions.Brochure || jobTemplate.Id == (int)JobTypeOptions.BrochureSpecial)) {
            //	folding = false;
            //}

            var list = Session
                .CreateCriteria(typeof(ISchedule), "s")
                .CreateAlias("s.Template", "t")
                .Add(Restrictions.Eq("t.Id", (int)jobType))
                .Add(Restrictions.Eq("Facility", facility))
                .Add(Restrictions.Eq("s.ScheduleType", pt))
                .Add(Restrictions.Eq("s.Folding", folding))
                .AddOrder(Order.Asc("s.StatusInt"))
				.SetCacheable(true)
				.List<ISchedule>();
            return list;
        }

        public void Save(ISchedule s)
        {
            base.Save<ISchedule>(s);
        }

        #endregion Public Methods
    }
}
