﻿namespace lep.promotion
{
	public interface IPromotedProduct
    {
        int Id { get; set; }

        //// Parent promotion
        //IPromotion Promotion { get; set; }

        //// the 3 attributes that are specified on a type
        //IJobTemplate JobTemplate { get; set; }

        //IStock Stock { get; set; }

        //IPaperSize PaperSize { get; set; }
        int JobOptionId { get; set; }

        int StockId { get; set; }
        int PaperSizeId { get; set; }
        int PromotionId { get; set; }
    }
}