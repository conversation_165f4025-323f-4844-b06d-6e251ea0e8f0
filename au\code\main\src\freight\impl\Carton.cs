using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;

namespace lep.freight.impl
{
    public class CartonCode
    {
        public const string D100 = "D100";
        public const string D250 = "D250";
        public const string L0 = "L0";
        public const string L1 = "L1";
        public const string L2 = "L2";
        public const string H1 = "H1";
        public const string H2 = "H2";
        public const string BC250 = "BC 250";
        public const string A4TMailer = "A4 T-Mailer";
        public const string A4TMailerSmall = "A4 T-Mailer (Small)";
        public const string DLTMailer = "DL T-Mailer";
        public const string BCStdTMailer = "BC Std T-Mailer";
        public const string D100TMailer = "D100 T-Mailer";
        public const string A4Small = "A4 Small";
        public const string A4 = "A4";
        public const string A3 = "A3";
        public const string PresentationFolderPF300 = "Presentation Folder PF300";
        public const string PresentationFolderPF400 = "Presentation Folder PF400";
        public const string PresentationFolderPFOS = "Presentation Folder PF O/S";
        public const string A1Poster = "A1 Poster";
        public const string A2Poster = "A2 Poster";
        public const string TurduckenOversizedA3 = "Turducken (Oversized A3)";
        public const string SkidSmall = "Skid - Small";
        public const string SkidMedium = "Skid - Medium";
        public const string SkidLarge = "Skid - Large";
    }

    [DebuggerDisplay("{Code}  {Level}  {Width}x{Height}x{Depth}   {IWidth}x{IHeight}x{IDepth}")]
    public class Carton : ICarton
    {
        public Carton()
        {
        }

        public virtual string Code { get; set; }
        public virtual decimal Weight { get; set; }

        public virtual int Width { get; set; }
        public virtual int Height { get; set; }
        public virtual int Depth { get; set; }

        public virtual int IWidth { get; set; }
        public virtual int IHeight { get; set; }
        public virtual int IDepth { get; set; }

        public virtual int? Capacity { get; set; }

        public virtual int Level { get; set; }
		public virtual bool Wrap { get; set; }

		public bool CanGetFurhterWrapped { get; set; }
		//public virtual ICarton Level2Carton { get; set; }

		//public virtual decimal AllowWeight { get; set; }

		//public virtual  string Rule { get; set; }
		//public virtual Func<IJob, bool> PredicateOnJob { get; set; } = null;

		public long InternalVolume => IDepth * IHeight * IWidth;
        public long ExternalVolume => Depth * Height * Width;

        public int[] GetLeast2Dims()
        {
            var ls = new List<int>() { IWidth, IHeight, IDepth };
            var a = ls.OrderBy(l => l).Take(2).ToArray();
            return a;
        }

        public float SizeLeft(ICarton innerCarton)
        {
            List<float> outer = new List<float> { IWidth, IHeight, IDepth };
            List<float> inner = new List<float> { innerCarton.Width, innerCarton.Height, innerCarton.Depth };
            inner = inner.OrderBy(v => v).ToList();
            outer = outer.OrderBy(v => v).ToList();
            for (var i = 0; i < inner.Count; i++)
            {
                if (inner[i] >= outer[i])
                {
                    return -1;
                }
            }

            return outer[0] * outer[1] * outer[2] - inner[0] * inner[1] * inner[2];
        }

        public bool CanFit(float w, float h)
        {
            var w1 = IWidth;
            var h1 = IHeight;

            if ((w1 >= w && h1 >= h) || (w1 >= h && h1 >= w))
                return true;

            w1 = IDepth;
            h1 = IWidth;
            if ((w1 >= w && h1 >= h) || (w1 >= h && h1 >= w))
                return true;

            w1 = IDepth;
            h1 = IHeight;
            if ((w1 >= w && h1 >= h) || (w1 >= h && h1 >= w))
                return true;

            return false;
        }
    }
}
