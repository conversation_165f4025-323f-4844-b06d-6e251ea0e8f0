using lep.job;

using Serilog;
using lumen.csv;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Reflection;

namespace lep.pricing.csv
{
	public class PriceImportHandler : BaseHandler
	{
		// private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);
		public List<string> errorList = new List<string>();

		public Queue<CsvException> errors;
		private Dictionary<string, int> fieldMap = new Dictionary<string, int>();

		private Hashtable hashJobTemplates = new Hashtable();
		private Hashtable hashPaperSize = new Hashtable();
		private Hashtable hashStock = new Hashtable();
		private IJobApplication jobApp;
		private IPricePointApplication priceApp;
		private bool seenFirstRow;

		public int NumRowAdded { get; private set; }

		public IJobApplication JobApplication
		{
			set { jobApp = value; }
		}

		public IPricePointApplication PricePointApplication
		{
			set { priceApp = value; }
		}

		public override void StartDocument()
		{
			base.StartDocument();
			jobApp.ListAllTemplates().ForEach(x => hashJobTemplates.Add(x.Name, x));
			jobApp.ListPaperSize().ForEach(x => hashPaperSize.Add(x.Name, x));
			jobApp.ListStock().ForEach(x => hashStock.Add(x.Name, x));
		}

		public override void EndDocument()
		{
		}

		public override void RowData(string[] values)
		{
			if (values.Length == 0) // number of columns
				return;

			NumRowAdded++;
			if (!seenFirstRow)
			{
				seenFirstRow = true;
				//Map column names to column numbers
				fieldMap = new Dictionary<string, int>(values.Length);
				for (var i = 0; i < values.Length; i++)
				{
					fieldMap[values[i].ToLower()] = i;
				}
			}
			else
			{
				if (values.Length < fieldMap.Count)
				{
					errorList.Add(String.Format("Line {0}: Not contain enough columns", NumRowAdded));
					return;
				}

				try
				{
					var fmjt = fieldMap["job type"];
					var t = values[fmjt];
					var template = (IJobTemplate)hashJobTemplates[t];

					var ps = values[fieldMap["size"]];
					var paperSize = (IPaperSize)hashPaperSize[ps];

					var st = values[fieldMap["stock"]];
					var stock = (IStock)hashStock[st];

					if (template == null)
					{
						throw new CsvException(String.Format("specified template '{0}'  does not exist",
							values[fieldMap["job type"]]));
					}

					if (paperSize == null)
					{
						throw new CsvException(String.Format("specified paper size '{0}' does not exist",
							values[fieldMap["size"]]));
					}

					if (stock == null)
					{
						throw new CsvException(String.Format("specified stock '{0}' does not exist",
							values[fieldMap["stock"]]));
					}

					var numColourSides = Convert.ToInt32(values[fieldMap["colour"]]);
					var numPages = Convert.ToInt32(values[fieldMap["page"]]);
					var quantity = Convert.ToInt32(values[fieldMap["qty"]]);
					var price = Convert.ToDecimal(values[fieldMap["price"]]);
					var celloglazing = values[fieldMap["cello"]];
					var required = values[fieldMap["required"]];
					//var myobNumber = string.IsNullOrEmpty(values[fieldMap["myob"]]) ? "" : values[fieldMap["myob"]];

					var pt = values[fieldMap["printtype"]];
					var printType = PrintType.O;
					if (pt == "D") printType = PrintType.D;
					else if (pt == "O") printType = PrintType.O;
					else if (pt == "W") printType = PrintType.W;
					else if (pt == "N") printType = PrintType.N;

					var cellos = celloglazing.Split('/');
					var frontCelloglaze =
						(JobCelloglazeOptions)Enum.Parse(typeof(JobCelloglazeOptions), cellos[0], true);
					var backCelloglaze =
						(JobCelloglazeOptions)Enum.Parse(typeof(JobCelloglazeOptions), cellos[1], true);

					//if (string.IsNullOrEmpty(myobNumber))
					//{
					//	//	throw new CsvException( String.Format( "MYOB number not specified") );
					//}

					if (required == "Y" && price == 0)
					{
						throw new CsvException(
							String.Format(
								"price not given for {0}, {1}, {2},  {3} page,  cello {4}, colorside {5}  quantity: {6}, print type:{7}",
								template.Name, paperSize.Name, stock.Name, numPages, celloglazing, numColourSides,
								quantity, printType.ToDescription()));
					}

					//if (required == "Other" && price == 0 && myobNumber != "")
					//{
					//	priceApp.UpdateOrSaveProductMYOB(printType, template, paperSize, stock, numColourSides, numPages,
					//		myobNumber, frontCelloglaze, backCelloglaze);
					//}

					priceApp.UpdateOrSavePricePoint(printType, template, stock, paperSize, numColourSides, celloglazing,
						numPages, quantity, price, "");
				}
				catch (IndexOutOfRangeException idxEx)
				{
					errorList.Add(string.Format("Line {0}: {1}", NumRowAdded, idxEx));
				}
				catch (CsvException csvEx)
				{
					errorList.Add(string.Format("Line {0}: {1}", NumRowAdded, csvEx.Message));
				}
				catch (Exception otherEx)
				{
					errorList.Add(string.Format("Line {0}: {1}", NumRowAdded, otherEx.Message));
				}
			}

			base.RowData(values);
		}
	}
}
