﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2"
				   namespace="lep.content"
				   assembly="lep"
				   auto-import="true"
				   default-cascade="all">

	<class name="IContent" table="Content" discriminator-value="null">
		<cache usage="read-write" />
		<id name="Id" type="Int32" unsaved-value="0">
			<generator class="identity" />
		</id>
		<discriminator column="Id" type="Int32" insert="false" />
		<timestamp name="DateModified" column="DateModified" />

		<property name="Title" length="80" not-null="true" />
		<property name="IsPlainText" type="YesNo" not-null="true" />
		<property name="Body" type="StringClob" />
		<property name="DateCreated" column="DateCreated" type="lumen.hibernate.type.DateTimeType, lumen" not-null="false" update="false" insert="false" />
		<subclass name="lep.content.impl.Content, lep" proxy="IContent" discriminator-value="not null" />
	</class>
</hibernate-mapping>