using lep.configuration;
using lep.courier;
using lep.job;
using lep.order;
using lep.security;

using Serilog;
using NHibernate;
using System;
using System.Linq;
using System.Reflection;

namespace lep.freight.impl
{
	/// <summary>
	///
	/// </summary>
	public class FreightApplication : BaseApplication, IFreightApplication
	{
		// private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);
		private const int SkidLevel = 4;

		public FreightApplication(ISession sf, ISecurityApplication _securityApp, IPackageApplication packageApplication, IConfigurationApplication configurationApplication) : base(sf, _securityApp)
		{
			PackageApplication = packageApplication;
			ConfigurationApplication = configurationApplication;
		}

		#region carton calculation for order

		public bool HasSkid(IOrder order, Facility facility)
		{
			var packages = order.PackDetail.GetPackages(facility);

			//return packages.Any(p => CartonApplication.GetCarton(p.CartonCode).PackagingLevel == 4);
			return packages.Any(p => p.CartonCode.Contains("Skid"));
		}
/*
		public OrderFreightLog GetFreightLog(int id)
		{
			OrderFreightLog freightLog = Get<OrderFreightLog>(id);
			if (freightLog == null)
			{
				freightLog = new OrderFreightLog() { Id = id, IsNew = true };
			}

			return freightLog;
		}

		public void Save(OrderFreightLog freightLog)
		{
			if (freightLog.IsNew)
			{
				if (String.IsNullOrEmpty(freightLog.PackLog))
					return;
				ForceSave<OrderFreightLog>(freightLog);
				freightLog.IsNew = false;
			}
			else
			{
				Save<OrderFreightLog>(freightLog);
			}
		}
*/
		// does this function need to get called after submitted?
		//
		public void LEPInternalFreightSelection(IOrder order)
		{
			/*
            OrderFreightLog freightLog = GetFreightLog(order.Id);
            StringBuilder courierlog = new StringBuilder();

            Facility? probableNearestFacility = OrderApplication.GetProductionFacilityByPostCode(order.DeliveryAddress.Postcode);
            if (probableNearestFacility.HasValue && !order.Courier.IsNone) {
                Facility nearestFacility = probableNearestFacility.Value;

                switch (nearestFacility) {
                    case Facility.FG:
                        // set order.PackDetail.FGCourier   to    whatever customer selected to be Preffered courier
                        order.PackDetail.FGCourier = order.Courier;

                        // set order.PackDetail.PMCourier   to    whatever is cheapest
                        if (order.PackDetail.IsPMCourierCustom) {
                            courierlog.AppendLine("PM courier custom");
                        } else {
                            if (order.PackDetail.Packages.Where(x => x.Facility == Facility.PM).Count() > 0) {
                                var pmprices = CourierApplication.GetAvailableRates(order, Facility.PM, false);
                                var lowestRate = pmprices.FirstOrDefault();

                                if(lowestRate != null) {
                                    order.PackDetail.PMCourier = new CourierType(string.Format("{0} ~ {1}", lowestRate.CarrierName, lowestRate.ServiceName));
                                } else {
                                    order.PackDetail.PMCourier = CourierType.None;
                                }
                            } else {
                                order.PackDetail.PMCourier = CourierType.None;
                            }
                        }
                        break;

                    case Facility.PM:
                        // set order.PackDetail.PMCourier   to    whatever customer selected to be Preffered courier
                        order.PackDetail.PMCourier = order.Courier;

                        // set order.PackDetail.FGCourier   to    whatever is cheapest
                        if (order.PackDetail.IsFGCourierCustom) {
                            courierlog.AppendLine("FG courier custom");
                        } else {
                            if (order.PackDetail.Packages.Where(x => x.Facility == Facility.FG).Count() > 0) {
                                var fgprices = CourierApplication.GetAvailableRates(order, Facility.FG, false);
                                var lowestRate = fgprices.FirstOrDefault();

                                if (lowestRate != null) {
                                    order.PackDetail.FGCourier = new CourierType(string.Format("{0} ~ {1}", lowestRate.CarrierName, lowestRate.ServiceName));
                                } else {
                                    order.PackDetail.FGCourier = CourierType.None;
                                }
                            } else {
                                order.PackDetail.FGCourier = CourierType.None;
                            }
                        }
                        break;
                }
            }
            freightLog.CourierLog = courierlog.ToString();
            Save(freightLog);*/
		}

		public void SetFreight(IJob job)
		{
			try
			{
				PackageApplication.SetPackage(job);
			}
			catch (Exception ex)
			{
				Log.Error(ex.Message, ex);
			}
		}

		public void SetFreight(IOrder order)
		{

			if (order.HasSplitDelivery)
				return;

			try
			{
				//OrderFreightLog freightLog = GetFreightLog(order.Id);

				if (order.PackDetail == null)
					order.PackDetail = new PackDetail();

				//freightLog.JobDetail = order.GetJobLog();
				//freightLog.PackLog = null;

				PackageApplication.SetPackage(order);
				//freightLog.PackLog = order.PackDetail.PackLog(null);
				if (order.Courier.IsPickup || order.Courier == CourierType.TNT)
				{
					//freightLog.CourierLog = null;
					//Save(freightLog);
					
					if(!order.PackDetail.IsFGCourierCustom)
						order.PackDetail.FGCourier = order.Courier;

					if (!order.PackDetail.IsPMCourierCustom)
						order.PackDetail.PMCourier = order.Courier;

					if (order.Courier.IsPickup)
					{
						// on pickup set PickUpCharge needs to be set instead of packing detail price
						order.PickUpCharge =
							Convert.ToDecimal(ConfigurationApplication.GetValue(Configuration.PickUpCharge));

						if( !(order.PackDetail.IsFGCourierCustom || order.PackDetail.IsPMCourierCustom))
							order.PackDetail.Price = 0;
					}

					return;
				}

				if (!order.PackDetail.IsFGCourierCustom)
					order.PackDetail.FGCourier = CourierType.None;

				if (!order.PackDetail.IsPMCourierCustom)
					order.PackDetail.PMCourier = CourierType.None;

				//Save(freightLog);
			}
			catch (Exception ex)
			{
				Log.Error(ex.Message, ex);
			}
		}

		public void CronTask_MoveToNewFreight()
		{
			var sql = @"select o.Id from [order] o
			where o.Status not in ('Dispatched', 'Archived', 'Open')
			and(o.FGPackageJson is null  or o.PMPackageJson is null)
			and(o.IsFreightCustom = 'N')
			order by o.id desc";

			var orderIds = Session.CreateSQLQuery(sql).List<int>();

			foreach (var orderId in orderIds)
				MoveToNewFreight(orderId);

			sql = @"	select top 300  o.Id from [order] o
			where o.Status in ('Open')
			and(o.FGPackageJson is null  or o.PMPackageJson is null)
			and(o.IsFreightCustom = 'N')
			and( o.DateCreated >= '2018-08-01')
			order by o.id desc";

			orderIds = Session.CreateSQLQuery(sql).List<int>();

			foreach (var orderId in orderIds)
				MoveToNewFreight(orderId);
		}

		public void MoveToNewFreight(int orderId)
		{
			try
			{
				//RunInTransaction(() => {
				var order = Session.Get<IOrder>(orderId);

				foreach (var j in order.Jobs)
					if (!j.Freight.IsCustom)
						PackageApplication.SetPackage(j); ;

				if (order.PackDetail == null)
				{
					order.PackDetail = new PackDetail();
				}
				var allowSkidDelivery = PackageApplication.AllowSkidDelivery(order);
				order.PackDetail.SetPackages(PackageApplication.PackItems(PackageApplication.GetPackage(order, Facility.FG), allowSkidDelivery), Facility.FG);
				order.PackDetail.SetPackages(PackageApplication.PackItems(PackageApplication.GetPackage(order, Facility.PM), allowSkidDelivery), Facility.PM);

				order.SetDispatchLabels();

				base.Save(order);
				Log.Information("Freight set " + orderId);
			}
			catch (Exception ex)
			{
				Log.Error($"Freight set {orderId} ", ex);
			}
		}

		#endregion carton calculation for order

		private decimal GetCustomerAdjustedFreightPrice(Int32 CustomerID, decimal Price)
		{
			string sql = @"Select dbo.GetFreightPriceForCustomer(:CustomerID,:Price)";
			IQuery query = Session.CreateSQLQuery(sql);
			query.SetInt32("CustomerID", CustomerID);
			query.SetDecimal("Price", Price);
			return query.UniqueResult<decimal>();
		}

		public decimal GetCustomerFreightMarginFromCode(String freightCode)
		{
			var sql = @"SELECT CASE
	                    WHEN EXISTS(SELECT 1                  from dbo.CustomerFreightPricing where Code  = :freightCode)
                            THEN (SELECT ISNULL(Margin, 0)  from dbo.CustomerFreightPricing  where Code = :freightCode)
                            ELSE 0
                        END";

			IQuery query = Session.CreateSQLQuery(sql).SetAnsiString("freightCode", freightCode);
			var margin = Convert.ToDecimal(query.UniqueResult());
			return margin;
			//return query.UniqueResult<float>(); // throws error: ?
		}

		public void AfterPropertiesSet()
		{
		}

		#region Service Properties

		public IOrderApplication OrderApplication { get; set; }
		public IConfigurationApplication ConfigurationApplication { set; get; }

		public IPackageApplication PackageApplication { get; set; }
		public ICourierApplication CourierApplication { get; set; }

		#endregion Service Properties
	}
}
