<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2"
				   namespace="lep.backupdelete"
				   assembly="lep"
				   auto-import="true"
				   default-cascade="all">

	<class name="IUsage" table="`Usage`" discriminator-value="null">
		<cache usage="read-write" />
		<id name="Id"  unsaved-value="0">
			<generator class="identity" />
		</id>
		<discriminator column="Id" insert="false" />
        <property name="OnDate"    type="lumen.hibernate.type.DateTimeType, lumen" />
        <property name="Remaining" not-null="true" />
        <property name="Used"      not-null="true" />
        <subclass name="lep.backupdelete.impl.Usage, lep" proxy="IUsage" discriminator-value="not null" />
	</class>
</hibernate-mapping>