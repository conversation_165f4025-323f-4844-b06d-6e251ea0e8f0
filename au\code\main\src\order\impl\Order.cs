#region

using lep.address;
using lep.address.impl;
using lep.contact;
using lep.courier;
using lep.extensionmethods;
using lep.freight;
using lep.freight.impl;
using lep.job;
using lep.job.impl;
using lep.promotion;
using lep.run;
using lep.user;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;

using System.Text;
using static lep.OrderPaymentStatusOptions;
using static lep.PaymentTermsOptions;

#endregion

namespace lep.order.impl
{
	[Serializable]
	public class Order : IOrder
	{
		// private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		public Order()
		{
			Courier = new CourierType();
		}

		public Order(ICustomerUser customer)
		{
			//order default detail
			Customer = customer ?? throw new ArgumentNullException("customer");
			Status = OrderStatusOptions.Open;
			Contact = customer.Contact1;
			Courier = customer.PreferredCourier;
			DeliveryAddress = new PhysicalAddress()
			{
				Address1 = customer.PostalAddress.Address1,
				Address2 = customer.PostalAddress.Address2,
				Address3 = customer.PostalAddress.Address3,
				City = customer.PostalAddress.City,
				Country = customer.PostalAddress.Country,
				Postcode = customer.PostalAddress.Postcode,
				State = customer.PostalAddress.State
			};

			Courier = new CourierType();
			var sb = new StringBuilder();
			sb.Append(Customer.Name);
			Log.Information("Order {OrderId}  for Customer {CustomerId} {CustomerName}", Id, customer.Id, customer.Name);
		}

		public string IsDigitalStyle => Jobs != null && Jobs.Any(j => j.IsDigital()) ? "DigitalStyle" : "";

		public string IsSpecialInstructions
		{
			get {
				var hasSpecial = "";

				if (Jobs != null)
				{
				
					foreach( var _ in Jobs)
					{
						if (!String.IsNullOrEmpty(_.SpecialInstructions))
						{
							hasSpecial += $"Job {_.Id}\n{_.SpecialInstructions}\n";
						}
					}
					//if (Jobs.Any(_ => !String.IsNullOrEmpty(_.SpecialInstructions)))
					//{
					//	hasSpecial = "YES";
					//}
				}
				return hasSpecial;
			}
		}

		public string Summary
		{
			get {
				var sb = new StringBuilder();
				if (Jobs != null)
					foreach (var j in Jobs)
					{
						sb.AppendLine(String.Format("{0} x {1}- {2} {3} {4}", j.Template.Name, j.Quantity, j.FinalStock.Name,
							j.FinishedSize.PaperSize.Name,
							j.Celloglaze == RunCelloglazeOptions.None ? String.Empty : j.Celloglaze.ToDescription()));
					}
				var value = sb.ToString();
				return value;
			}
		}

		public string Description
		{
			get {
				if (Jobs != null)
				{
					var description = String.Join("\n",
						Jobs.Select(j => $"{j.Name} - {j.Quantity} ").ToArray());
					return description;
				}
				return "";
			}
		}

		public string JobType
		{
			get {
				var jobType = "";
				if (Jobs.Any())
				{
					jobType = Jobs.Select(_ => {
						
						var sb = new StringBuilder();
						sb.Append(_.Template.Name);
						
						var c = _.Celloglaze.ToString();
						if(c.Contains("Spot"))
							sb.Append(" - SpotUV");

						if(c.Contains("Foil"))
							sb.Append(" - Foil " + _.FoilColour);


						return sb.ToString();

						}).Distinct().Aggregate((m, n) => m + "\n" + n);
				}
				return jobType;
			}
		}

		public string JobSize
		{
			get {
				var jobSize = "";
				if (Jobs.Any())
				{
					jobSize = Jobs.Select(j => j.GetJobSize()).Distinct().Aggregate((m, n) => m + "\n" + n);
				}
				return jobSize;
			}
		}

		public int NumJobs => Jobs?.Count() ?? 0;

		public string GetJobLog()
		{
			var jobLog = new StringBuilder();
			foreach (var j in Jobs)
			{
				jobLog.AppendFormat("{0} {1} x {2} {3}\n", j.JobNr, j.Template.Name, j.Quantity,
					j.Facility.HasValue ? j.Facility.Value.ToString() : "facility not set");
				jobLog.AppendFormat("    stock: {0}\n    finish: {1}\n    fold: {2}\n", j.FinalStock.Name,
					j.FinishedSize.PaperSize.Name, j.FoldedSize?.PaperSize?.Name ?? "None");
				if (j.StockForCover != null)
				{
					jobLog.AppendFormat("    cover stock: {0}\n", j.StockForCover.Name);
				}
				if (j.Pages > 1)
				{
					jobLog.AppendFormat("    pages: {0}\n", j.Pages);
				}
				if (j.NumberOfMagnets > 0)
				{
					jobLog.AppendLine(j.NumberOfMagnets + " magnet");
				}
				if (j.Freight.IsCustom)
				{
					jobLog.AppendLine("    freight set by staff");
				}

				if (j.Freight.Packages?.Count > 0)
				{
					foreach (var c in j.Freight.Packages)
					{
						jobLog.AppendFormat("    freight: {0}  \n", c.CartonCode);
						jobLog.AppendFormat("    carton weight: {0} KG\n", AuditDecimal(c.Weight));
					}
				}
				else
				{
					jobLog.AppendLine("    freight not set");
				}

				jobLog.Append("\n\n");
			}

			return jobLog.ToString();
		}
		
	 
		public virtual IList<IDispatchLabel> DispatchLabels { get; set; } = new List<IDispatchLabel>();
		//public string GetVersion()
		//{
		//	if (SubmissionDate != DateTime.MinValue && SubmissionDate < new DateTime(2015, 10, 20)) {
		//		return "v1";
		//	}
		//	return "v2";
		//}

		//public bool IsV2()
		//{
		//	return GetVersion() == "v2";
		//}

		#region security methods

		private void AssertOrderStatus(OrderStatusOptions checkstatus)
		{
			if (Status != checkstatus)
			{
				var message = $"status expected {checkstatus}   actual {Status} ";
				Log.Error("Order {OrderId} " + message, OrderNr);
				throw new ArgumentException("Order is not in appropriate status. " + message);
			}
		}

		#endregion

		public static string AuditPercentage(decimal? number)
		{
			if (number.HasValue)
			{
				return Math.Round(number.Value * 100, 5).ToString("G29");
			}
			return String.Empty;
		}

		public static string AuditDecimal(decimal? number)
		{
			if (number.HasValue)
			{
				return Math.Round(number.Value, 5).ToString("G29");
			}
			return String.Empty;
		}

		#region IOrder Members

		public virtual int Id { get; set; }

		public virtual string OrderNr
		{
			get { return String.Format("{0:D6}", Id); }
		}

		public virtual ICustomerUser Customer { get; set; }

		private decimal? _price;

		public virtual decimal? Price
		{
			get {
				decimal? result = null;

				if (PriceOfJobs != null && PriceOfGST != null)
				{
					result = PriceOfJobs.Value + PriceOfGST.Value;

					_price = result;
					return result;
				}
				return null;
			}

			set {
				_price = value;
			}
		}

		public virtual decimal? PriceWL
		{
			get {
				decimal? result = null;

				if (PriceOfJobsWL != null && PriceOfGSTWL != null)
				{
					result = PriceOfJobsWL.Value + PriceOfGSTWL.Value;

					return result;
				}
				return null;
			}
		}

		public virtual decimal? PriceOfOnlyJobs
		{
			get {
				decimal total = 0;

				if (Jobs != null)
					foreach (var job in Jobs)
					{
						if (String.IsNullOrEmpty(job.Price))
						{
							return null;
						}
						//else if (job.Enable) {
						total += decimal.Parse(job.Price);
						//}
					}
				return total;
			}
		}

		public virtual decimal? PriceOfOnlyJobsWL
		{
			get {
				decimal total = 0;

				if (Jobs != null)
					foreach (var job in Jobs)
					{
						if (String.IsNullOrEmpty(job.PriceWL))
						{
							return null;
						}
						//else if (job.Enable) {
						decimal d = 0;
						if (decimal.TryParse(job.PriceWL, out d))
						{
							total += d;
						}

						//}
					}
				return total;
			}
		}

		public virtual decimal? PriceOfJobs
		{
			get {
				var tmp = PriceOfOnlyJobs;

				if (tmp.HasValue)
				{
					tmp -= PromotionBenefit;
				}

				if (tmp.HasValue && PackDetail != null)
				{
					if ((PackDetail.FGCourier.IsPickup || PackDetail.PMCourier.IsPickup) && PickUpCharge != 0)
					{
						return tmp.Value + decimal.Round(PickUpCharge, 2); // - PromotionBenefit;
					}
					else if (PackDetail.Price.HasValue)
					{
						return tmp.Value + decimal.Round(PackDetail.Price.Value, 2); // - PromotionBenefit;
					}
					return tmp;
				}

				return null;
			}
		}

		public virtual decimal? PriceOfJobsWL
		{
			get {
				var tmp = PriceOfOnlyJobsWL;

				if (tmp.HasValue && PackDetail != null)
				{
					if ((PackDetail.FGCourier.IsPickup || PackDetail.PMCourier.IsPickup) && PickUpCharge != 0)
					{
						return tmp.Value + decimal.Round(PickUpCharge, 2); // - PromotionBenefit;
					}
					else if (PackDetail.Price.HasValue)
					{
						return tmp.Value + decimal.Round(PackDetail.Price.Value, 2); // - PromotionBenefit;
					}
					return tmp;
				}

				return null;
			}
		}

		public virtual decimal? PriceOfGST
		{
			get {
				if (GST == 0)
					return 0;

				var tmp = PriceOfJobs;
				if (tmp.HasValue)
				{
					return tmp.Value * GST / 100;
				}

				return null;
			}
		}

		public virtual decimal? PriceOfGSTWL
		{
			get {
				if (GST == 0)
					return 0;

				var tmp = PriceOfJobsWL;
				if (tmp.HasValue)
				{
					return tmp.Value * GST / 100;
				}

				return null;
			}
		}

		public virtual IPackDetail PackDetail { get; set; } = new PackDetail();

		public virtual OrderStatusOptions Status { get; set; }

		public virtual string ExtraStatus
		{
			get {
				string result = "";
				foreach (IJob j in Jobs)
				{
					if (j.ProofStatus == JobProofStatus.OnHold)
					{
						result = "<b style='color:red'>On Hold<b>";
						break;
					}
					else
					if (j.Runs.Count > 0)
					{
						result = "Run";
						break;
					}
					else if (j.Urgent)
					{
						result = "Urgent";
						break;
					}
					else if (j.HasReject)
					{
						result = "Rejected";
						break;
					}
					else if (j.NeedApproval)
					{
						result = "Requires Approval";
						break;
					}
				}
				if (PaymentStatus == AwaitingPayment)
				{
					if (Customer.PaymentTerms == PrePay || Customer.PaymentTerms == OnHold)
					{
						if (Status == OrderStatusOptions.Submitted)
						{
							bool IsReady = true;
							foreach (IJob j in Jobs)
							{
								if (j.Status != JobStatusOptions.PreflightDone)
								{
									IsReady = false;
									break;
								}
							}
							if (Jobs.Any() && IsReady)
							{
								result = "Pay";
							}
						}
					}
					else if (Customer.PaymentTerms == COD)
					{
						foreach (IJob j in Jobs)
						{
							if (j.Status == JobStatusOptions.Finished)
							{
								result = "Pay";
								break;
							}
						}
					}
				}

				return result;
			}

			set { }
		}

		//public virtual bool Enable { get; } = true;

		public virtual OrderPaymentStatusOptions PaymentStatus { get; set; }

		public virtual IPhysicalAddress DeliveryAddress { get; set; }

		public virtual string DeliveryInstructions { get; set; }

		public virtual DateTime? DispatchEst { get; set; }

		public virtual IContact Contact { get; set; }
		public virtual IContact WLContact { get; set; }

		public virtual IList<IJob> Jobs { get; set; } = new List<IJob>();

		//public virtual IEnumerable<IJob> ActiveJobs => Jobs; //.Where(j => j.Enable);

		public virtual DateTime? SubmissionDate { get; set; }
		public virtual DateTime? DispatchDate { get; set; }
		public virtual DateTime? FinishDate { get; set; }
		public virtual DateTime DateCreated { get; set; }
		public virtual DateTime DateModified { get; set; }
		public virtual bool FileRemoved { get; set; } = false;
		public virtual CourierType Courier { get; set; }

		public virtual bool CustomerLogoRequired { get; set; }
		public virtual string CustomerLogoYourRef { get; set; }
		public virtual string RecipientName { get; set; }
		public virtual string RecipientPhone { get; set; }
		public virtual string PurchaseOrder { get; set; }
		public virtual string FreightPriceCode { get; set; }
		public virtual decimal GST { get; set; }
		public virtual IPromotion Promotion { get; set; }
		public virtual decimal PromotionJobPriceBenefit { get; set; }
		public virtual bool IsQuote { get; set; }
		public virtual decimal PickUpCharge { get; set; }

		public virtual bool IsWLOrder { get; set; }

		/// <summary>
		/// For White labeled order, if the web user logged in then this field will store it
		/// </summary>
		public virtual int? WLCustomerId { get; set; }

		public virtual string WLCustomerName { get; set; }
		public virtual string WLAnonymousUserId { get; set; }

		public virtual bool IsWLOrderPaidFor { get; set; }
		public virtual string WLOrderPaymentDetails { get; set; }

		public virtual string Invoiced { get; set; }
		public virtual string Invoiced2 { get; set; }
		public virtual bool IsDeleted { get; set; }

		public virtual bool PackWithoutPallets { get; set; }

		public virtual IList<OrderCredit> OrderCredits { get; set; } = new List<OrderCredit>();

		public virtual decimal PromotionBenefit
		{
			set { }
			get {
				var benefit = PromotionJobPriceBenefit;
				if (Promotion != null && Promotion.FreeDelivery && PackDetail.Price.HasValue)
				{
					benefit += PackDetail.Price.Value;
				}
				return benefit;
			}
		}

		public virtual string PigeonHoleSize(bool hasSkid)
		{
			if (hasSkid)
				return "SKID";

			var c = PackDetail.GetPackages(null).Count;
			if (c == 0)
				return "";
			else if (c > 0 && c < 3)
				return "SML";
			else if (c > 2 && c < 5)
				return "MED";
			if (c > 5)
				return "SKID";

			return "";
		}

		public virtual IList<IOrderConNote> ConNotes { get; set; } = new List<IOrderConNote>();

		#endregion

		#region Job Methods
 
		public IJob NewJob(string name, int quantity, bool trackProgress, string specialInstructions,
			IJobTemplate template, IUser createdByUser = null)
		{
			var job = new Job(this, name, quantity, trackProgress, specialInstructions, template);
			job.CreatedBy = createdByUser;
			if (Customer != null && Customer.SendSamples)
			{
				job.SendSamples = true;
			}

			// if(template != null && template.Id == (int) JobTypeOptions.WiroMagazines)
			// {
			// 	job.WiroInfo = new WiroMagazineInfo();
			// 	job.BindingOption = new BindingOption(){ Id =};
			// }
			Jobs.Add(job);

			return job;
		}

		public IList<IJob> ListJobs(IUser user)
		{
			//ApplicationSecurityManager.AssertPermission("order.jobs.list");
			var jobarray = new IJob[Jobs.Count];
			Jobs.CopyTo(jobarray, 0);
			var jobcopy = new List<IJob>();
			jobcopy.AddRange(jobarray);
			return jobcopy;
		}

		public void DeleteJob(IJob job)
		{
			//ApplicationSecurityManager.AssertPermission("order.jobs.delete");
			if (!job.IsOpenish())
			{
				Log.Error("Order " + OrderNr + " - Job " + job.Name + " cannot be deleted : job not in correct status");
				throw new ArgumentException("Job not in correct status");
			}
			//ApplicationSecurityManager.AssertIsCustomerUser(customer);
			if (!Jobs.Contains(job))
			{
				Log.Error("Order " + OrderNr + " - Job " + job.Name + " cannot be deleted : not found in Jobs");
				throw new ArgumentException("Job to delete not found in list");
			}
			Jobs.Remove(job);
			Log.Information("Order " + OrderNr + " - Job removed : " + job.Name);
		}

		#endregion

		#region Order manipulation methods

		private void CommentAllJobs(string commentText, IUser user)
		{
			foreach (var job in Jobs)
			{
				job.AddComment(user, commentText);
			}
		}

		public void Submit(IUser user)
		{
			//ApplicationSecurityManager.AssertPermission("order.submit");
			if (Status != OrderStatusOptions.Open/* && !Enable*/)
			{
				Log.Error("Order {OrderId} cannot Submit with Status : " + Status.ToString(), OrderNr);
				throw new ArgumentException("Order is not in appropriate status");
			}

			//ApplicationSecurityManager.AssertIsCustomerUser(customer);
			// check all jobs are ready
			foreach (var job in Jobs)
			{
				if (job.IsNCRBook() || job.IsEnvelope())
				{
					job.InvolvesOutwork = true;
				}

				if (job.Status.IsNot(JobStatusOptions.Open, JobStatusOptions.UnableToMeetPrice,  JobStatusOptions.PreflightDone, JobStatusOptions.Submitted))
				{
					throw new ArgumentException("Job not in appropriate status for submit: " + job.Name);
				}
			}

			foreach (var job in Jobs)
			{
				if (job.IsOpenish())
				{
					job.Submit(user);
				}
				job.ReceivedDate = DateTime.Now;
			}

			Status = OrderStatusOptions.Submitted;
			SubmissionDate = DateTime.Now;
			CommentAllJobs("Job submitted as part of order", user);
			Log.Information("Order {OrderId} submitted", OrderNr);
		}

		public bool CanReturn
		{
			get { return Status == OrderStatusOptions.Submitted; /* && Enable;*/ }
		}

		public void Return(IStaff staff)
		{
			//ApplicationSecurityManager.AssertPermission("order.preflight");
			if (!CanReturn)
			{
				throw new ArgumentException("Can't return order ");
			}

			foreach (var j in Jobs)
			{
				if (j.Status == JobStatusOptions.Submitted)
				{
					j.Status = JobStatusOptions.Open;
				}
			}
			Status = OrderStatusOptions.Open;
			CommentAllJobs("Job return to customer", staff);
			Log.Information("Order {OrderId} return to customer", OrderNr);
		}

		public void Ready(IStaff user)
		{
			//ApplicationSecurityManager.AssertPermission("order.preflight");
			AssertOrderStatus(OrderStatusOptions.Submitted);


			foreach (var job in Jobs)
			{
				if (job.Status != JobStatusOptions.PreflightDone && job.Status != JobStatusOptions.DPCPreProduction)
				{
					Log.Error("Order {OrderId} not Ready, Job {JobId} must be Ready", Id, job.Id);
					throw new ArgumentException(String.Format("Order {0} not Ready, Job {1} must be Ready", Id, job.Id));
				}
			}

			Status = OrderStatusOptions.Ready;
			CommentAllJobs("Order ready for processing", user);
			Log.Information("Order {OrderId} - preflight complete", Id);

			SetDispatchLabels();
		}

		public void SetDispatchLabels()
		{
		 
			try
			{
				DispatchLabels.Clear();
			}
			catch (Exception ex)
			{
				// eat up null exceptions from delphi dispatcher not filling these in
			}

			if (!PackDetail.FGCourier.IsNone)
			{
			}
			SetDispatchLabels(Facility.FG, PackDetail.FGCourier);

			if (!PackDetail.PMCourier.IsNone)
			{
			}
			SetDispatchLabels(Facility.PM, PackDetail.PMCourier);
		}

		private void SetDispatchLabels(Facility facility, CourierType courierType)
		{
			IList<Package> packages = packages = PackDetail.GetPackages(facility);

			foreach (var p in packages)
			{
				DispatchLabels.Add(
					new DispatchLabel()
					{
						CourierType = courierType,
						CartonType = p.CartonCode,
						Weight = p.Weight,
						Facility = facility,
					});
			}
		}

		public bool Withdraw(IUser user)
		{
			if (/*Enable &&*/ Status < OrderStatusOptions.Ready)
			{
				foreach (var job in Jobs)
				{
					job.Status = JobStatusOptions.Open;
					job.AddComment(user, "Job Withdraw");
					foreach (var r in job.Runs)
					{
						if (r.Jobs.Contains(job))
						{
							r.Jobs.Remove(job);
						}
					}
					job.Runs.Clear();
					if (job.IsQuotePrice)
						job.QuoteOutcome = QuoteStatus.Lost.ToString();
				}
				Status = OrderStatusOptions.Open;
				SubmissionDate = null;
				DispatchEst = null;
				return true;
			}
			return false;
		}

		public bool CanWithdraw(IUser user)
		{
			//if ((user is ICustomerUser && Status == OrderStatusOptions.Submitted) || user is IStaff) {
			//	if (IsWithdrawn == false)
			//		return true;
			//}

			//return false;

			if (Jobs.Count == 0 /*|| !Enable*/)
			{
				return false;
			}
			bool b = Jobs.All(job => /*!job.Enable || */ job.CanWithdraw(user));
			return b;
		}

		//public bool Reactivate (IUser user)
		//{
		//	//ApplicationSecurityManager.AssertPermission("order.withdraw");
		//	Enable = true;
		//	foreach (var job in jobs) {
		//		if (job.Enable) {
		//			return false;
		//		}
		//		job.Reactivate(user);
		//	}
		//	return true;
		//}

		//public bool CanReactivate (IUser user)
		//{
		//	if (jobs.Count == 0 || Enable) {
		//		return false;
		//	}

		//	foreach (var job in jobs) {
		//		if (job.Enable || !job.CanReactivate(user)) {
		//			return false;
		//		}
		//	}
		//	return true;
		//}

		public bool CanUpdate(IUser currentUser)
		{

			if ((currentUser is IStaff && ((IStaff)currentUser).Role == Role.SuperAdministrator))
				return true;
			if ((currentUser is IStaff && ((IStaff)currentUser).Role == Role.Administrator))
				return true;

			if ((currentUser is ICustomerUser))
			{
				if (Status == OrderStatusOptions.Open)
				{
					return true;
				}
			}

			bool result = (PaymentStatus != Paid);
			return result;
		}

		#endregion

		public string StatusC
		{
			get {
				//if (!Enable) {
				//	return "Cancelled";
				//}

				//if (IsWithdrawn) {
				//	return "Withdrawn";
				//}

				foreach (var j in Jobs)
				{
					if (j.HasReject)
					{
						return "Rejected";
					}
					else if (j.NeedApproval)
					{
						return "Requires Approval";
					}
				}

				switch (Status)
				{
					case OrderStatusOptions.Open:
						return "Not Submitted";

					case OrderStatusOptions.Submitted:
						return (Price != null ? "Submitted" : "Quoting");

					case OrderStatusOptions.Ready:
						return "In Production";

					case OrderStatusOptions.Finished:
						return "Completed";

					case OrderStatusOptions.Dispatched:
						return "Dispatched";

					case OrderStatusOptions.Archived:
						return "Archived";

					default:
						return Status.ToString();
				}
			}
		}

		public string StatusS
		{
			get {
				//if (!Enable) {
				//	return "Cancelled";
				//}

				//if (IsWithdrawn) {
				//	return "Withdrawn";
				//}
				//foreach (var j in Jobs) {
				//	if (j.HasReject) {
				//		return "Rejected";
				//	}
				//	else if (j.NeedApproval) {
				//		return "Requires Approval";
				//	}
				//}

				switch (Status)
				{
					case OrderStatusOptions.Open:
						return "Not Submitted";

					case OrderStatusOptions.Submitted:
						return "New Order";

					case OrderStatusOptions.Ready:
						return "Ready";

					case OrderStatusOptions.Finished:
						return "Completed";

					case OrderStatusOptions.Dispatched:
						return "Dispatched";

					case OrderStatusOptions.Archived:
						return "Archived";

					default:
						return Status.ToString();
				}
			}
		}

		public string Css
		{
			get {
				string result = "";
				if (Jobs == null) return result;


				if (Jobs.Any(j => j.IsDigital()))
				{
					result += "DigitalStyle ";
				}

				if (Jobs.Any(_ => _.IsSDD()))
				{
					result += "HasSDD ";
				}

				if (Jobs.Any(_ => _.IsNDD()))
				{
					result += "HasNDD ";
				}

				if (Jobs.Any(_ => (_.Template?.Id??0).Is(1,26,27,2) &&( _.Celloglaze.ToString().Contains("Spot")  || _.Celloglaze.ToString().Contains("Foil") )))
				{
					result += "HasSpotFoil ";
				}

				return result;
			}
		}

		//public bool IsWithdrawn { get; set; }
		public virtual Facility? FacilityAtSubmit { get; set; }

		public virtual ListOfOrderPaymentRecord Payments { get; set; } = new ListOfOrderPaymentRecord();

		public decimal? PendingPayment
		{
			get {
				if (Payments == null)
					return Price;

				if (Payments.Count == 0)
					return Price;

				var paidAmount = Payments.Sum(x => x.PaidAmount);

				var pricex = decimal.Round(Price.Value, 2);

				if (paidAmount > pricex)
					return 0;
				else
					return pricex - paidAmount;
			}
		}

		public void AddPayment(decimal amount)
		{
			if (Payments == null)
			{
				Payments = new ListOfOrderPaymentRecord();
			}
			Payments.Add(new OrderPaymentRecord(amount));
		}

		public bool ArtRequired => Jobs.Any(j => !j.IsArtworkValidForSubmit());
		public DateTime? RevisedDispatchDate => Jobs.Max(j => j.RequiredByDate);

		public bool? AACNotPerformed => Jobs.Any(_ => _.AACNotPerformed.HasValue && _.AACNotPerformed == true);

		public bool HasSkid(Facility facility)
		{
			var packages = PackDetail.GetPackages(facility);

			//return packages.Any(p => CartonApplication.GetCarton(p.CartonCode).PackagingLevel == 4);
			return packages.Any(p => p.CartonCode.Contains("Skid"));
		}

		public string ReceiverName
		{
			get {

				if (DeliveryAddress.Equals(Customer?.PostalAddress))
					return Customer.Name;

				return
					RecipientName.NullIf() ??
					WLContact?.Name.NullIf() ??
					Contact?.Name.NullIf() ??
					Customer.Name.NullIf() ?? "-";
			}
		}

		public string ReceiverPhone
		{
			get {
				if (DeliveryAddress.Equals(Customer?.PostalAddress))
					return Customer.Contact1.PhoneFull().NullIf() ?? Customer.Contact1.Mobile.NullIf() ?? "-";

				return
					RecipientPhone.NullIf() ?? Contact?.Phone.NullIf() ?? Contact?.Mobile.NullIf() ??
					WLContact?.Phone.NullIf() ?? WLContact?.Mobile.NullIf() ??
					Customer.Contact1.PhoneFull().NullIf() ?? Customer.Contact1.Mobile.NullIf() ?? "-";
			}
		}
		
		public bool HasSplitDelivery  {  
			get {
				return Jobs.Any(j => j.HasSplitDelivery);
			}
		}
		
		public string SplitsSummary()
		{

			if (!HasSplitDelivery) return "";
			var j = Jobs.FirstOrDefault();
			var sb = new StringBuilder();
			sb.AppendLine($"Split delivery for Job {j.Id}");
			foreach(var split in j.Splits)
			{
				var a = split.Address;
				var c = new CourierType(split.Courier);
				var charge = Math.Round(split.CustomerCharge.Value, 2);

				sb.AppendLine($"{split.Quantity} @ {a.Address1} {a.Address2} {a.City}, {a.State} {a.Postcode}");
				sb.AppendLine($"       {c.CourierName} - {c.ServiceName}  @ ${charge}");
				sb.AppendLine(split.BrochureDistPackInfo.Summerize("       "));
				
			}
			return sb.ToString();
		}

	}

	public class ListOfOrderPaymentRecord : HashSet<OrderPaymentRecord>
	{
		public ListOfOrderPaymentRecord() : base()
		{

		}
		public override bool Equals(object obj)
		{
			if (obj is ListOfOrderPaymentRecord other)
			{
				return this.SequenceEqual(other);
			}

			return false;
		}
	}

	public class OrderPaymentRecord
	{
		public virtual decimal? PaidAmount { get; set; }
		public virtual DateTime At { get; set; }

		public OrderPaymentRecord()
		{
			At = DateTime.Now;
		}

		public OrderPaymentRecord(decimal? amount)
		{
			At = DateTime.Now;
			PaidAmount = amount;
		}

		public override bool Equals(object obj)
		{
			if (obj == null) { return false; }

			var o = obj as OrderPaymentRecord;
			if (o == null) { return false; }

			if (At == o.At && decimal.Equals(PaidAmount, o.PaidAmount))
			{
				return true;
			}
			return false;
		}
	}
}
