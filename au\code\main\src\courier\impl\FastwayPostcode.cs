namespace lep.courier.impl
{
	using System;
	using System.Text;

	public class FastwayPostcode
	{
		public virtual string Postcode { get; set; }
        public virtual string Origin { get; set; }
		public virtual FastwayLabelType LabelType { get; set; }

        public override bool Equals(object obj)
        {
            var data = obj as FastwayPostcode;

            if (data == null)
                return false;

            if (data.Postcode == null || data.Origin == null)
                return false;

            return Postcode == data.Postcode && Origin == data.Origin;
        }

        public override int GetHashCode()
        {
            return (Postcode + "." + Origin).GetHashCode();
        }
	}
}
