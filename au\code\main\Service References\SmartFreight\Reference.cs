﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace lep.SmartFreight {
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.ServiceContractAttribute(Namespace="http://www.smartfreight.com/online", ConfigurationName="SmartFreight.SFOv1")]
    public interface SFOv1 {
        
        [System.ServiceModel.OperationContractAttribute(Action="http://www.smartfreight.com/online/SFOv1/Import", ReplyAction="http://www.smartfreight.com/online/SFOv1/ImportResponse")]
        string Import(string id, string passwd, string reference, string consignmentxml);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://www.smartfreight.com/online/SFOv1/Import", ReplyAction="http://www.smartfreight.com/online/SFOv1/ImportResponse")]
        System.Threading.Tasks.Task<string> ImportAsync(string id, string passwd, string reference, string consignmentxml);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://www.smartfreight.com/online/SFOv1/CostComparison", ReplyAction="http://www.smartfreight.com/online/SFOv1/CostComparisonResponse")]
        string CostComparison(string id, string passwd, string reference, string consignmentxml);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://www.smartfreight.com/online/SFOv1/CostComparison", ReplyAction="http://www.smartfreight.com/online/SFOv1/CostComparisonResponse")]
        System.Threading.Tasks.Task<string> CostComparisonAsync(string id, string passwd, string reference, string consignmentxml);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://www.smartfreight.com/online/SFOv1/CalculateRate", ReplyAction="http://www.smartfreight.com/online/SFOv1/CalculateRateResponse")]
        string CalculateRate(string id, string passwd, string reference, string consignmentxml);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://www.smartfreight.com/online/SFOv1/CalculateRate", ReplyAction="http://www.smartfreight.com/online/SFOv1/CalculateRateResponse")]
        System.Threading.Tasks.Task<string> CalculateRateAsync(string id, string passwd, string reference, string consignmentxml);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://www.smartfreight.com/online/SFOv1/SOAPRequest", ReplyAction="http://www.smartfreight.com/online/SFOv1/SOAPRequestResponse")]
        string SOAPRequest(string id, string passwd, string operation, string reference, string parameterxml);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://www.smartfreight.com/online/SFOv1/SOAPRequest", ReplyAction="http://www.smartfreight.com/online/SFOv1/SOAPRequestResponse")]
        System.Threading.Tasks.Task<string> SOAPRequestAsync(string id, string passwd, string operation, string reference, string parameterxml);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://www.smartfreight.com/online/SFOv1/Enquiry", ReplyAction="http://www.smartfreight.com/online/SFOv1/EnquiryResponse")]
        string Enquiry(string id, string passwd, string conid);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://www.smartfreight.com/online/SFOv1/Enquiry", ReplyAction="http://www.smartfreight.com/online/SFOv1/EnquiryResponse")]
        System.Threading.Tasks.Task<string> EnquiryAsync(string id, string passwd, string conid);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://www.smartfreight.com/online/SFOv1/FindCon", ReplyAction="http://www.smartfreight.com/online/SFOv1/FindConResponse")]
        System.Collections.Generic.List<string> FindCon(string id, string passwd, string xmlfield, string fieldvalue, string searchrepository);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://www.smartfreight.com/online/SFOv1/FindCon", ReplyAction="http://www.smartfreight.com/online/SFOv1/FindConResponse")]
        System.Threading.Tasks.Task<System.Collections.Generic.List<string>> FindConAsync(string id, string passwd, string xmlfield, string fieldvalue, string searchrepository);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://www.smartfreight.com/online/SFOv1/UpdateCon", ReplyAction="http://www.smartfreight.com/online/SFOv1/UpdateConResponse")]
        string UpdateCon(string id, string passwd, string conid, string consignmentxml);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://www.smartfreight.com/online/SFOv1/UpdateCon", ReplyAction="http://www.smartfreight.com/online/SFOv1/UpdateConResponse")]
        System.Threading.Tasks.Task<string> UpdateConAsync(string id, string passwd, string conid, string consignmentxml);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://www.smartfreight.com/online/SFOv1/DeleteCon", ReplyAction="http://www.smartfreight.com/online/SFOv1/DeleteConResponse")]
        string DeleteCon(string id, string passwd, string conid);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://www.smartfreight.com/online/SFOv1/DeleteCon", ReplyAction="http://www.smartfreight.com/online/SFOv1/DeleteConResponse")]
        System.Threading.Tasks.Task<string> DeleteConAsync(string id, string passwd, string conid);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://www.smartfreight.com/online/SFOv1/TrackingEvents", ReplyAction="http://www.smartfreight.com/online/SFOv1/TrackingEventsResponse")]
        string TrackingEvents(string id, string passwd, string conid);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://www.smartfreight.com/online/SFOv1/TrackingEvents", ReplyAction="http://www.smartfreight.com/online/SFOv1/TrackingEventsResponse")]
        System.Threading.Tasks.Task<string> TrackingEventsAsync(string id, string passwd, string conid);
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public interface SFOv1Channel : lep.SmartFreight.SFOv1, System.ServiceModel.IClientChannel {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public partial class SFOv1Client : System.ServiceModel.ClientBase<lep.SmartFreight.SFOv1>, lep.SmartFreight.SFOv1 {
        
        public SFOv1Client() {
        }
        
        public SFOv1Client(string endpointConfigurationName) : 
                base(endpointConfigurationName) {
        }
        
        public SFOv1Client(string endpointConfigurationName, string remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public SFOv1Client(string endpointConfigurationName, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public SFOv1Client(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress) {
        }
        
        public string Import(string id, string passwd, string reference, string consignmentxml) {
            return base.Channel.Import(id, passwd, reference, consignmentxml);
        }
        
        public System.Threading.Tasks.Task<string> ImportAsync(string id, string passwd, string reference, string consignmentxml) {
            return base.Channel.ImportAsync(id, passwd, reference, consignmentxml);
        }
        
        public string CostComparison(string id, string passwd, string reference, string consignmentxml) {
            return base.Channel.CostComparison(id, passwd, reference, consignmentxml);
        }
        
        public System.Threading.Tasks.Task<string> CostComparisonAsync(string id, string passwd, string reference, string consignmentxml) {
            return base.Channel.CostComparisonAsync(id, passwd, reference, consignmentxml);
        }
        
        public string CalculateRate(string id, string passwd, string reference, string consignmentxml) {
            return base.Channel.CalculateRate(id, passwd, reference, consignmentxml);
        }
        
        public System.Threading.Tasks.Task<string> CalculateRateAsync(string id, string passwd, string reference, string consignmentxml) {
            return base.Channel.CalculateRateAsync(id, passwd, reference, consignmentxml);
        }
        
        public string SOAPRequest(string id, string passwd, string operation, string reference, string parameterxml) {
            return base.Channel.SOAPRequest(id, passwd, operation, reference, parameterxml);
        }
        
        public System.Threading.Tasks.Task<string> SOAPRequestAsync(string id, string passwd, string operation, string reference, string parameterxml) {
            return base.Channel.SOAPRequestAsync(id, passwd, operation, reference, parameterxml);
        }
        
        public string Enquiry(string id, string passwd, string conid) {
            return base.Channel.Enquiry(id, passwd, conid);
        }
        
        public System.Threading.Tasks.Task<string> EnquiryAsync(string id, string passwd, string conid) {
            return base.Channel.EnquiryAsync(id, passwd, conid);
        }
        
        public System.Collections.Generic.List<string> FindCon(string id, string passwd, string xmlfield, string fieldvalue, string searchrepository) {
            return base.Channel.FindCon(id, passwd, xmlfield, fieldvalue, searchrepository);
        }
        
        public System.Threading.Tasks.Task<System.Collections.Generic.List<string>> FindConAsync(string id, string passwd, string xmlfield, string fieldvalue, string searchrepository) {
            return base.Channel.FindConAsync(id, passwd, xmlfield, fieldvalue, searchrepository);
        }
        
        public string UpdateCon(string id, string passwd, string conid, string consignmentxml) {
            return base.Channel.UpdateCon(id, passwd, conid, consignmentxml);
        }
        
        public System.Threading.Tasks.Task<string> UpdateConAsync(string id, string passwd, string conid, string consignmentxml) {
            return base.Channel.UpdateConAsync(id, passwd, conid, consignmentxml);
        }
        
        public string DeleteCon(string id, string passwd, string conid) {
            return base.Channel.DeleteCon(id, passwd, conid);
        }
        
        public System.Threading.Tasks.Task<string> DeleteConAsync(string id, string passwd, string conid) {
            return base.Channel.DeleteConAsync(id, passwd, conid);
        }
        
        public string TrackingEvents(string id, string passwd, string conid) {
            return base.Channel.TrackingEvents(id, passwd, conid);
        }
        
        public System.Threading.Tasks.Task<string> TrackingEventsAsync(string id, string passwd, string conid) {
            return base.Channel.TrackingEventsAsync(id, passwd, conid);
        }
    }
}
