<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2"
                   namespace="lep.order"
                   assembly="lep"
                   auto-import="true"
                   default-cascade="all">

    <class name="IOrder" table="`Order`" discriminator-value="null">
        <cache usage="read-write" />
        <id name="Id" type="Int32" unsaved-value="0">
            <generator class="identity" />
        </id>
        <discriminator column="Id" type="Int32" insert="false" />
        <timestamp name="DateModified" column="DateModified" />
        <many-to-one name="Customer" class="lep.user.impl.CustomerUser, lep" column="UserId" not-null="true" cascade="none" />
        <property name="CustomerLogoRequired" type="YesNo" not-null="false" />
        <property name="CustomerLogoYourRef" length="200" />
        <property name="RecipientName" length="200" />
        <property name="RecipientPhone" length="12" />
        <property name="Status" type="lep.GenericEnum`1[lep.OrderStatusOptions], lep" not-null="true" />
        <!--<property name="ExtraStatus" />-->

        <!--<property name="Enable" column="IsEnable" type="YesNo" not-null="true" />-->
        <property name="PaymentStatus" type="lep.GenericEnum`1[lep.OrderPaymentStatusOptions], lep" not-null="true" />
        <property name="FileRemoved" type="YesNo" not-null="true" />
        <property name="PurchaseOrder" length="40" />
        <property name="FreightPriceCode" column="FreightPriceCode" length="5" not-null="false" />
        <property name="DispatchEst" />

        <!--   <property name="Price"  column="Price2"  not-null="false" /> -->

        <!--<property name="IsSpecialInstructions"
        formula="(select case when exists ( select id from job where job.OrderId  = Id  and    DATALENGTH(job.SpecialInstructions) > 0) then 'yes' else 'no' end)"
        type="string"   />-->

        <!--<property name="JobType"
        formula="(SELECT distinct jo.Name + '.' FROM   Job INNER JOIN JobOption jo ON Job.JobOptionId = jo.Id WHERE  (Job.OrderId = Id) FOR XML PATH(''))"
        type="string"   />-->

        <!--formula="(SELECT distinct ps.Name + '.' FROM   Job INNER JOIN PaperSize ps ON Job.FinishedPaperSizeId = ps.Id  WHERE  (Job.OrderId = Id) FOR XML PATH(''))"-->
        <!--<property name="JobSize"
        formula="(SELECT  dbo.GetOrderPaperSizes(Id))"
        type="string"   />-->

        <!--<property name="NumJobs"
        formula="(SELECT count(*) FROM Job WHERE  (Job.OrderId = Id))"  type="integer"   />-->

        <!--<property name="Description"
        formula="(SELECT Concat(job1.Name, ' - ', job1.Quantity, '.') FROM  [Order] AS ord1 INNER JOIN Job AS job1 ON ord1.Id = job1.OrderId WHERE  (ord1.Id = Id) FOR XML PATH(''))"  type="string" />-->

        <component name="DeliveryAddress" class="lep.address.impl.PhysicalAddress, lep">
            <property name="Address1" length="80" not-null="true" />
            <property name="Address2" length="80" />
            <property name="Address3" length="80" />
            <property name="City" length="80" not-null="true" />
            <property name="State" length="40" not-null="true" />
            <property name="Postcode" length="10" not-null="true" />
            <property name="Country" length="80" not-null="true" />
        </component>
        <property name="DeliveryInstructions" type="StringClob" />
        <component name="Contact" class="lep.contact.impl.Contact, lep">
            <property name="Name" column="ContactName" length="80" not-null="true" />
            <property name="Phone" column="ContactPhone" length="12" />
            <property name="AreaCode" column="ContactAreaCode" length="6" />
            <property name="Mobile" column="ContactMobile" length="12" />
            <property name="Fax" column="ContactFax" length="12" />
            <property name="Email" column="ContactEmail" />
        </component>

        <bag name="Jobs" cascade="all-delete-orphan" lazy="true">
            <key column="OrderId" />
            <one-to-many class="lep.job.IJob, lep" />
        </bag>

        <property name="Payments" column="Payments"  not-null="false"
                  type="lep.JsonType`1[lep.order.impl.ListOfOrderPaymentRecord], lep" />

        <bag name="OrderCredits" cascade="all-delete-orphan" lazy="true">
            <key column="OrderId" />
            <one-to-many class="lep.order.OrderCredit, lep" />
        </bag>

        <bag name="ConNotes" cascade="all-delete-orphan" lazy="true">
            <key column="OrderId" />
            <one-to-many class="lep.order.IOrderConNote, lep" />
        </bag>

        <many-to-one name="Promotion" class="lep.promotion.impl.Promotion, lep" column="PromotionId" cascade="none" />
        <property name="IsQuote" type="YesNo" not-null="true" />
        <property name="PromotionJobPriceBenefit" type="Decimal" not-null="true" />
        <property name="PromotionBenefit" type="Decimal" not-null="true" />
        <property name="PickUpCharge" type="Decimal" not-null="true" />

        <property name="GST"  column="GST"   not-null="true" />


        <property name="PackWithoutPallets" type="YesNo" not-null="true" />
        <component name="PackDetail" class="lep.freight.impl.PackDetail, lep">
            <property name="Price" type="Decimal" column="FreightPrice" not-null="false" />

            <component name="FGCourier"  class="lep.courier.CourierType, lep">
                <property name="CourierName" column="FGSuggestedCourier"  length="50" not-null="false" />
                <property name="ServiceName" column="FGSuggestedCourierService"  length="50"  not-null="false" />
                <property name="CarrierAccount" column="FGSuggestedCourierAccount"  length="50"  not-null="false" />
            </component>
            <property name="FGPackingSpecification" type="StringClob" />
            <property name="IsFGCourierCustom" type="YesNo" />

            <component name="PMCourier"  class="lep.courier.CourierType, lep">
                <property name="CourierName" column="PMSuggestedCourier"  length="50"  not-null="false" />
                <property name="ServiceName" column="PMSuggestedCourierService"  length="50"  not-null="false" />
                <property name="CarrierAccount" column="PMSuggestedCourierAccount"  length="50"  not-null="false" />
            </component>
            <property name="PMPackingSpecification" type="StringClob" />
            <property name="IsPMCourierCustom" type="YesNo" />

            <property name="FGPackageJson"   type="lep.JsonType`1[lep.freight.ListOfPackages], lep" />
            <property name="PMPackageJson"   type="lep.JsonType`1[lep.freight.ListOfPackages], lep" />

            <property name="IsCustom" column="IsFreightCustom" type="YesNo" />
            <property name="IsCustomPackages" column="IsCustomPackages" type="YesNo" />
        </component>

        <bag name="DispatchLabels" cascade="all-delete-orphan" lazy="true">
            <key column="OrderId" />
            <one-to-many class="lep.freight.IDispatchLabel, lep" />
        </bag>

        <property name="SubmissionDate" />
        <!--type="lumen.hibernate.type.DateTimeType, lumen"-->

        <!--<property name="Courier" column="PreferredCourier"  type="lep.courier.CourierType, lep" not-null="true" />-->
        <component name="Courier"  class="lep.courier.CourierType, lep">
            <property name="CourierName" column="PreferredCourier"  length="50" not-null="false" />
            <property name="ServiceName" column="PreferredCourierService"  length="50" not-null="false" />
            <property name="CarrierAccount" column="PreferredCourierAccount"  length="50" not-null="false" />
        </component>

        <property name="DispatchDate" />
        <!--type="lumen.hibernate.type.DateTimeType, lumen"-->
        <property name="FinishDate" />
        <property name="Invoiced" not-null="false" />
        <property name="Invoiced2" not-null="false" />

        <property name="FacilityAtSubmit" column="FacilityAtSubmit" type="lep.GenericEnum`1[lep.job.Facility], lep" not-null="false" />
        <property name="IsWLOrder" type="YesNo" not-null="true" />
        <property name="WLCustomerId" not-null="false" />
        <property name="WLCustomerName" not-null="false" formula=" (Select wlc.Name from Customer as wlc where wlc.CustomerId = WLCustomerId) " />

        <property name="WLAnonymousUserId" not-null="false" />

        <property name="IsWLOrderPaidFor" type="YesNo" not-null="true" />
        <property name="WLOrderPaymentDetails"  not-null="false" />
        <property name="WLContact" type="lep.JsonType`1[lep.contact.impl.Contact], lep" />

        <property name="IsDeleted" type="YesNo" not-null="false" />

        <property name="DateCreated" column="DateCreated" type="lumen.hibernate.type.DateTimeType, lumen" not-null="false" update="false" insert="false" />
        <subclass name="lep.order.impl.Order, lep" proxy="IOrder" discriminator-value="not null" />
    </class>

    <!--<class name="OrderFreightLog" table="OrderFreightLog">
        <cache usage="read-write" />
        <id name="Id" type="Int32">
            <generator class="assigned" />
        </id>
        <property name="JobDetail" type="StringClob" />
        <property name="PackLog" type="StringClob" />
        <property name="CourierLog" type="StringClob" />
        <property name="CustomerPriceLog" type="StringClob" />
    </class>-->

    <class name="OrderCredit" table="OrderCredit"  discriminator-value="null">
        <cache usage="read-write" />
        <id name="Id" type="Int32" unsaved-value="0">
            <generator class="identity" />
        </id>
        <discriminator column="Id" type="Int32" insert="false" />
        <timestamp name="DateModified" column="DateModified" />
        <property name="DateCreated" column="DateCreated" not-null="true" />

        <property name="Type" />
        <property name="Description" />
        <property name="Account" />
        <property name="Amount" />
        <property name="GST" />
        <property name="Total" />

        <property name="Approved"  type="YesNo"/>
        <property name="Invoiced"  type="YesNo"/>
        <property name="InvoicedDate" not-null="false"/>

        <many-to-one name="Order" column="OrderId" class="lep.order.IOrder, lep" not-null="false" cascade="none" />
        <many-to-one name="Customer" class="lep.user.impl.CustomerUser, lep" column="CustomerId" not-null="true" cascade="none" />
        <many-to-one name="IssuedBy" class="lep.user.impl.Staff, lep" column="IssuedBy" not-null="true" cascade="none" />
    </class>

    <class name="IOrderConNote" table="ConNote" discriminator-value="null">
        <cache usage="read-write" />
        <id name="Id" type="Int32" unsaved-value="0">
            <generator class="identity" />
        </id>
        <discriminator column="Id" type="Int32" insert="false" />
        <timestamp name="DateModified" column="DateModified" />
        <property name="ConNote" length="25" not-null="true" />
        <property name="DateCreated" column="DateCreated" type="lumen.hibernate.type.DateTimeType, lumen" not-null="false" update="false" insert="false" />
        <property name="IsEmailGenerated"  type="Int32" not-null="true" />
        <property name="DispatchFacility" column="DispatchFacility" type="lep.GenericEnum`1[lep.job.Facility], lep" not-null="true" />

        <property name="CarrierName" length="30" />
        <property name="CarrierService" length="30" />

        <property name="TrackingLabels"  not-null="false" type="lep.JsonType`1[lep.ListOfString], lep" />

        <many-to-one name="Order" column="OrderId" class="lep.order.IOrder, lep" not-null="true" cascade="none" />
        <subclass name="lep.order.impl.OrderConNote, lep" proxy="IOrderConNote" discriminator-value="not null" />
    </class>
</hibernate-mapping>
