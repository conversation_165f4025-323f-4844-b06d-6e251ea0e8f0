<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:wsap="http://schemas.xmlsoap.org/ws/2004/08/addressing/policy" xmlns:wsa10="http://www.w3.org/2005/08/addressing" xmlns:tns="http://www.smartfreight.com/online" xmlns:msc="http://schemas.microsoft.com/ws/2005/12/wsdl/contract" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:wsx="http://schemas.xmlsoap.org/ws/2004/09/mex" xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing" xmlns:wsaw="http://www.w3.org/2006/05/addressing/wsdl" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:xsd="http://www.w3.org/2001/XMLSchema" name="SmartFreightOnlineEndpoint" targetNamespace="http://www.smartfreight.com/online" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <xs:schema elementFormDefault="qualified" targetNamespace="http://www.smartfreight.com/online" xmlns:xs="http://www.w3.org/2001/XMLSchema">
      <xs:import namespace="http://schemas.microsoft.com/2003/10/Serialization/Arrays" />
      <xs:element name="Import">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="id" nillable="true" type="xs:string" />
            <xs:element minOccurs="0" name="passwd" nillable="true" type="xs:string" />
            <xs:element minOccurs="0" name="reference" nillable="true" type="xs:string" />
            <xs:element minOccurs="0" name="consignmentxml" nillable="true" type="xs:string" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="ImportResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="ImportResult" nillable="true" type="xs:string" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="CostComparison">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="id" nillable="true" type="xs:string" />
            <xs:element minOccurs="0" name="passwd" nillable="true" type="xs:string" />
            <xs:element minOccurs="0" name="reference" nillable="true" type="xs:string" />
            <xs:element minOccurs="0" name="consignmentxml" nillable="true" type="xs:string" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="CostComparisonResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="CostComparisonResult" nillable="true" type="xs:string" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="CalculateRate">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="id" nillable="true" type="xs:string" />
            <xs:element minOccurs="0" name="passwd" nillable="true" type="xs:string" />
            <xs:element minOccurs="0" name="reference" nillable="true" type="xs:string" />
            <xs:element minOccurs="0" name="consignmentxml" nillable="true" type="xs:string" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="CalculateRateResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="CalculateRateResult" nillable="true" type="xs:string" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="SOAPRequest">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="id" nillable="true" type="xs:string" />
            <xs:element minOccurs="0" name="passwd" nillable="true" type="xs:string" />
            <xs:element minOccurs="0" name="operation" nillable="true" type="xs:string" />
            <xs:element minOccurs="0" name="reference" nillable="true" type="xs:string" />
            <xs:element minOccurs="0" name="parameterxml" nillable="true" type="xs:string" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="SOAPRequestResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="SOAPRequestResult" nillable="true" type="xs:string" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="Enquiry">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="id" nillable="true" type="xs:string" />
            <xs:element minOccurs="0" name="passwd" nillable="true" type="xs:string" />
            <xs:element minOccurs="0" name="conid" nillable="true" type="xs:string" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="EnquiryResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="EnquiryResult" nillable="true" type="xs:string" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="FindCon">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="id" nillable="true" type="xs:string" />
            <xs:element minOccurs="0" name="passwd" nillable="true" type="xs:string" />
            <xs:element minOccurs="0" name="xmlfield" nillable="true" type="xs:string" />
            <xs:element minOccurs="0" name="fieldvalue" nillable="true" type="xs:string" />
            <xs:element minOccurs="0" name="searchrepository" nillable="true" type="xs:string" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="FindConResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element xmlns:q1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="FindConResult" nillable="true" type="q1:ArrayOfstring" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="UpdateCon">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="id" nillable="true" type="xs:string" />
            <xs:element minOccurs="0" name="passwd" nillable="true" type="xs:string" />
            <xs:element minOccurs="0" name="conid" nillable="true" type="xs:string" />
            <xs:element minOccurs="0" name="consignmentxml" nillable="true" type="xs:string" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="UpdateConResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="UpdateConResult" nillable="true" type="xs:string" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="DeleteCon">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="id" nillable="true" type="xs:string" />
            <xs:element minOccurs="0" name="passwd" nillable="true" type="xs:string" />
            <xs:element minOccurs="0" name="conid" nillable="true" type="xs:string" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="DeleteConResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="DeleteConResult" nillable="true" type="xs:string" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="TrackingEvents">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="id" nillable="true" type="xs:string" />
            <xs:element minOccurs="0" name="passwd" nillable="true" type="xs:string" />
            <xs:element minOccurs="0" name="conid" nillable="true" type="xs:string" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="TrackingEventsResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="TrackingEventsResult" nillable="true" type="xs:string" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
    </xs:schema>
    <xs:schema xmlns:tns="http://schemas.microsoft.com/2003/10/Serialization/" attributeFormDefault="qualified" elementFormDefault="qualified" targetNamespace="http://schemas.microsoft.com/2003/10/Serialization/" xmlns:xs="http://www.w3.org/2001/XMLSchema">
      <xs:element name="anyType" nillable="true" type="xs:anyType" />
      <xs:element name="anyURI" nillable="true" type="xs:anyURI" />
      <xs:element name="base64Binary" nillable="true" type="xs:base64Binary" />
      <xs:element name="boolean" nillable="true" type="xs:boolean" />
      <xs:element name="byte" nillable="true" type="xs:byte" />
      <xs:element name="dateTime" nillable="true" type="xs:dateTime" />
      <xs:element name="decimal" nillable="true" type="xs:decimal" />
      <xs:element name="double" nillable="true" type="xs:double" />
      <xs:element name="float" nillable="true" type="xs:float" />
      <xs:element name="int" nillable="true" type="xs:int" />
      <xs:element name="long" nillable="true" type="xs:long" />
      <xs:element name="QName" nillable="true" type="xs:QName" />
      <xs:element name="short" nillable="true" type="xs:short" />
      <xs:element name="string" nillable="true" type="xs:string" />
      <xs:element name="unsignedByte" nillable="true" type="xs:unsignedByte" />
      <xs:element name="unsignedInt" nillable="true" type="xs:unsignedInt" />
      <xs:element name="unsignedLong" nillable="true" type="xs:unsignedLong" />
      <xs:element name="unsignedShort" nillable="true" type="xs:unsignedShort" />
      <xs:element name="char" nillable="true" type="tns:char" />
      <xs:simpleType name="char">
        <xs:restriction base="xs:int" />
      </xs:simpleType>
      <xs:element name="duration" nillable="true" type="tns:duration" />
      <xs:simpleType name="duration">
        <xs:restriction base="xs:duration">
          <xs:pattern value="\-?P(\d*D)?(T(\d*H)?(\d*M)?(\d*(\.\d*)?S)?)?" />
          <xs:minInclusive value="-P10675199DT2H48M5.4775808S" />
          <xs:maxInclusive value="P10675199DT2H48M5.4775807S" />
        </xs:restriction>
      </xs:simpleType>
      <xs:element name="guid" nillable="true" type="tns:guid" />
      <xs:simpleType name="guid">
        <xs:restriction base="xs:string">
          <xs:pattern value="[\da-fA-F]{8}-[\da-fA-F]{4}-[\da-fA-F]{4}-[\da-fA-F]{4}-[\da-fA-F]{12}" />
        </xs:restriction>
      </xs:simpleType>
      <xs:attribute name="FactoryType" type="xs:QName" />
      <xs:attribute name="Id" type="xs:ID" />
      <xs:attribute name="Ref" type="xs:IDREF" />
    </xs:schema>
    <xs:schema xmlns:tns="http://schemas.microsoft.com/2003/10/Serialization/Arrays" elementFormDefault="qualified" targetNamespace="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:xs="http://www.w3.org/2001/XMLSchema">
      <xs:complexType name="ArrayOfstring">
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="string" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="ArrayOfstring" nillable="true" type="tns:ArrayOfstring" />
    </xs:schema>
  </wsdl:types>
  <wsdl:message name="SFOv1_Import_InputMessage">
    <wsdl:part name="parameters" element="tns:Import" />
  </wsdl:message>
  <wsdl:message name="SFOv1_Import_OutputMessage">
    <wsdl:part name="parameters" element="tns:ImportResponse" />
  </wsdl:message>
  <wsdl:message name="SFOv1_CostComparison_InputMessage">
    <wsdl:part name="parameters" element="tns:CostComparison" />
  </wsdl:message>
  <wsdl:message name="SFOv1_CostComparison_OutputMessage">
    <wsdl:part name="parameters" element="tns:CostComparisonResponse" />
  </wsdl:message>
  <wsdl:message name="SFOv1_CalculateRate_InputMessage">
    <wsdl:part name="parameters" element="tns:CalculateRate" />
  </wsdl:message>
  <wsdl:message name="SFOv1_CalculateRate_OutputMessage">
    <wsdl:part name="parameters" element="tns:CalculateRateResponse" />
  </wsdl:message>
  <wsdl:message name="SFOv1_SOAPRequest_InputMessage">
    <wsdl:part name="parameters" element="tns:SOAPRequest" />
  </wsdl:message>
  <wsdl:message name="SFOv1_SOAPRequest_OutputMessage">
    <wsdl:part name="parameters" element="tns:SOAPRequestResponse" />
  </wsdl:message>
  <wsdl:message name="SFOv1_Enquiry_InputMessage">
    <wsdl:part name="parameters" element="tns:Enquiry" />
  </wsdl:message>
  <wsdl:message name="SFOv1_Enquiry_OutputMessage">
    <wsdl:part name="parameters" element="tns:EnquiryResponse" />
  </wsdl:message>
  <wsdl:message name="SFOv1_FindCon_InputMessage">
    <wsdl:part name="parameters" element="tns:FindCon" />
  </wsdl:message>
  <wsdl:message name="SFOv1_FindCon_OutputMessage">
    <wsdl:part name="parameters" element="tns:FindConResponse" />
  </wsdl:message>
  <wsdl:message name="SFOv1_UpdateCon_InputMessage">
    <wsdl:part name="parameters" element="tns:UpdateCon" />
  </wsdl:message>
  <wsdl:message name="SFOv1_UpdateCon_OutputMessage">
    <wsdl:part name="parameters" element="tns:UpdateConResponse" />
  </wsdl:message>
  <wsdl:message name="SFOv1_DeleteCon_InputMessage">
    <wsdl:part name="parameters" element="tns:DeleteCon" />
  </wsdl:message>
  <wsdl:message name="SFOv1_DeleteCon_OutputMessage">
    <wsdl:part name="parameters" element="tns:DeleteConResponse" />
  </wsdl:message>
  <wsdl:message name="SFOv1_TrackingEvents_InputMessage">
    <wsdl:part name="parameters" element="tns:TrackingEvents" />
  </wsdl:message>
  <wsdl:message name="SFOv1_TrackingEvents_OutputMessage">
    <wsdl:part name="parameters" element="tns:TrackingEventsResponse" />
  </wsdl:message>
  <wsdl:portType name="SFOv1">
    <wsdl:operation name="Import">
      <wsdl:input wsaw:Action="http://www.smartfreight.com/online/SFOv1/Import" message="tns:SFOv1_Import_InputMessage" />
      <wsdl:output wsaw:Action="http://www.smartfreight.com/online/SFOv1/ImportResponse" message="tns:SFOv1_Import_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="CostComparison">
      <wsdl:input wsaw:Action="http://www.smartfreight.com/online/SFOv1/CostComparison" message="tns:SFOv1_CostComparison_InputMessage" />
      <wsdl:output wsaw:Action="http://www.smartfreight.com/online/SFOv1/CostComparisonResponse" message="tns:SFOv1_CostComparison_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="CalculateRate">
      <wsdl:input wsaw:Action="http://www.smartfreight.com/online/SFOv1/CalculateRate" message="tns:SFOv1_CalculateRate_InputMessage" />
      <wsdl:output wsaw:Action="http://www.smartfreight.com/online/SFOv1/CalculateRateResponse" message="tns:SFOv1_CalculateRate_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SOAPRequest">
      <wsdl:input wsaw:Action="http://www.smartfreight.com/online/SFOv1/SOAPRequest" message="tns:SFOv1_SOAPRequest_InputMessage" />
      <wsdl:output wsaw:Action="http://www.smartfreight.com/online/SFOv1/SOAPRequestResponse" message="tns:SFOv1_SOAPRequest_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="Enquiry">
      <wsdl:input wsaw:Action="http://www.smartfreight.com/online/SFOv1/Enquiry" message="tns:SFOv1_Enquiry_InputMessage" />
      <wsdl:output wsaw:Action="http://www.smartfreight.com/online/SFOv1/EnquiryResponse" message="tns:SFOv1_Enquiry_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="FindCon">
      <wsdl:input wsaw:Action="http://www.smartfreight.com/online/SFOv1/FindCon" message="tns:SFOv1_FindCon_InputMessage" />
      <wsdl:output wsaw:Action="http://www.smartfreight.com/online/SFOv1/FindConResponse" message="tns:SFOv1_FindCon_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="UpdateCon">
      <wsdl:input wsaw:Action="http://www.smartfreight.com/online/SFOv1/UpdateCon" message="tns:SFOv1_UpdateCon_InputMessage" />
      <wsdl:output wsaw:Action="http://www.smartfreight.com/online/SFOv1/UpdateConResponse" message="tns:SFOv1_UpdateCon_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="DeleteCon">
      <wsdl:input wsaw:Action="http://www.smartfreight.com/online/SFOv1/DeleteCon" message="tns:SFOv1_DeleteCon_InputMessage" />
      <wsdl:output wsaw:Action="http://www.smartfreight.com/online/SFOv1/DeleteConResponse" message="tns:SFOv1_DeleteCon_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="TrackingEvents">
      <wsdl:input wsaw:Action="http://www.smartfreight.com/online/SFOv1/TrackingEvents" message="tns:SFOv1_TrackingEvents_InputMessage" />
      <wsdl:output wsaw:Action="http://www.smartfreight.com/online/SFOv1/TrackingEventsResponse" message="tns:SFOv1_TrackingEvents_OutputMessage" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="BasicHttpBinding_SFOv1" type="tns:SFOv1">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="Import">
      <soap:operation soapAction="http://www.smartfreight.com/online/SFOv1/Import" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CostComparison">
      <soap:operation soapAction="http://www.smartfreight.com/online/SFOv1/CostComparison" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CalculateRate">
      <soap:operation soapAction="http://www.smartfreight.com/online/SFOv1/CalculateRate" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SOAPRequest">
      <soap:operation soapAction="http://www.smartfreight.com/online/SFOv1/SOAPRequest" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Enquiry">
      <soap:operation soapAction="http://www.smartfreight.com/online/SFOv1/Enquiry" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="FindCon">
      <soap:operation soapAction="http://www.smartfreight.com/online/SFOv1/FindCon" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateCon">
      <soap:operation soapAction="http://www.smartfreight.com/online/SFOv1/UpdateCon" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteCon">
      <soap:operation soapAction="http://www.smartfreight.com/online/SFOv1/DeleteCon" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TrackingEvents">
      <soap:operation soapAction="http://www.smartfreight.com/online/SFOv1/TrackingEvents" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="SmartFreightOnlineEndpoint">
    <wsdl:port name="BasicHttpBinding_SFOv1" binding="tns:BasicHttpBinding_SFOv1">
      <soap:address location="http://api-r1.smartfreight.com/api/soap/classic" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>