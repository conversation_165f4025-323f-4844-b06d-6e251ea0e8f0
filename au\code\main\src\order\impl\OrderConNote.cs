﻿using lep.job;
using System;
using System.Collections.Generic;

namespace lep.order.impl
{
	public class OrderConNote : IOrderConNote
	{
		public virtual int Id { get; set; }
		public virtual int OrderId { get; set; }
		public virtual string ConNote { get; set; }
		public virtual int IsEmailGenerated { get; set; }
		public virtual DateTime DateCreated { get; set; }
		public virtual DateTime DateModified { get; set; }
		public virtual IOrder Order { get; set; }
		public virtual Facility DispatchFacility { get; set; }

		public virtual string CarrierName { get; set; }
		public virtual string CarrierService { get; set; }
		public virtual IList<string> TrackingLabels { get; set; } = new List<string>();
	}
}
