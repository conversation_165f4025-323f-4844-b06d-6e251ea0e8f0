
using lep.job.impl;
using Newtonsoft.Json;
using System.IO;
using System;
using lep.freight.impl;
using System.Collections.Generic;
using CsvHelper;
using System.Globalization;
using System.Linq;
using System.Reflection;
namespace lep
{
	[AttributeUsage(AttributeTargets.Property)]
	public class CsvFilePathAttribute : Attribute
	{
		public string Path { get; }

		public CsvFilePathAttribute(string path)
		{
			Path = path;
		}
	}
}


namespace lep.job
{

	// static class for Wiro providing extension methods to Ijob

	public class WiroMagazineInfo
	{
		// Outer properties
		public string OuterFront { get; set; }
		public string OuterBack { get; set; }

		// Inner properties
		public string InnerFrontCello { get; set; }
		public string InnerBackCello { get; set; }

		// Stock properties
		public Stock InnerFrontStockForCover { get; set; }
		public Stock InnerBackStockForCover { get; set; }

		// Other properties
		public string WireColor { get; set; }
		public decimal CoilThickness { get; set; }
	}




	public class WiroMagazineValues
	{

		private dynamic _wiroInfo;
		public WiroMagazineValues(string jobOptionCSVsFolder)
		{
			var jsonFilePath = Path.Combine(jobOptionCSVsFolder, "WiroMagazineValues.json");
			var jsonData = File.ReadAllText(jsonFilePath);
			_wiroInfo = JsonConvert.DeserializeObject<dynamic>(jsonData);


			//LoadFromCsv(jobOptionCSVsFolder);
		}

		public dynamic GetWiroMagazineValues()
		{
			return _wiroInfo;
		}


		[CsvFilePath("Wiro-InnerCover.csv")]
		public List<InnerCover> InnerCovers { get; set; }

		[CsvFilePath("Wiro-InnerCoverCello.csv")]
		public List<InnerCoverCelloPrice> InnerCoverCelloPrices { get; set; }

		[CsvFilePath("Wiro-OuterCover.csv")]
		public List<OuterCover> OuterCovers { get; set; }

		[CsvFilePath("Wiro-Coils.csv")]
		public List<WiroCoil> WiroCoils { get; set; }

		public List<T> ReadFromCsv<T>(string csvPath)
		{
			using var reader = new StreamReader(csvPath);
			using var csv = new CsvReader(reader, CultureInfo.InvariantCulture);
			return csv.GetRecords<T>().ToList();
		}
		public void LoadFromCsv(string jobOptionCSVsFolder)
		{
			var properties = GetType().GetProperties();
			foreach (var property in properties)
			{
				var attribute = property.GetCustomAttribute<CsvFilePathAttribute>();
				if (attribute != null)
				{
					var fullPath = Path.Combine(jobOptionCSVsFolder, attribute.Path);
					if (property.PropertyType.IsGenericType && property.PropertyType.GetGenericTypeDefinition() == typeof(List<>))
					{
						var itemTypes = property.PropertyType.GetGenericArguments();
						var itemType = property.PropertyType.GetGenericArguments()[0];
						var method = typeof(WiroMagazineValues).GetMethod("ReadFromCsv").MakeGenericMethod(itemType);
						var list = method.Invoke(this, new object[] { fullPath });
						property.SetValue(this, list);
					}
				}
			}
		}


	}

	public record InnerCover(int Id, string Name, decimal UnitPrice_A4, decimal UnitPrice_A5);
	public record InnerCoverCelloPrice(string Type, decimal Price);
	public record OuterCover(string CoverType, decimal Price, decimal Thickness, decimal Weight);
	public record WiroCoil(decimal Thickness, decimal Price, decimal MaxBookThickness);

}
