using System.Diagnostics;

namespace lep.job.impl
{
	[DebuggerDisplay("{Minium} {Step1} {Change} {Step2} {Change2}")]
	public class JobOptionSpecQuantity : IJobOptionSpecQuantity
	{
		public JobOptionSpecQuantity()
		{
		}

		public JobOptionSpecQuantity(int _minium, int _step1, int _change, int _step2, int _change2)
		{
			Minium = _minium;
			Step1 = _step1;
			Change = _change;
			Step2 = _step2;
			Change2 = _change2;
		}

		public virtual int Minium { get; set; }

		public virtual int Step1 { get; set; }

		public virtual int Change { get; set; }

		public virtual int Step2 { get; set; }

		public virtual int Change2 { get; set; }
	}
}
