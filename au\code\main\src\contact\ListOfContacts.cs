using System.Collections.Generic;
using System.Linq;

namespace lep.contact
{
    public class ListOfContacts : List<impl.Contact>
    {
        public ListOfContacts() : base(new List<impl.Contact>())
        {
        }

        public override bool Equals(object obj)
        {
            if (obj is ListOfContacts other)
            {
                return this.SequenceEqual(other);
            }
            return false;
        }
    }
}