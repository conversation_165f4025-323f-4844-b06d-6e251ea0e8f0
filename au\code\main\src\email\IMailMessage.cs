using System.Net.Mail;
using System.Text;

namespace lep.email
{
    public interface IMailMessage
    {
        string ReplyTo { get; set; }
        string From { get; set; }
        string To { get; set; }
        string BCC { get; set; }
        string Subject { get; set; }
        string Body { get; set; }
        bool IsBodyHtml { get; set; }
        Encoding BodyEncoding { get; set; }
        MailPriority Priority { get; set; }

        MailMessage ToMailMessage();
    }
}