namespace lep.despatch.impl
{
    ////printapplication and printengine has to be Separated because receive will lock the process
    //public class PrintApplication : IDisposable, IInitializingObject
    //{
    //    static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

    //    MessageQueue messageQueue = null;

    //    public PrintApplication()
    //    {
    //    }

    //    public bool IsExternal { protected get; set; }
    //    public string MessageQueueName { protected get; set; }

    //    public void Dispose()
    //    {
    //    }

    //    public void AfterPropertiesSet()
    //    {
    //    }

    //}
}