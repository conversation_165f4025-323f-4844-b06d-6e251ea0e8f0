using lep.job.impl;
using lumen.csv;
using lumen.csv.handler;
using System.Collections.Generic;

namespace lep.job.csv
{
    public class BaseCsvReader : CsvParser
    {
        protected Queue<CsvException> errors;
        protected JobApplication jobApp;
        protected SkipEmptyRowHandler skipHandler;

        protected Trim<PERSON>hit<PERSON>paceHandler trimHandler;

        public BaseCsvReader()
        {
            HandleQuotes = true;

            trimHandler = new TrimWhitespaceHandler();
            skipHandler = new SkipEmptyRowHandler();
            Handler = trimHandler;
            trimHandler.Handler = skipHandler;
        }

        public Queue<CsvException> Errors
        {
            get { return errors; }
        }

        public JobApplication JobApplication
        {
            set { jobApp = value; }
        }

        public override void StartDocument()
        {
            errors = new Queue<CsvException>();
            base.StartDocument();
        }

        public override void RowData(string[] values)
        {
            try
            {
                base.RowData(values);
            }
            catch (CsvException ex)
            {
                //Collect errors and continue parsing
                errors.Enqueue(ex);
            }
        }

        public override void EndDocument()
        {
            base.EndDocument();

            if (errors.Count > 0)
            {
                throw new CsvException("Errors found parsing CSV");
            }
        }
    }
}