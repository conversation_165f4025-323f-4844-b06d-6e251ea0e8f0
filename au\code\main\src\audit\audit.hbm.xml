<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2"
                   namespace="lep.audit"
                   assembly="lep"
                   auto-import="true"
                   default-cascade="all">
    <class name="Audit" table="PRD_AU_Notes.[dbo].[Audit]"  discriminator-value="null">
        <cache usage="read-write" />
        <id name="Id" type="Int32" unsaved-value="0">
            <generator class="identity" />
        </id>
        <discriminator column="Id" type="Int32" insert="false" />
        <property name="EventDate" column="EventDate" />
        <property name="UserId" />
        <property name="UserName" />
        <property name="Entity" />
        <property name="EntityId" />
        <property name="Body" />
        <property name="IsStaff" type="YesNo" />
    </class>
</hibernate-mapping>
