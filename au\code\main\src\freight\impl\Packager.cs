using lep.freight;
using System;
using System.Collections;
using System.Collections.Generic;

namespace lep.src.freight.impl
{
	//modified from https://github.com/exad/boxologic
	public class Packager
	{
		private bool packing;
		private bool layerdone;
		private bool evened;
		private int variant;
		private int best_variant;
		private bool packingbest;
		private bool hundredpercent;

		private int boxx, boxy, boxz, boxi;
		private int bboxx, bboxy, bboxz, bboxi;
		private int cboxx, cboxy, cboxz, cboxi;
		private int bfx, bfy, bfz;
		private int bbfx, bbfy, bbfz;
		private int xx, yy, zz;
		private int pallet_x, pallet_y, pallet_z;

		private int total_boxes;
		private int x;
		private int layerlistlen;
		private int layerinlayer;
		private int prelayer;
		private int lilz;
		private int number_of_iterations;
		private int layersindex;
		private int remainpy, remainpz;
		private int packedy;
		private int prepackedy;
		private int layerthickness;
		private int itelayer;
		private int preremainpy;
		private int best_iteration;
		private int packednumbox;
		private int number_packed_boxes;

		private double packedvolume;
		private double best_solution_volume;
		private double total_pallet_volume;
		private double total_box_volume;
		private double temp;
		private double pallet_volume_used_percentage;
		private double packed_box_percentage;

		private struct Boxinfo
		{
			public bool is_packed;
			public int dim1, dim2, dim3, n, cox, coy, coz, packx, packy, packz;
			public int vol;
			public int boxIndex;
		}

		private Boxinfo[] boxlist = new Boxinfo[1500];

		private struct Layerlist
		{
			public int layereval;
			public int layerdim;
		}

		private Layerlist[] layers = new Layerlist[50];

		private class Scrappad
		{
			public Scrappad prev, next;
			public int cumx, cumz;
		};

		~Packager()
		{
			boxlist = null;
			layers = null;
		}

		public struct Item
		{
			public int width;
			public int depth;
			public int height;
			public decimal weight;
			public int itemIndex;
		}

		public class Container
		{
			public IList<int> items;
			public ICarton carton;
			public double volume_used;
		}

		private Scrappad scrapfirst, scrapmemb, smallestz, trash;

		private List<int> packItems;

		public Container Pack(ICarton container, List<Item> items)
		{
			read_boxlist_input(container, items);
			initialize();
			execiterations();
			do_best_pack();

			boxlist = null;
			layers = null;

			return new Container
			{
				carton = container,
				items = packItems,
				volume_used = best_solution_volume
			};
		}

		private void read_boxlist_input(ICarton container, List<Item> items)
		{
			//TODO: Robustify this so the box label can be larger and have spaces

			total_boxes = 1;

			xx = container.IWidth;
			yy = container.IHeight;
			zz = container.IDepth;

			foreach (var b in items)
			{
				boxlist[total_boxes].dim1 = b.width;
				boxlist[total_boxes].dim2 = b.height;
				boxlist[total_boxes].dim3 = b.depth;
				boxlist[total_boxes].vol = boxlist[total_boxes].dim1 * boxlist[total_boxes].dim2 * boxlist[total_boxes].dim3;
				boxlist[total_boxes].boxIndex = b.itemIndex;
				boxlist[total_boxes].n = 1;
				boxlist[total_boxes + 1] = boxlist[total_boxes];
				total_boxes += 1;
			}
			--total_boxes;
		}

		private void initialize()
		{
			temp = 1.0;
			total_pallet_volume = temp * xx * yy * zz;
			total_box_volume = 0.0;
			for (x = 1; x <= total_boxes; x++)
			{
				total_box_volume = total_box_volume + boxlist[x].vol;
			}

			scrapfirst = new Scrappad();
			best_solution_volume = 0.0;
			packingbest = false;
			hundredpercent = false;
			number_of_iterations = 0;
		}

		private class LayerComparer : IComparer
		{
			public int Compare(Object x, Object y)
			{
				var tmpx = (Layerlist)x;
				var tmpy = (Layerlist)y;
				return (tmpx.layerdim + tmpx.layereval).CompareTo(tmpy.layerdim + tmpy.layereval);
			}
		}

		private void execiterations()
		{
			for (variant = 1; variant <= 6; variant++)
			{
				switch (variant)
				{
					case 1:
						pallet_x = xx; pallet_y = yy; pallet_z = zz;
						break;

					case 2:
						pallet_x = zz; pallet_y = yy; pallet_z = xx;
						break;

					case 3:
						pallet_x = zz; pallet_y = xx; pallet_z = yy;
						break;

					case 4:
						pallet_x = yy; pallet_y = xx; pallet_z = zz;
						break;

					case 5:
						pallet_x = xx; pallet_y = zz; pallet_z = yy;
						break;

					case 6:
						pallet_x = yy; pallet_y = zz; pallet_z = xx;
						break;
				}

				list_candidate_layers();
				layers[0].layereval = -1;
				Array.Sort(layers, 0, layerlistlen + 1, new LayerComparer());

				for (layersindex = 1; layersindex <= layerlistlen; layersindex++)
				{
					++number_of_iterations;
					packedvolume = 0.0;
					packedy = 0;
					packing = true;
					layerthickness = layers[layersindex].layerdim;
					itelayer = layersindex;
					remainpy = pallet_y;
					remainpz = pallet_z;
					packednumbox = 0;
					for (x = 1; x <= total_boxes; x++)
					{
						boxlist[x].is_packed = false;
					}

					//BEGIN DO-WHILE
					do
					{
						layerinlayer = 0;
						layerdone = false;
						if (pack_layer())
						{
							throw new Exception("unable to pack");
						}
						packedy = packedy + layerthickness;
						remainpy = pallet_y - packedy;
						if (layerinlayer != 0)
						{
							prepackedy = packedy;
							preremainpy = remainpy;
							remainpy = layerthickness - prelayer;
							packedy = packedy - layerthickness + prelayer;
							remainpz = lilz;
							layerthickness = layerinlayer;
							layerdone = false;
							if (pack_layer())
							{
								throw new Exception("unable to pack");
							}
							packedy = prepackedy;
							remainpy = preremainpy;
							remainpz = pallet_z;
						}
						find_layer(remainpy);
					}
					while (packing);
					// END DO-WHILE

					if (packedvolume > best_solution_volume)
					{
						best_solution_volume = packedvolume;
						best_variant = variant;
						best_iteration = itelayer;
						number_packed_boxes = packednumbox;
					}

					if (hundredpercent) break;
					pallet_volume_used_percentage = best_solution_volume * 100 / total_pallet_volume;
				}
				if (hundredpercent) break;
				if ((xx == yy) && (yy == zz)) variant = 6;
			}
		}

		private int find_layer(int thickness)
		{
			int exdim, dimdif, dimen2, dimen3, y, z;
			int layereval, eval;
			layerthickness = 0;
			eval = 1000000;
			for (x = 1; x <= total_boxes; x++)
			{
				if (boxlist[x].is_packed) continue;
				for (y = 1; y <= 3; y++)
				{
					switch (y)
					{
						case 1:
							exdim = boxlist[x].dim1;
							dimen2 = boxlist[x].dim2;
							dimen3 = boxlist[x].dim3;
							break;

						case 2:
							exdim = boxlist[x].dim2;
							dimen2 = boxlist[x].dim1;
							dimen3 = boxlist[x].dim3;
							break;

						case 3:
						default:
							exdim = boxlist[x].dim3;
							dimen2 = boxlist[x].dim1;
							dimen3 = boxlist[x].dim2;
							break;
					}
					layereval = 0;
					if ((exdim <= thickness) && (((dimen2 <= pallet_x) && (dimen3 <= pallet_z)) || ((dimen3 <= pallet_x) && (dimen2 <= pallet_z))))
					{
						for (z = 1; z <= total_boxes; z++)
						{
							if (!(x == z) && !(boxlist[z].is_packed))
							{
								dimdif = Math.Abs(exdim - boxlist[z].dim1);
								if (Math.Abs(exdim - boxlist[z].dim2) < dimdif)
								{
									dimdif = Math.Abs(exdim - boxlist[z].dim2);
								}
								if (Math.Abs(exdim - boxlist[z].dim3) < dimdif)
								{
									dimdif = Math.Abs(exdim - boxlist[z].dim3);
								}
								layereval = layereval + dimdif;
							}
						}
						if (layereval < eval)
						{
							eval = layereval;
							layerthickness = exdim;
						}
					}
				}
			}
			if (layerthickness == 0 || layerthickness > remainpy) packing = false;
			return 0;
		}

		private void list_candidate_layers()
		{
			bool same;
			int exdim, dimdif, dimen2, dimen3, y, z, k;
			int layereval;

			layerlistlen = 0;

			for (x = 1; x <= total_boxes; x++)
			{
				for (y = 1; y <= 3; y++)
				{
					switch (y)
					{
						case 1:
							exdim = boxlist[x].dim1;
							dimen2 = boxlist[x].dim2;
							dimen3 = boxlist[x].dim3;
							break;

						case 2:
							exdim = boxlist[x].dim2;
							dimen2 = boxlist[x].dim1;
							dimen3 = boxlist[x].dim3;
							break;

						case 3:
						default:
							exdim = boxlist[x].dim3;
							dimen2 = boxlist[x].dim1;
							dimen3 = boxlist[x].dim2;
							break;
					}
					if ((exdim > pallet_y) || (((dimen2 > pallet_x) || (dimen3 > pallet_z)) && ((dimen3 > pallet_x) || (dimen2 > pallet_z)))) continue;
					same = false;

					for (k = 1; k <= layerlistlen; k++)
					{
						if (exdim == layers[k].layerdim)
						{
							same = true;
							continue;
						}
					}
					if (same) continue;
					layereval = 0;
					for (z = 1; z <= total_boxes; z++)
					{
						if (!(x == z))
						{
							dimdif = Math.Abs(exdim - boxlist[z].dim1);
							if (Math.Abs(exdim - boxlist[z].dim2) < dimdif)
							{
								dimdif = Math.Abs(exdim - boxlist[z].dim2);
							}
							if (Math.Abs(exdim - boxlist[z].dim3) < dimdif)
							{
								dimdif = Math.Abs(exdim - boxlist[z].dim3);
							}
							layereval = layereval + dimdif;
						}
					}
					++layerlistlen;
					layers[layerlistlen].layereval = layereval;
					layers[layerlistlen].layerdim = exdim;
				}
			}
		}

		private void find_box(int hmx, int hy, int hmy, int hz, int hmz)
		{
			int y;
			bfx = 32767; bfy = 32767; bfz = 32767;
			bbfx = 32767; bbfy = 32767; bbfz = 32767;
			boxi = 0; bboxi = 0;
			for (y = 1; y <= total_boxes; y = y + boxlist[y].n)
			{
				for (x = y; x < x + boxlist[y].n - 1; x++)
				{
					if (!boxlist[x].is_packed) break;
				}
				if (boxlist[x].is_packed) continue;
				if (x > total_boxes) return;
				analyze_box(hmx, hy, hmy, hz, hmz, boxlist[x].dim1, boxlist[x].dim2, boxlist[x].dim3);
				if ((boxlist[x].dim1 == boxlist[x].dim3) && (boxlist[x].dim3 == boxlist[x].dim2)) continue;
				analyze_box(hmx, hy, hmy, hz, hmz, boxlist[x].dim1, boxlist[x].dim3, boxlist[x].dim2);
				analyze_box(hmx, hy, hmy, hz, hmz, boxlist[x].dim2, boxlist[x].dim1, boxlist[x].dim3);
				analyze_box(hmx, hy, hmy, hz, hmz, boxlist[x].dim2, boxlist[x].dim3, boxlist[x].dim1);
				analyze_box(hmx, hy, hmy, hz, hmz, boxlist[x].dim3, boxlist[x].dim1, boxlist[x].dim2);
				analyze_box(hmx, hy, hmy, hz, hmz, boxlist[x].dim3, boxlist[x].dim2, boxlist[x].dim1);
			}
		}

		private void analyze_box(int hmx, int hy, int hmy, int hz, int hmz, int dim1, int dim2, int dim3)
		{
			if (dim1 <= hmx && dim2 <= hmy && dim3 <= hmz)
			{
				if (dim2 <= hy)
				{
					if (hy - dim2 < bfy)
					{
						boxx = dim1;
						boxy = dim2;
						boxz = dim3;
						bfx = hmx - dim1;
						bfy = hy - dim2;
						bfz = Math.Abs(hz - dim3);
						boxi = x;
					}
					else if (hy - dim2 == bfy && hmx - dim1 < bfx)
					{
						boxx = dim1;
						boxy = dim2;
						boxz = dim3;
						bfx = hmx - dim1;
						bfy = hy - dim2;
						bfz = Math.Abs(hz - dim3);
						boxi = x;
					}
					else if (hy - dim2 == bfy && hmx - dim1 == bfx && Math.Abs(hz - dim3) < bfz)
					{
						boxx = dim1;
						boxy = dim2;
						boxz = dim3;
						bfx = hmx - dim1;
						bfy = hy - dim2;
						bfz = Math.Abs(hz - dim3);
						boxi = x;
					}
				}
				else
				{
					if (dim2 - hy < bbfy)
					{
						bboxx = dim1;
						bboxy = dim2;
						bboxz = dim3;
						bbfx = hmx - dim1;
						bbfy = dim2 - hy;
						bbfz = Math.Abs(hz - dim3);
						bboxi = x;
					}
					else if (dim2 - hy == bbfy && hmx - dim1 < bbfx)
					{
						bboxx = dim1;
						bboxy = dim2;
						bboxz = dim3;
						bbfx = hmx - dim1;
						bbfy = dim2 - hy;
						bbfz = Math.Abs(hz - dim3);
						bboxi = x;
					}
					else if (dim2 - hy == bbfy && hmx - dim1 == bbfx && Math.Abs(hz - dim3) < bbfz)
					{
						bboxx = dim1;
						bboxy = dim2;
						bboxz = dim3;
						bbfx = hmx - dim1;
						bbfy = dim2 - hy;
						bbfz = Math.Abs(hz - dim3);
						bboxi = x;
					}
				}
			}
		}

		private void checkfound()
		{
			evened = false;
			if (boxi != 0)
			{
				cboxi = boxi;
				cboxx = boxx;
				cboxy = boxy;
				cboxz = boxz;
			}
			else
			{
				if ((bboxi > 0) && (layerinlayer != 0 || (smallestz.prev == null && smallestz.next == null)))
				{
					if (layerinlayer == 0)
					{
						prelayer = layerthickness;
						lilz = smallestz.cumz;
					}
					cboxi = bboxi;
					cboxx = bboxx;
					cboxy = bboxy;
					cboxz = bboxz;
					layerinlayer = layerinlayer + bboxy - layerthickness;
					layerthickness = bboxy;
				}
				else
				{
					if (smallestz.prev == null && smallestz.next == null)
					{
						layerdone = true;
					}
					else
					{
						evened = true;
						if (smallestz.prev == null)
						{
							trash = smallestz.next;
							smallestz.cumx = smallestz.next.cumx;
							smallestz.cumz = smallestz.next.cumz;
							smallestz.next = smallestz.next.next;
							if (smallestz.next != null)
							{
								smallestz.next.prev = smallestz;
							}
							trash = null;
						}
						else if (smallestz.next == null)
						{
							smallestz.prev.next = null;
							smallestz.prev.cumx = smallestz.cumx;
							smallestz = null;
						}
						else
						{
							if (smallestz.prev.cumz == smallestz.next.cumz)
							{
								smallestz.prev.next = smallestz.next.next;
								if (smallestz.next.next != null)
								{
									smallestz.next.next.prev = smallestz.prev;
								}
								smallestz.prev.cumx = smallestz.next.cumx;
								smallestz.next = null;
								smallestz = null;
							}
							else
							{
								smallestz.prev.next = smallestz.next;
								smallestz.next.prev = smallestz.prev;
								if (smallestz.prev.cumz < smallestz.next.cumz)
								{
									smallestz.prev.cumx = smallestz.cumx;
								}
								smallestz = null;
							}
						}
					}
				}
			}
		}

		private void write_boxlist_file()
		{
			int x, y, z, bx, by, bz;

			switch (best_variant)
			{
				case 1:
					x = boxlist[cboxi].cox;
					y = boxlist[cboxi].coy;
					z = boxlist[cboxi].coz;
					bx = boxlist[cboxi].packx;
					by = boxlist[cboxi].packy;
					bz = boxlist[cboxi].packz;
					break;

				case 2:
					x = boxlist[cboxi].coz;
					y = boxlist[cboxi].coy;
					z = boxlist[cboxi].cox;
					bx = boxlist[cboxi].packz;
					by = boxlist[cboxi].packy;
					bz = boxlist[cboxi].packx;
					break;

				case 3:
					x = boxlist[cboxi].coy;
					y = boxlist[cboxi].coz;
					z = boxlist[cboxi].cox;
					bx = boxlist[cboxi].packy;
					by = boxlist[cboxi].packz;
					bz = boxlist[cboxi].packx;
					break;

				case 4:
					x = boxlist[cboxi].coy;
					y = boxlist[cboxi].cox;
					z = boxlist[cboxi].coz;
					bx = boxlist[cboxi].packy;
					by = boxlist[cboxi].packx;
					bz = boxlist[cboxi].packz;
					break;

				case 5:
					x = boxlist[cboxi].cox;
					y = boxlist[cboxi].coz;
					z = boxlist[cboxi].coy;
					bx = boxlist[cboxi].packx;
					by = boxlist[cboxi].packz;
					bz = boxlist[cboxi].packy;
					break;

				default:
				case 6:
					x = boxlist[cboxi].coz;
					y = boxlist[cboxi].cox;
					z = boxlist[cboxi].coy;
					bx = boxlist[cboxi].packz;
					by = boxlist[cboxi].packx;
					bz = boxlist[cboxi].packy;
					break;
			}

			boxlist[cboxi].cox = x;
			boxlist[cboxi].coy = y;
			boxlist[cboxi].coz = z;
			boxlist[cboxi].packx = bx;
			boxlist[cboxi].packy = by;
			boxlist[cboxi].packz = bz;
			packItems.Add(boxlist[cboxi].boxIndex);
		}

		private void volume_check()
		{
			boxlist[cboxi].is_packed = true;
			boxlist[cboxi].packx = cboxx;
			boxlist[cboxi].packy = cboxy;
			boxlist[cboxi].packz = cboxz;
			packedvolume = packedvolume + boxlist[cboxi].vol;
			packednumbox++;
			if (packingbest)
			{
				write_boxlist_file();
			}
			else if (packedvolume == total_pallet_volume || packedvolume == total_box_volume)
			{
				packing = false;
				hundredpercent = true;
			}
			return;
		}

		private bool pack_layer()
		{
			int lenx, lenz, lpz;

			if (layerthickness == 0)
			{
				packing = false;
				return false;
			}

			scrapfirst.cumx = pallet_x;
			scrapfirst.cumz = 0;

			while (true)
			{
				find_smallest_z();

				if (smallestz.prev == null && smallestz.next == null)
				{
					//*** SITUATION-1: NO BOXES ON THE RIGHT AND LEFT SIDES ***

					lenx = smallestz.cumx;
					lpz = remainpz - smallestz.cumz;
					find_box(lenx, layerthickness, remainpy, lpz, lpz);
					checkfound();

					if (layerdone) break;
					if (evened) continue;

					boxlist[cboxi].cox = 0;
					boxlist[cboxi].coy = packedy;
					boxlist[cboxi].coz = smallestz.cumz;
					if (cboxx == smallestz.cumx)
					{
						smallestz.cumz = smallestz.cumz + cboxz;
					}
					else
					{
						smallestz.next = new Scrappad();
						smallestz.next.prev = smallestz;
						smallestz.next.cumx = smallestz.cumx;
						smallestz.next.cumz = smallestz.cumz;
						smallestz.cumx = cboxx;
						smallestz.cumz = smallestz.cumz + cboxz;
					}
					volume_check();
				}
				else if (smallestz.prev == null)
				{
					//*** SITUATION-2: NO BOXES ON THE LEFT SIDE ***

					lenx = smallestz.cumx;
					lenz = smallestz.next.cumz - smallestz.cumz;
					lpz = remainpz - smallestz.cumz;
					find_box(lenx, layerthickness, remainpy, lenz, lpz);
					checkfound();

					if (layerdone) break;
					if (evened) continue;

					boxlist[cboxi].coy = packedy;
					boxlist[cboxi].coz = smallestz.cumz;
					if (cboxx == smallestz.cumx)
					{
						boxlist[cboxi].cox = 0;
						if (smallestz.cumz + cboxz == smallestz.next.cumz)
						{
							smallestz.cumz = smallestz.next.cumz;
							smallestz.cumx = smallestz.next.cumx;
							trash = smallestz.next;
							smallestz.next = smallestz.next.next;
							if (smallestz.next != null)
							{
								smallestz.next.prev = smallestz;
							}
							trash = null;
						}
						else
						{
							smallestz.cumz = smallestz.cumz + cboxz;
						}
					}
					else
					{
						boxlist[cboxi].cox = smallestz.cumx - cboxx;
						if (smallestz.cumz + cboxz == smallestz.next.cumz)
						{
							smallestz.cumx = smallestz.cumx - cboxx;
						}
						else
						{
							smallestz.next.prev = new Scrappad();
							smallestz.next.prev.next = smallestz.next;
							smallestz.next.prev.prev = smallestz;
							smallestz.next = smallestz.next.prev;
							smallestz.next.cumx = smallestz.cumx;
							smallestz.cumx = smallestz.cumx - cboxx;
							smallestz.next.cumz = smallestz.cumz + cboxz;
						}
					}
					volume_check();
				}
				else if (smallestz.next == null)
				{
					//*** SITUATION-3: NO BOXES ON THE RIGHT SIDE ***

					lenx = smallestz.cumx - smallestz.prev.cumx;
					lenz = smallestz.prev.cumz - smallestz.cumz;
					lpz = remainpz - smallestz.cumz;
					find_box(lenx, layerthickness, remainpy, lenz, lpz);
					checkfound();

					if (layerdone) break;
					if (evened) continue;

					boxlist[cboxi].coy = packedy;
					boxlist[cboxi].coz = smallestz.cumz;
					boxlist[cboxi].cox = smallestz.prev.cumx;

					if (cboxx == smallestz.cumx - smallestz.prev.cumx)
					{
						if (smallestz.cumz + cboxz == smallestz.prev.cumz)
						{
							smallestz.prev.cumx = smallestz.cumx;
							smallestz.prev.next = null;
							smallestz = null;
						}
						else
						{
							smallestz.cumz = smallestz.cumz + cboxz;
						}
					}
					else
					{
						if (smallestz.cumz + cboxz == smallestz.prev.cumz)
						{
							smallestz.prev.cumx = smallestz.prev.cumx + cboxx;
						}
						else
						{
							smallestz.prev.next = new Scrappad();
							smallestz.prev.next.prev = smallestz.prev;
							smallestz.prev.next.next = smallestz;
							smallestz.prev = smallestz.prev.next;
							smallestz.prev.cumx = smallestz.prev.prev.cumx + cboxx;
							smallestz.prev.cumz = smallestz.cumz + cboxz;
						}
					}
					volume_check();
				}
				else if (smallestz.prev.cumz == smallestz.next.cumz)
				{
					//*** SITUATION-4: THERE ARE BOXES ON BOTH OF THE SIDES ***

					//*** SUBSITUATION-4A: SIDES ARE EQUAL TO EACH OTHER ***

					lenx = smallestz.cumx - smallestz.prev.cumx;
					lenz = smallestz.prev.cumz - smallestz.cumz;
					lpz = remainpz - smallestz.cumz;

					find_box(lenx, layerthickness, remainpy, lenz, lpz);
					checkfound();

					if (layerdone) break;
					if (evened) continue;

					boxlist[cboxi].coy = packedy;
					boxlist[cboxi].coz = smallestz.cumz;
					if (cboxx == smallestz.cumx - smallestz.prev.cumx)
					{
						boxlist[cboxi].cox = smallestz.prev.cumx;
						if (smallestz.cumz + cboxz == smallestz.next.cumz)
						{
							smallestz.prev.cumx = smallestz.next.cumx;
							if (smallestz.next.next != null)
							{
								smallestz.prev.next = smallestz.next.next;
								smallestz.next.next.prev = smallestz.prev;
								smallestz = null;
							}
							else
							{
								smallestz.prev.next = null; ;
								smallestz = null;
							}
						}
						else
						{
							smallestz.cumz = smallestz.cumz + cboxz;
						}
					}
					else if (smallestz.prev.cumx < pallet_x - smallestz.cumx)
					{
						if (smallestz.cumz + cboxz == smallestz.prev.cumz)
						{
							smallestz.cumx = smallestz.cumx - cboxx;
							boxlist[cboxi].cox = smallestz.cumx - cboxx;
						}
						else
						{
							boxlist[cboxi].cox = smallestz.prev.cumx;
							smallestz.prev.next = new Scrappad();
							smallestz.prev.next.prev = smallestz.prev;
							smallestz.prev.next.next = smallestz;
							smallestz.prev = smallestz.prev.next;
							smallestz.prev.cumx = smallestz.prev.prev.cumx + cboxx;
							smallestz.prev.cumz = smallestz.cumz + cboxz;
						}
					}
					else
					{
						if (smallestz.cumz + cboxz == smallestz.prev.cumz)
						{
							smallestz.prev.cumx = smallestz.prev.cumx + cboxx;
							boxlist[cboxi].cox = smallestz.prev.cumx;
						}
						else
						{
							boxlist[cboxi].cox = smallestz.cumx - cboxx;
							smallestz.next.prev = new Scrappad();
							smallestz.next.prev.next = smallestz.next;
							smallestz.next.prev.prev = smallestz;
							smallestz.next = smallestz.next.prev;
							smallestz.next.cumx = smallestz.cumx;
							smallestz.next.cumz = smallestz.cumz + cboxz;
							smallestz.cumx = smallestz.cumx - cboxx;
						}
					}
					volume_check();
				}
				else
				{
					//*** SUBSITUATION-4B: SIDES ARE NOT EQUAL TO EACH OTHER ***

					lenx = smallestz.cumx - smallestz.prev.cumx;
					lenz = smallestz.prev.cumz - smallestz.cumz;
					lpz = remainpz - smallestz.cumz;
					find_box(lenx, layerthickness, remainpy, lenz, lpz);
					checkfound();

					if (layerdone) break;
					if (evened) continue;

					boxlist[cboxi].coy = packedy;
					boxlist[cboxi].coz = smallestz.cumz;
					boxlist[cboxi].cox = smallestz.prev.cumx;
					if (cboxx == smallestz.cumx - smallestz.prev.cumx)
					{
						if (smallestz.cumz + cboxz == smallestz.prev.cumz)
						{
							smallestz.prev.cumx = smallestz.cumx;
							smallestz.prev.next = smallestz.next;
							smallestz.next.prev = smallestz.prev;
							smallestz = null;
						}
						else
						{
							smallestz.cumz = smallestz.cumz + cboxz;
						}
					}
					else
					{
						if (smallestz.cumz + cboxz == smallestz.prev.cumz)
						{
							smallestz.prev.cumx = smallestz.prev.cumx + cboxx;
						}
						else if (smallestz.cumz + cboxz == smallestz.next.cumz)
						{
							boxlist[cboxi].cox = smallestz.cumx - cboxx;
							smallestz.cumx = smallestz.cumx - cboxx;
						}
						else
						{
							smallestz.prev.next = new Scrappad();
							smallestz.prev.next.prev = smallestz.prev;
							smallestz.prev.next.next = smallestz;
							smallestz.prev = smallestz.prev.next;
							smallestz.prev.cumx = smallestz.prev.prev.cumx + cboxx;
							smallestz.prev.cumz = smallestz.cumz + cboxz;
						}
					}
					volume_check();
				}
			}
			return false;
		}

		private void find_smallest_z()
		{
			scrapmemb = scrapfirst;
			smallestz = scrapmemb;
			while (!(scrapmemb.next == null))
			{
				if (scrapmemb.next.cumz < smallestz.cumz)
				{
					smallestz = scrapmemb.next;
				}
				scrapmemb = scrapmemb.next;
			}
			return;
		}

		private void do_best_pack()
		{
			packItems = new List<int>();
			switch (best_variant)
			{
				case 1:
					pallet_x = xx; pallet_y = yy; pallet_z = zz;
					break;

				case 2:
					pallet_x = zz; pallet_y = yy; pallet_z = xx;
					break;

				case 3:
					pallet_x = zz; pallet_y = xx; pallet_z = yy;
					break;

				case 4:
					pallet_x = yy; pallet_y = xx; pallet_z = zz;
					break;

				case 5:
					pallet_x = xx; pallet_y = zz; pallet_z = yy;
					break;

				case 6:
					pallet_x = yy; pallet_y = zz; pallet_z = xx;
					break;
			}
			packingbest = true;
			packed_box_percentage = best_solution_volume * 100 / total_box_volume;
			pallet_volume_used_percentage = best_solution_volume * 100 / total_pallet_volume;

			list_candidate_layers();
			layers[0].layereval = -1;
			Array.Sort(layers, 0, layerlistlen + 1, new LayerComparer());
			packedvolume = 0.0;
			packedy = 0;
			packing = true;
			layerthickness = layers[best_iteration].layerdim;
			remainpy = pallet_y;
			remainpz = pallet_z;

			for (x = 1; x <= total_boxes; x++)
			{
				boxlist[x].is_packed = false;
			}

			do
			{
				layerinlayer = 0;
				layerdone = false;
				pack_layer();
				packedy = packedy + layerthickness;
				remainpy = pallet_y - packedy;
				if (layerinlayer != 0)
				{
					prepackedy = packedy;
					preremainpy = remainpy;
					remainpy = layerthickness - prelayer;
					packedy = packedy - layerthickness + prelayer;
					remainpz = lilz;
					layerthickness = layerinlayer;
					layerdone = false;
					pack_layer();
					packedy = prepackedy;
					remainpy = preremainpy;
					remainpz = pallet_z;
				}
				find_layer(remainpy);
			}
			while (packing);
		}
	}
}
