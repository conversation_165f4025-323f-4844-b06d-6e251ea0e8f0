using lep.freight;

//using lep.quote;
using lep.run;
using lep.user;
using System;
using System.Collections.Generic;

namespace lep.job.impl
{
	public class JobDTO
    {
        private string ArtSuppliedVia { get; set; }
        private IList<IArtwork> Artworks { get; set; }
        private ArtworkStatusOption ArtworkStatus { get; set; }
        private JobCelloglazeOptions BackCelloglaze { get; set; }
        private JobPrintOptions BackPrinting { get; set; }
        private IBindingOption BindingOption { get; set; }
        private bool BLround { get; set; }
        private bool BRround { get; set; }
        private RunCelloglazeOptions Celloglaze { get; }
        private IList<IComment> Comments { get; set; }
        private string CustomDieCut { get; set; }
        private int CustomSlot { get; set; }
        private DateTime DateCreated { get; set; }
        private DateTime DateModified { get; set; }
        private string DieCutting { get; set; }
        private CutOptions DieCutType { get; set; }
        private DateTime DispatchDate { get; set; }
        private string DispatchRequirements { get; set; }
        private bool Enable { get; set; }
        private Facility? Facility { get; set; }
        private IStaff FinishedBy { get; set; }
        private DateTime FinishedDate { get; set; }
        private ISize FinishedSize { get; set; }
        private ISize FoldedSize { get; set; }
        private string Folding { get; set; }
        private IFreight Freight { get; set; }
        private JobCelloglazeOptions FrontCelloglaze { get; set; }


		JobCelloglazeOptions? FrontCelloglazeOverride { get; set; }
		JobCelloglazeOptions? BackCelloglazeOverride { get; set; }

		private JobPrintOptions FrontPrinting { get; set; }
        private int? NumberOfHoles { get; set; }
        private HoleDrilling HoleDrilling { get; set; }
        private int Id { get; set; }
        private bool IsAutoPriceExpired { get; }
        private bool IsBrochure { get; }
        private bool IsBusinessCard { get; }
        private bool IsCMYK { get; }
        private bool IsCustomerGood { get; }
        private bool IsCustomFacility { get; set; }
        private bool IsDigital { get; }
        private bool IsFurtherProcessingRequired { get; }
        private bool IsFurtherProcessingRequiredForAnyJobInOrder { get; }
        private bool IsFurtherProcessingRequiredForOtherJobsInOrder { get; }
        private bool IsMagazine { get; }
        private bool IsPartOfMultiJobOrder { get; }
        private bool IsQuoteExpired { get; }
        private bool IsQuotePrice { get; set; }
        private bool IsReprint { get; set; }
        private bool IsReprintJobDispatch { get; set; }
        private bool IsTheLastJobInOrderBeingProcessed { get; }
        private string JobNr { get; }
        private bool JobsOrderIsInSingleRun { get; }
        private string LepSpecialInstructions { get; set; }
        private bool Magnet { get; set; }
        private bool MailedComplete { get; set; }
        private bool MailedCompletePayment { get; set; }
        private bool MailedGoneFinish { get; set; }
        private bool MailedGonePlate { get; set; }
        private bool MailedPrePayment { get; set; }
        private string MYOB { get; set; }
        private string Name { get; set; }
        private string NCRNo { get; set; }
        private bool NeedApproval { get; }
        private JobStatusOptions NextStatus { get; set; }
        private int NumPressSheets { get; set; }
        private PadDirection PadDirection { get; set; }
        private int Pages { get; set; }
        private bool Perforating { get; set; }
        private string PerforatingInstructions { get; set; }
        private IPrepress Prepress { get; set; }
        private IStaff PrepressCheckedBy { get; set; }
        private IList<IPressDetail> PressDetails { get; set; }
        private string Preview { get; set; }
        private string Price { get; set; }
        private string PriceBase { get; set; }
        private DateTime PriceDate { get; set; }
        private decimal PriceMargin { get; set; }
        private string PriceMarginValue { get; set; }
        private bool Printed { get; set; }
        private IStaff PrintedBy { get; set; }
        private DateTime PrintedDate { get; set; }
        private PrintType PrintType { get; set; }
        private string ProductionInstructions { get; set; }
        private string ProductPriceCode { get; set; }
        private int Quantity { get; set; }

        //IQuote Quote { get; set; }
        private bool QuoteNeedApprove { get; set; }

        private JobApprovalOptions ReadyArtworkApproval { get; set; }
        private DateTime ReceivedDate { get; set; }
        private int ReOrderSourceJobId { get; set; }
        private IUser ReprintBy { get; set; }
        private string ReprintCost { get; set; }
        private int ReprintFromPreviousJobNo { get; set; }
        private int ReprintFromPreviousRunNo { get; set; }
        private string ReprintReason { get; set; }
        private string ReprintReasonPredefined { get; set; }
        private string ReprintResult { get; set; }
        private string RequestedPackaging { get; set; }
        private DateTime RequiredByDate { get; set; }
        private IList<string> RequiredPosition { get; }
        private RotationOption Rotation { get; set; }
        private RoundDetailOption RoundDetailOption { get; set; }
        private RoundOption RoundOption { get; set; }
        private IList<IRun> Runs { get; set; }
        private int ScanCount { get; set; }
        private bool Scoring { get; set; }
        private string ScoringInstructions { get; set; }
        private bool SelfCovered { get; set; }
        private bool SendSamples { get; set; }
        private string SpecialInstructions { get; set; }
        private JobStatusOptions Status { get; set; }
        private DateTime StatusDate { get; set; }
        private IStock Stock { get; set; }
        private IStock StockForCover { get; set; }        
        private IStock StockOverride { get; set; }
        private IStock StockForCoverOverride { get; set; }
        private JobApprovalOptions SupplyArtworkApproval { get; set; }
        private IJobTemplate Template { get; set; }
        private bool TLround { get; set; }
        private bool TrackProgress { get; set; }
        private bool TRround { get; set; }
        private bool Urgent { get; set; }
    }
}
