namespace lep.courier.csv
{
	using System;
	using System.IO;
	using System.Collections.Generic;
	using System.Text;
	using System.Data;
	using System.Data.SqlClient;
	using System.Linq;

	using NHibernate;
	using lumen.csv;
	using lep.courier.impl;

	public class StarTrackPostcodeReader: lep.www.BulkCopyImport
	{

		private IList<string> postcodes;

		public StarTrackPostcodeReader()
			: base( "StarTrack_Postcode", typeof(StarTrackPostcode) )
		{
		}

		protected override DataTable CreateDataTable()
		{

			DataTable table = new DataTable();

			DataColumn postcodeCol = new DataColumn( "Postcode",typeof( string ) );
			postcodeCol.AllowDBNull = false;
			postcodeCol.MaxLength = 4;
			table.Columns.Add( postcodeCol );

			DataColumn directCol = new DataColumn( "DirectZone",typeof( string ) );
			directCol.MaxLength = 10;
			directCol.AllowDBNull = false;
			table.Columns.Add( directCol );

			DataColumn altCol = new DataColumn( "AltOnForwardZone",typeof( string ) );
			altCol.MaxLength = 10;
			altCol.AllowDBNull = false;
			table.Columns.Add( altCol );

			DataColumn forwardCol = new DataColumn( "OnForwardZone",typeof( string ) );
			forwardCol.MaxLength = 10;
			forwardCol.AllowDBNull = false;
			table.Columns.Add( forwardCol );

			table.PrimaryKey = new DataColumn[] { postcodeCol };
			return table;
		}

		public override void StartDocument()
		{
			base.StartDocument();

			postcodes = new List<string>();
		}

		protected override bool VerifyHeader( string[] values )
		{
			string[] headers = new string[] { "postcode","nearest depot","suburb name","state code","service city","direct zone","onforward zone","alternate service city","alternate onforward zone","alternate state code" };

			for (int i = 0; i < headers.Length; i++) {
				if (i >= values.Length || headers[i].ToLower() != values[i].ToLower()) {
					AddError( "Header row must be: postcode, nearest depot, suburb name, state code, service city, direct zone, onforward zone, alternate service city, alternate onforward zone, alternate state code" );
					return false;
				}
			}

			return true;
		}

		protected override bool ProcessRow( DataRow dr, string[] values )
		{
			if (values.Length < 10) {
				AddError( "Not enough columns" );
				return false;
			}


			if (!String.IsNullOrEmpty( values[0] )) {
				if (!postcodes.Contains( values[0] )) {
					dr[0] = values[0].ToString();
					postcodes.Add( values[0] );
				} else {
					return false;
				}
			} else {
				AddError( "Postcode required" );
				return false;
			}

			dr[1] = values[5].ToString();
			dr[2] = values[8].ToString();
			dr[3] = values[6].ToString();

			return true;
		}
	}
}
