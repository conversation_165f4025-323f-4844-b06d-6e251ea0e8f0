name: .NET


on:
  push:
    branches: [ "net6" ]
  pull_request:
    branches: [ "net6" ]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Setup .NET
      uses: actions/setup-dotnet@v3
      with:
        dotnet-version: 7.0.101
    - name: Restore dependencies
      working-directory: ./au
      run: dotnet restore
    - name: Build
      working-directory: ./au
      run: dotnet build --no-restore
