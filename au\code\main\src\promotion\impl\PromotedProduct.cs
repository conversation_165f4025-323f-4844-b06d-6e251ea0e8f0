using lep.job;
using System;

namespace lep.promotion.impl
{
	[Serializable]
    public class PromotedProduct : IPromotedProduct
    {
        #region Constructors

        public PromotedProduct(IPromotion promotion, IJobTemplate template, IPaperSize paperSize, IStock stock)
        {
            //Promotion = promotion;
            //JobTemplate = template;
            //PaperSize = paperSize;
            //Stock = stock;
        }

        public PromotedProduct()
        {
        }

        public PromotedProduct(IPromotedProduct pp)
        {
            //JobTemplate = pp.JobTemplate;
            //PaperSize = pp.PaperSize;
            //Stock = pp.Stock;
            JobOptionId = pp.JobOptionId;
            StockId = pp.StockId;
            PaperSizeId = pp.PaperSizeId;
        }

        #endregion Constructors

        #region Properties

        public virtual int Id { get; set; }

        //public virtual IJobTemplate JobTemplate { get; set; }
        //public virtual IPaperSize PaperSize { get; set; }
        //public virtual IPromotion Promotion { get; set; }
        //public virtual IStock Stock { get; set; }

        public virtual int JobOptionId { get; set; }
        public virtual int StockId { get; set; }
        public virtual int PaperSizeId { get; set; }
        public virtual int PromotionId { get; set; }

        #endregion Properties
    }
}