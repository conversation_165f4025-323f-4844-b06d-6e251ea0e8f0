using System.Collections.Specialized;
using System.IO;

namespace lep.macro.impl
{
	public class StringMacroFilter : MacroFilter
    {
        private StringDictionary macro = new StringDictionary();

        public StringMacroFilter(TextWriter writer) : base(writer)
        {
        }

        public string this[string name]
        {
            set { macro[name] = value; }
        }

        protected override string ExpandMacro(string name)
        {
            if (macro.ContainsKey(name))
            {
                return macro[name];
            }
            return string.Format("[[{0}]]", name);
        }
    }
}