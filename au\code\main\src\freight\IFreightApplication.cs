namespace lep.freight
{
    using lep.job;
    using lep.order;
    using System;

    public interface IFreightApplication
    {
        decimal GetCustomerFreightMarginFromCode(String freightCode);

        void LEPInternalFreightSelection(IOrder order);

        //OrderFreightLog GetFreightLog(int id);

        //void Save(OrderFreightLog freightLog);

        void SetFreight(IOrder order);

        void SetFreight(IJob job);

        //void SetFreightPrice(IOrder order);

        bool HasSkid(IOrder order, Facility facility);

        void CronTask_MoveToNewFreight();

        void MoveToNewFreight(int orderId);

        //CourierType GetCourier(CourierType orderCourier, IList<IPackage> packages, Facility facility, string destPostcode, IDictionary<CourierType, decimal> prices, StringBuilder courierLog);
    }
}
