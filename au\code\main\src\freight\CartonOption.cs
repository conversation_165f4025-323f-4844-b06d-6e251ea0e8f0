namespace lep.freight
{
    using lep.job;

    public class CartonOption
    {
        public virtual int Id { get; protected set; }
        public virtual ICarton Carton { get; protected set; }
        public virtual IPaperSize Finish { get; protected set; }
        public virtual IPaperSize Fold { get; protected set; }
        public virtual IStock Stock { get; protected set; }
        public virtual int Capacity { get; protected set; }
        public virtual bool Magnet { get; protected set; }
        public virtual IJobTemplate JobTemplate { get; protected set; }
    }
}