﻿using NHibernate;
using System;
using System.Collections.Generic;

namespace lep.onlineTxn
{
	public interface IOnlineTxnApplication
    {
        string MerchantID { get; set; }
        string MerchantAccessCode { get; set; }
        string MerchantTransactionRef { get; set; }

        IList<IOnlineTxn> GetAllPayments();

        IList<IOnlineTxn> GetSuccessfulPayments();

        IList<IOnlineTxn> GetFailedPayments();

        IList<IOnlineTxn> GetPaymentsByCriteria(bool? success, DateTime? fromDate, DateTime? toDate, string customerName,
            OrderStatusOptions? status);

        IList<IOnlineTxn> GetPaymentsFromCriteria(ICriteria criteria);

        ICriteria GetPaymentsCriteria(bool? success, DateTime? fromDate, DateTime? toDate, string customerName,
            OrderStatusOptions? status);

        void Save(IOnlineTxn tx);

        void DumpTodaysOnlineTransactions();
    }
}