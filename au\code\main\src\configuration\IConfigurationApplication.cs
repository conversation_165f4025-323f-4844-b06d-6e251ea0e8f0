using lep.job;
using lep.order;
using lep.run;
using System.Collections.Generic;
using System.IO;

namespace lep.configuration
{
	/// <summary>
	///
	/// </summary>
	public interface IConfigurationApplication
	{
		string GetValue(Configuration name);
		void SetValue(Configuration name, string value);

		// move this to better place
		//DirectoryInfo ArtworkDirectoryDigitalHotFolder();
		//Facility? GetProductionFacilityByPostCode(string postcode);
	}
}
