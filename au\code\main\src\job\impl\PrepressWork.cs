using System;

namespace lep.job.impl
{
	public class PrepressWork : IPrepressWork
    {
        private int sheets;

        public PrepressWork()
        {
            sheets = 0;
        }

        public PrepressWork(int sheets, bool a1, bool a2)
        {
            Sheets = sheets;
            A1 = a1;
            A2 = a2;
        }

        #region IJobPrepressWork Members

        public virtual int Sheets
        {
            get { return sheets; }
            set {
                if (value < 0)
                {
                    throw new ArgumentOutOfRangeException("sheets");
                }
                sheets = value;
            }
        }

        public virtual bool A1 { get; set; }

        public virtual bool A2 { get; set; }

        #endregion IJobPrepressWork Members

        public override bool Equals(object obj)
        {
            if (obj == null)
                return false;

            PrepressWork other = obj as PrepressWork;
            if (other == null) return false;
            if (A1 == other.A1 &&
                A2 == other.A2 &&
                Sheets == other.Sheets)
            {
                return true;
            }

            return false;
        }

		public override int GetHashCode()
		{
			return HashCode.Combine(Sheets, A1, A2);
		}
	}
}
