namespace lep.configuration
{
    public enum Configuration
    {
        None,
        OrderLimit,
        ArchiveOrdersPeriod,
        EmailReplyAddress,
        EmailFromAddress,
        AutoPrintQuotes,
        QuotesPrinterName,
        EmailErrorAddress,
        OnHoldChangeTime,
        OnHoldNotificationTime,
        JobOptionCacheScript,
        EmailAdminAddress,
        EmailJobRejectBCCAddress,
        EmailCustomerCreditLimitExceeded,

        //NZ setting
        NZEmailReplyAddress,

        NZEmailFromAddress,
        NZPriceMagnet,
        NZPriceRoundOption,
        NZPriceRoundDieCutOption,
        NZPriceHoleDrilling,
        NZPriceCrashFolding,
        NZPriceCrashFoldingBase,
        NZPriceMagazineCover,
        NZPriceBCSizeAdjustment,
        NZPriceDataFoldingSetup,
        NZPriceDataFoldingPer1000,
        NZPriceDataScoringSetup,
        NZPriceDataScoringPer1000,
        NZPriceDataPerforatingSetup,
        NZPriceDataPerforatingPer1000,

        PriceMagnet,
        PriceRoundOption,
        PriceRoundDieCutOption,
        PriceHoleDrilling,
        PriceHoleDrillingMagazine,

        PriceCrashFolding,
        PriceCrashFoldingBase,

        PriceMagazineCover,
        PriceBCSizeAdjustment,

        //cr27
        PriceDataFoldingSetup,

        PriceDataFoldingPer1000,
        PriceDataScoringSetup,
        PriceDataScoringPer1000,
        PriceDataPerforatingSetup,
        PriceDataPerforatingPer1000,

        //cr21

        EmailCallCentreAddress,

        //cr18
        NetworkPathPaymentReportsCsv,

        //cr14
        BackupLongTermStoragePeriod,

        ResourceSourcePath,
        ResourceBackupPath,
        BackupDailySpaceUsage,
        BackupDeletionWarningThreshold,
        BackupDeletionThreshold,

        //cr26
        FG_JobSheetPrinterName, // moving here from Config.dev

        PM_JobSheetPrinterName,
        FG_PigeonHolePrinterName,
        PM_PigeonHolePrinterName,
        FG_BCReferencePrinterName,
        PM_BCReferencePrinterName,
        FG_FurtherProcessingThumbnailPrinterName,
        PM_FurtherProcessingThumbnailPrinterName,
        FG_CartonLabelPrinterName,
        PM_CartonLabelPrinterName,
        FG_FreightPickListPrinterName,
        PM_FreightPickListPrinterName,
        FG_DPCProcessingPrinterName,
        PM_DPCProcessingPrinterName,

        FG_WideFormatProcessingPrinterName,
        PM_WideFormatProcessingPrinterName,

        LastScan,

        //SR
        GST,

        ExcludeFreeFreight,

        FreightExtraPrint,
        FreightOverpack,

        StarTrackSurCharge,
        StarTrackRating,
        StarTrackSmallWeight,
        StarTrackSmallPrice,
        StarTrackLargeWeight,
        StarTrackLargePrice,

        AustraliaPOSTSurCharge,
        AustraliaPOSTRating,

        FastwaySurCharge,
        FastwayRating,
        FastwayRedBase,
        FastwayRedExcess,
        FastwayPinkBase,
        FastwayPinkExcess,
        FastwayOrangeBase,
        FastwayOrangeExcess,
        FastwayGreenBase,
        FastwayGreenExcess,
        FastwayBrownBase,
        FastwayLimeBase,
        FastwayMaxBC,

        AustraliaPOSTSmallWeight,
        AustraliaPOSTSmallPrice,
        AustraliaPOSTLargeWeight,
        AustraliaPOSTLargePrice,

        PickUpCharge,
        PriceSamples,

        DigitalHPIndigoHotFolder,
        FPLSlotOrder,

        ActivePaymentGateway,

        WestpacUsername,
        WestpacPassword,
        WestpacBillercode,
        WestpacMerchantId,
        WestpacPaywayUrl,
        WestpacEncryptionKey,

        CourierImage_AustPOST,
        CourierURL_AustPOST,
        CourierImage_FastWay,
        CourierURL_FastWay,
        CourierImage_StarTrack,
        CourierURL_StarTrack,
        CourierImage_TNT,
        CourierURL_TNT,

        FG_WideFormatHotFolder,
        PM_WideFormatHotFolder,



		FG_DispatchCartonLabelPrinterName,
		PM_DispatchCartonLabelPrinterName,
		AutoRunAllocationEnabled,
	}
}
