﻿using lep.configuration;
using lep.email;
using lep.security;

using Serilog;
using lumen.csv;
using NHibernate;
using NHibernate.Criterion;
using NHibernate.SqlCommand;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Reflection;
using System.Text;

namespace lep.onlineTxn.impl
{
	public class OnlineTxnApplication : BaseApplication, IOnlineTxnApplication
	{
		public OnlineTxnApplication(ISession sf, ISecurityApplication _securityApp) : base(sf, _securityApp)
		{
		}


		#region Constants

		private const string STR_OnlinePaymentsCSVFileName = "OnlinePayments_{0:yyyyMMdd}.csv";

		#endregion Constants

		#region Readonly

		// private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		#endregion Readonly

		#region Private Methods

		private IList<IOnlineTxn> GetTodaysOnlineTransactions()
		{
			return Session.CreateCriteria(typeof(IOnlineTxn), "tx")
				.Add(Restrictions.Ge("PaymentDate", DateTime.Today))
				.List<IOnlineTxn>();
		}

		#endregion Private Methods

		#region Fields

		private IConfigurationApplication configurationApp;

		private IEmailApplication emailApp;

		private string merchantAccessCode;

		private string merchantID;

		private string merchantTransactionRef;

		#endregion Fields

		#region Properties

		public IConfigurationApplication ConfigurationApplication
		{
			get { return configurationApp; }
			set {
				Debug.Assert(value != null, "ConfigurationApplication is null.");
				configurationApp = value;
			}
		}

		public IEmailApplication EmailApplication
		{
			get { return emailApp; }
			set {
				Debug.Assert(value != null, "EmailApplication is null.");
				emailApp = value;
			}
		}

		public string MerchantAccessCode
		{
			get { return merchantAccessCode; }
			set {
				Debug.Assert(!String.IsNullOrEmpty(value), "MerchantAccessCode is null or empty.");
				merchantAccessCode = value;
			}
		}

		public string MerchantID
		{
			get { return merchantID; }
			set {
				Debug.Assert(!String.IsNullOrEmpty(value), "MerchantID is null or empty.");
				merchantID = value;
			}
		}

		public string MerchantTransactionRef
		{
			get { return merchantTransactionRef; }
			set {
				Debug.Assert(!String.IsNullOrEmpty(value), "MerchantTransactionRef is null or empty.");
				merchantTransactionRef = value;
			}
		}

		#endregion Properties

		#region Public Methods

		public void DumpTodaysOnlineTransactions()
		{
			var path = configurationApp.GetValue(Configuration.NetworkPathPaymentReportsCsv);

			var fileName = String.Format(STR_OnlinePaymentsCSVFileName, DateTime.Today);

			RunWithTransaction(Session, () =>
			{
				var fs = new FileStream(path + fileName, FileMode.Create);
				TextWriter writer = new StreamWriter(fs, new UTF8Encoding());
				var serial = new CsvSerialiser();
				serial.Writer = writer;
				serial.AlwaysQuote = true;
				serial.RowData(new string[]
				{
					"PaymentDate", "OrderNo", "Customer", "MYOB-Cust#", "Price", "ReceiptNo", "TransactionNo",
					"TxnReponseCode", "VpcMessage"
				});
				foreach (var tx in GetTodaysOnlineTransactions())
					serial.RowData(new string[]
					{
						tx.PaymentDate.ToString(), tx.Order.OrderNr, tx.Order.Customer.Name, tx.Order.Customer.MYOB,
						tx.Order.Price.ToString(), tx.VpcReceiptNo, tx.VpcTransactionNo, tx.VpcTxnResponseCode,
						tx.VpcMessage
					});
				writer.Flush();
				fs.Close();
			});
			Log.Information("Online Payment Transactions CRON Job updated the file : " + path + fileName);
		}

		public IList<IOnlineTxn> GetAllPayments()
		{
			return Session.CreateCriteria(typeof(IOnlineTxn)).List<IOnlineTxn>();
		}

		public IList<IOnlineTxn> GetFailedPayments()
		{
			return Session.CreateCriteria(typeof(IOnlineTxn), "tx")
				.Add(Restrictions.Not(Restrictions.Eq("tx.VpcTxnResponseCode", "0")))
				.List<IOnlineTxn>();
		}

		public IList<IOnlineTxn> GetPaymentsByCriteria(bool? success, DateTime? fromDate, DateTime? toDate,
			string customerName, OrderStatusOptions? status)
		{
			return GetPaymentsCriteria(success, fromDate, toDate, customerName, status).List<IOnlineTxn>();
		}

		public ICriteria GetPaymentsCriteria(bool? success, DateTime? fromDate, DateTime? toDate, string customerName,
			OrderStatusOptions? status)
		{
			var criteria = Session.CreateCriteria(typeof(IOnlineTxn), "tx");

			if (success != null)
			{
				if (success == true)
				{
					criteria.Add(Restrictions.Eq("tx.VpcTxnResponseCode", "0"));
				}
				else
				{
					criteria.Add(Restrictions.Not(Restrictions.Eq("tx.VpcTxnResponseCode", "0")));
				}
			}

			if (fromDate != null)
			{
				criteria.Add(Restrictions.Ge("tx.PaymentDate", fromDate));
			}

			if (toDate != null)
			{
				var fixedDateWithHours = (DateTime)toDate + new TimeSpan(23, 59, 59);
				criteria.Add(Restrictions.Le("tx.PaymentDate", fixedDateWithHours));
			}

			criteria.CreateAlias("tx.Order", "o", JoinType.InnerJoin);
			criteria.CreateAlias("o.Customer", "c", JoinType.InnerJoin);

			if (!String.IsNullOrEmpty(customerName))
			{
				criteria.Add(Restrictions.Like("c.Name", customerName, MatchMode.Anywhere));
			}

			if (status != null)
			{
				criteria.Add(Restrictions.Eq("o.Status", status));
			}

			return criteria;
		}

		public IList<IOnlineTxn> GetPaymentsFromCriteria(ICriteria criteria)
		{
			return criteria.List<IOnlineTxn>();
		}

		public IList<IOnlineTxn> GetSuccessfulPayments()
		{
			return Session.CreateCriteria(typeof(IOnlineTxn), "tx")
				.Add(Restrictions.Eq("VpcTxnResponseCode", "0"))
				.List<IOnlineTxn>();
		}

		public void Save(IOnlineTxn tx)
		{
			base.Save<IOnlineTxn>(tx);
			emailApp.SendOnlinePaymentNotification(tx);
		}

		#endregion Public Methods
	}
}
