namespace lep.job
{
	public enum RoundDetailOption
    {
        None,
        [Description("6mm x 1 corner")] Standard6mm1,
        [Description("6mm x 2 corners")] Standard6mm2,
        [Description("6mm x 3 corners")] Standard6mm3,
        [Description("6mm x 4 corners")] Standard6mm4,
        [Description("10mm x 1 corners")] Standard10mm1,
        [Description("10mm x 2 corners")] Standard10mm2,
        [Description("10mm x 3 corners")] Standard10mm3,
        [Description("10mm x 4 corners")] Standard10mm4,
        [Description("3mm x 4 corners")] DieCut3mm4,
        [Description("3mm x Top L Bottom R")] DieCut3mmTopLBottomR,
        [Description("6mm x 4 corners")] DieCut6mm4,
        [Description("6mm x Top L Bottom R")] DieCut6mmTopLBottomR,
        [Description("24mm Bottom R")] DieCut24mmBottomR,

        [Description("3.5mm x 1 corner")] Standard35mm1,
        [Description("3.5mm x 2 corners")] Standard35mm2,
        [Description("3.5mm x 3 corners")] Standard35mm3,
        [Description("3.5mm x 4 corners")] Standard35mm4
    }
}