using Serilog;
using lumen.csv;
using System;
using System.Collections.Generic;
using System.Reflection;

namespace lep.job.csv
{
	public class FoldOptionsImportHandler : BaseHandler
	{
		// private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);
		private IJobApplication jobApp;

		private int rowNumber = 0;

		private bool seenFirstRow;

		public IDictionary<int, IList<IPaperSize>> Result { get; } = new Dictionary<int, IList<IPaperSize>>();

		public IJobApplication JobApplication
		{
			set { jobApp = value; }
		}

		public override void StartDocument()
		{
			seenFirstRow = false;
		}

		public override void EndDocument()
		{
		}

		public override void RowData(string[] values)
		{
			if (!seenFirstRow)
			{
				seenFirstRow = true;
			}
			else
			{
				rowNumber++;

				try
				{
					IList<IPaperSize> pageSize = new List<IPaperSize>();
					var fid = Int32.Parse(values[0]);
					for (var i = 1; i < values.Length; i++)
					{
						if ("" != values[i] && "-" != values[i])
						{
							pageSize.Add(jobApp.GetPaperSize(values[i]));
						}
					}
					Result.Add(fid, pageSize);
				}
				catch (Exception ex)
				{
					//Wrap the exception in another that gives the line number
					Log.Error(string.Format("Invalid value in row {0} : {1}", rowNumber, ex.Message), ex);
				}
			}

			base.RowData(values);
		}
	}
}
