using System;
using System.Collections.Generic;

namespace lep.promotion.impl
{
	[Serializable]
    public class Promotion : IPromotion
    {
        #region Fields

        private IList<IPromotedProduct> promotedProducts = new List<IPromotedProduct>();

        #endregion Fields

        #region Constructors

        public Promotion()
        {
            Id = 0;
            Active = true;
            PromotionCode = String.Empty;
            ShortDescription = String.Empty;
            LongDescription = String.Empty;
            SalesDescription = String.Empty;

            MaxDeduction = 1000000;
            Discount = 0;
            MaxDiscount = 1000000;
            MaxJobPrice = 0;
            MinJobPrice = 1000000;
            MinJobQuantity = 0;
            OnlyFirstOrder = false;
            CanUseOnce = false;
            FreeDelivery = false;
            FreeBusinessCard = false;
            DateValidStart = DateTime.Now;
            DateValidEnd = DateTime.Now.AddDays(30);
            OnlyValidInCampaign = false;
            DateCreated = DateTime.MinValue;
            DateModified = DateTime.MinValue;
            DateCreated = DateTime.Now;
        }

        public Promotion(IPromotion promotion)
        {
            PromotionCode = "Copy " + promotion.PromotionCode;
            Active = true;
            ShortDescription = promotion.ShortDescription;
            LongDescription = promotion.LongDescription;
            SalesDescription = promotion.SalesDescription;

            MaxDeduction = promotion.MaxDeduction;
            MaxDiscount = promotion.MaxDiscount;
            Discount = promotion.Discount;
            MaxJobPrice = promotion.MaxJobPrice;
            MaxJobPrice = promotion.MaxJobPrice;
            MinJobPrice = promotion.MinJobPrice;
            MaxOrderPrice = promotion.MaxOrderPrice;
            MinOrderPrice = promotion.MinOrderPrice;
            MinJobQuantity = promotion.MinJobQuantity;

            LifeSpan = promotion.LifeSpan;
            DateValidStart = promotion.DateValidStart;
            DateValidEnd = promotion.DateValidEnd;
            Window = promotion.Window;

            CanUseOnce = promotion.CanUseOnce;
            FreeBusinessCard = promotion.FreeBusinessCard;
            FreeDelivery = promotion.FreeDelivery;
            OnlyFirstOrder = promotion.OnlyFirstOrder;
            OnlyValidInCampaign = promotion.OnlyValidInCampaign;
            CheckCustomerAgainstCampaign = promotion.CheckCustomerAgainstCampaign;

            SalesCategoryLead = promotion.SalesCategoryLead;
            SalesCategoryLapsed = promotion.SalesCategoryLapsed;
            SalesCategoryProspect = promotion.SalesCategoryProspect;
            SalesCategoryCustomer = promotion.SalesCategoryCustomer;

            DateCreated = DateTime.Now;
        }

        #endregion Constructors

        #region Properties

        public virtual int Id { get; set; }

        public virtual bool Active { get; set; }

        public virtual bool CanUseOnce { get; set; }

        public virtual DateTime DateCreated { get; set; }

        public virtual DateTime DateModified { get; set; }

        public virtual DateTime? DateValidEnd { get; set; }
        public virtual DateTime? DateValidStart { get; set; }

        public virtual decimal MaxDeduction { get; set; }

        public virtual int Discount { get; set; }

        public virtual decimal MaxDiscount { get; set; }

        public virtual bool FreeBusinessCard { get; set; }

        public virtual bool FreeDelivery { get; set; }

        public virtual PromotionLifeSpan LifeSpan { get; set; }

        public virtual string LongDescription { get; set; }

        public virtual decimal MaxJobPrice { get; set; }

        public virtual decimal MaxOrderPrice { get; set; }

        public virtual decimal MinJobPrice { get; set; }

        public virtual int MinJobQuantity { get; set; }

        public virtual decimal MinOrderPrice { get; set; }

        public virtual bool OnlyFirstOrder { get; set; }

        public virtual bool OnlyValidInCampaign { get; set; }

        public virtual bool CheckCustomerAgainstCampaign { get; set; }

        public virtual IList<IPromotedProduct> PromotedProducts
        {
            get { return promotedProducts; }
            set { promotedProducts = value; }
        }

        public virtual string PromotionCode { get; set; }

        public virtual string SalesCategories
        {
            get {
                var value = "";
                value += SalesCategoryLead ? "Lead " : "";
                value += SalesCategoryProspect ? "Prospect " : "";
                value += SalesCategoryLapsed ? "Lapsed " : "";
                value += SalesCategoryCustomer ? "Customer " : "";
                return value;
            }
        }

        public virtual bool SalesCategoryCustomer { get; set; }

        public virtual bool SalesCategoryLapsed { get; set; }

        public virtual bool SalesCategoryLead { get; set; }

        public virtual bool SalesCategoryProspect { get; set; }

        public virtual string SalesDescription { get; set; }

        public virtual string ShortDescription { get; set; }

        public virtual string Vaild
        {
            get {
                var value = "?";

                if (LifeSpan == PromotionLifeSpan.Windowed)
                {
                    value = Window + " days";
                }
                else
                {
                    if (DateValidStart.HasValue && DateValidEnd.HasValue)
                    {
                        var t = (int)(DateValidEnd.Value - DateValidStart.Value).TotalDays;

                        value = string.Format("{0} to {1} ({2} days)",
                            DateValidStart.Value.ToString("dd-MMM-yy"),
                            DateValidEnd.Value.ToString("dd-MMM-yy"),
                            t);
                    }
                }
                return value;
            }
        }

        public virtual int Window { get; set; }

        #endregion Properties
    }
}