﻿using System;
using System.Collections.Generic;

namespace lep.promotion
{
	public interface IPromotion
    {
        int Id { get; set; }

        // Used to Identify Promotion
        string PromotionCode { get; set; }

        // Promotions LifeSpan  can be daterange or windowed
        PromotionLifeSpan LifeSpan { get; set; }

        DateTime? DateValidEnd { get; set; }
        DateTime? DateValidStart { get; set; }

        int Window { get; set; }

        // Sales categories
        bool SalesCategoryLead { get; set; }

        bool SalesCategoryProspect { get; set; }

        bool SalesCategoryLapsed { get; set; }

        bool SalesCategoryCustomer { get; set; }

        // Other descriptions
        string SalesDescription { get; set; }

        string ShortDescription { get; set; }

        string LongDescription { get; set; }

        //exclude freight
        decimal MaxDeduction { get; set; }

        // Values associated with price rules
        decimal MaxJobPrice { get; set; }

        decimal MinJobPrice { get; set; }

        int MinJobQuantity { get; set; }

        decimal MaxOrderPrice { get; set; }

        decimal MinOrderPrice { get; set; }

        int Discount { get; set; }

        decimal MaxDiscount { get; set; }

        // Various flags of the promotion
        bool Active { get; set; }

        bool CanUseOnce { get; set; }

        bool OnlyFirstOrder { get; set; }

        bool OnlyValidInCampaign { get; set; }

        bool CheckCustomerAgainstCampaign { get; set; }

        bool FreeBusinessCard { get; set; }

        bool FreeDelivery { get; set; }

        string Vaild { get; }

        IList<IPromotedProduct> PromotedProducts { get; set; }

        string SalesCategories { get; }

        DateTime DateCreated { get; set; }

        DateTime DateModified { get; set; }

        int GetHashCode();

        bool Equals(object obj);
    }
}