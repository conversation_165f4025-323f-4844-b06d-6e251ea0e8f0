using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;

namespace lep.freight.impl
{
	public class FoldFactor
	{
		public string GSMStr { get; set; }
		public string PaperSize { get; set; }
		public string FoldType { get; set; }
		public decimal? FoldFactorValue { get; set; }
	}

	public class FoldFactorFinder
	{
		//private static Lazy<FoldFactorFinder> _instance = new Lazy<FoldFactorFinder>(() => new FoldFactorFinder());
		//public static FoldFactorFinder Instance => _instance.Value;
		private readonly IEnumerable<FoldFactor> _data;

		public FoldFactorFinder(string jobOptionCSVsFolder)
		{
			var pathOfCsv = Path.Combine(jobOptionCSVsFolder, "FoldFactor.csv");
			var datajson = CsvToJson.Convert(pathOfCsv);
			_data = JsonConvert.DeserializeObject<List<FoldFactor>>(datajson).AsEnumerable();
		}

		public decimal Get(int gsm, string paperSize, string foldSize)
		{
			var gsms = "";
			if (gsm > 0 && gsm <= 150)
				gsms = "upto 150";
			else if (gsm > 150 && gsm <= 250)
				gsms = "upto 250";
			else 
				gsms = "250 +";

			var r = (from f in _data
					 where  f.PaperSize == paperSize && f.FoldType == foldSize && f.GSMStr == gsms
					 select f.FoldFactorValue).FirstOrDefault() ?? 1.000m;

	 

			return r;
		}
	}
}
