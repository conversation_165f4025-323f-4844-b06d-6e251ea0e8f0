<hibernate-mapping
xmlns="urn:nhibernate-mapping-2.2"
assembly="lep"
namespace="lep.onlineIVRTxn"  auto-import="true" default-cascade="all">
    <class name="IOnlineIVRTxn" table="`OnlinePaymentsIVR`"  discriminator-value="null">
        <cache usage="read-write" />
        <id name="Id" column="Id" type="Int32" unsaved-value="0">
            <generator class="identity" />
        </id>
        <discriminator column="Id" type="Int32" insert="false" />
        <timestamp name="DateModified" column="DateModified" />
        <many-to-one name="Order" column="orderid" class="lep.order.IOrder, lep" not-null="true" />

        <property column="PaymentReceiptNumber"     type="String" name="PaymentReceiptNumber"    length="255" />
        <property name="DateCreated" column="DateCreated" type="lumen.hibernate.type.DateTimeType, lumen" not-null="false" update="false" insert="false" />

        <subclass name="lep.onlineIVRTxn.impl.OnlineIVRTxn, lep" proxy="IOnlineIVRTxn" discriminator-value="not null" />
    </class>
</hibernate-mapping>