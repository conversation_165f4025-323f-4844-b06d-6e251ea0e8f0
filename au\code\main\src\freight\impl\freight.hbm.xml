<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2"
				   namespace="lep.freight"
				   assembly="lep"
				   auto-import="true"
				   default-cascade="all">

	<!--<class name="CartonOption" table="CartonOption" mutable="false">
		<cache usage="read-only" />
		<id name="Id" type="Int32" unsaved-value="0">
			<generator class="identity" />
		</id>
		<many-to-one name="Stock" class="lep.job.IStock, lep" column="StockId" not-null="true" cascade="none" />
		<many-to-one name="Finish" class="lep.job.IPaperSize, lep" column="FinishId" not-null="true" cascade="none" />
		<many-to-one name="Fold" class="lep.job.IPaperSize, lep" column="FoldId" not-null="true" cascade="none" />
		<many-to-one name="Carton" class="lep.freight.ICarton, lep" column="CartonCode" not-null="true" cascade="none" />
		<many-to-one name="JobTemplate" class="lep.job.IJobTemplate" column="JobTemplateId" not-null="true" cascade="none" />F
		<property name="Magnet" type="YesNo" not-null="true" />
		<property name="Capacity" not-null="true" type="Int32" />
	</class>-->

	<!--<class name="ICarton" table="Carton" discriminator-value="null">
		<cache usage="read-write" />
		<id name="Code" type="string" column="CartonType">
			<generator class="assigned" />
		</id>
		--><!--<discriminator column="CartonType" type="lep.GenericEnum`1[lep.freight.CartonType], lep" insert="false" />--><!--
		<many-to-one name="Level2Carton" class="lep.freight.ICarton, lep" column="Level2CartonCode" cascade="none" />
		<property name="Weight" type="Decimal" not-null="true" />
		<property name="AllowWeight" type="Decimal" not-null="true" />
		<property name="Width" type="Int32" not-null="true" />
		<property name="Height" type="Int32" not-null="true" />
		<property name="Depth" type="Int32" not-null="true" />
		<property name="PackagingLevel" type="Int32" not-null="true" />
		<property name="Wrap" type="YesNo" not-null="true" />
		<subclass name="lep.freight.impl.Carton, lep" proxy="ICarton" discriminator-value="not null" />
	</class>-->

	<class name="IDispatchLabel" table="Order_DispatchLabel" discriminator-value="null">
		<cache usage="read-write" />
		<id name="Id" type="Int32" unsaved-value="0">
			<generator class="identity" />
		</id>
		<discriminator column="Id" type="Int32" insert="false" />
		<property name="CartonType" />

		<!--<property name="CourierType" type="lep.courier.CourierType, lep" not-null="true" column="Courier" />-->
		<component name="CourierType"  class="lep.courier.CourierType, lep">
			<property name="CourierName" column="Courier"  length="50" not-null="false" />
			<property name="ServiceName" column="CourierService"  length="50" not-null="false" />
		</component>

		<property name="Weight" type="Decimal" not-null="true" />
		<property name="Facility" column="Facility" type="lep.GenericEnum`1[lep.job.Facility], lep" not-null="true" />
		<subclass name="lep.freight.impl.DispatchLabel, lep" proxy="IDispatchLabel" discriminator-value="not null" />
	</class>
</hibernate-mapping>