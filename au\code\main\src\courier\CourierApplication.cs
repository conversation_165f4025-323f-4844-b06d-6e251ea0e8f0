using lep.CompData;
using lep.configuration;
using lep.job;
using lep.order;
using lep.security;

using Serilog;
using Newtonsoft.Json;
using NHibernate;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;

namespace lep.courier.impl
{
	// Wrapper around Comp data webservice
	public sealed class CourierApplicationSF : BaseApplication, ICourierApplication
	{
		public const string LEP = "LEP";
		//// private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);
		//// private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		private Dictionary<Facility, Party> faciltyToSender = new Dictionary<Facility, Party>() {
		   { Facility.FG, new Party() { Code = "LEP", Country = "Australia", Suburb = "FOREST GLEN", State = "QLD", PostCode = "4556" } },
		   { Facility.PM, new Party() { Code = "LEPVIC", Country = "Australia", Suburb = "PORT MELBOURNE", State = "VIC", PostCode = "3207"}}
		  };

		public CourierApplicationSF(ISession sf, ISecurityApplication _securityApp, IJobApplication jobApp, IiFreightChargeEnquiryService ifreightCharge) : base(sf, _securityApp)
		{
			JobApplication = jobApp;
			_ifreightCharge = ifreightCharge;
		}

		private IiFreightChargeEnquiryService _ifreightCharge;

		public FreightChargeEnquiryResponseType TryGetRatesFromMultipleICompDataEndpoints(FreightChargeEnquiryRequestType fcr)
		{
			var CompDataEndPoints = LepGlobal.Instance.CompDataWebservieUrl;
			List<string> compdataEndPoints = CompDataEndPoints.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries).ToList();

			FreightChargeEnquiryResponseType finalResult;

			if (compdataEndPoints.Count() > 1)
			{
				Log.Debug(string.Format("*** making calls to {0}  diffrent end points to compdata ifreight", compdataEndPoints.Count()));

				Task<FreightChargeEnquiryResponseType>[] tasks = compdataEndPoints.Select(url =>
				Task<FreightChargeEnquiryResponseType>.Factory.StartNew(state =>
				{
					var st1 = new Stopwatch();
					st1.Start();
					try
					{
						//Log.Debug(string.Format("calling {0} ", url));
						FreightChargeEnquiryResponseType result = _ifreightCharge.GetCarrierFreightCharge(fcr);
						return result;
					}
					catch (Exception ex)
					{
						//Log.Error(string.Format("call failed in {0} seconds", st1.Elapsed.TotalSeconds));
						//Log.Error(ex.Message);
						// Task.Delay(TimeSpan.FromSeconds(15));
						return null;
					}
				}, url)
				).ToArray();

				int index = Task.WaitAny(tasks);

				finalResult = tasks[index].Result;

				if (finalResult != null)
				{
					Log.Information(string.Format("*** call {0}  returned {1} rates", index, tasks[index].Result.Rates.Count()));
					return finalResult;
				}
				else
				{
					Log.Error("*** Failed, no rates returned ");
					return null;
				}
			}
			else
			{
				//var binding = new BasicHttpBinding(BasicHttpSecurityMode.None);
				//var epa = new EndpointAddress(compdataEndPoints[0]);
				//var ifreight = ChannelFactory<IiFreightChargeEnquiryService>.CreateChannel(binding, epa);

				//binding.OpenTimeout = TimeSpan.FromSeconds(3);
				//// binding.CloseTimeout = TimeSpan.FromSeconds(3);
				//binding.SendTimeout = TimeSpan.FromSeconds(10);
				//// binding.ReceiveTimeout = TimeSpan.FromSeconds(10);

				//binding.MaxBufferPoolSize = long.MaxValue;
				//binding.MaxBufferSize = int.MaxValue;
				//binding.MaxReceivedMessageSize = long.MaxValue;
				FreightChargeEnquiryResponseType result = _ifreightCharge.GetCarrierFreightCharge(fcr);
				return result;
			}
		}

		public IList<Rate> GetAvailableRates(IOrder order, Facility? facility, bool includePickup = true)
		{
			//return new List<Rate>();
			/*todo:freight */
			Log.Information(string.Format("Order # {0}, Facility: {1}", order.Id, facility));
			var facilityFreightCode = "LEP";
			// create a sender based on the faciliy provided
			Party sender = null;

			if (facility != null)
			{
				if (facility == Facility.FG)
				{
					sender = faciltyToSender[Facility.FG];
					facilityFreightCode = "LEP";
				}
				else if ((facility == Facility.PM))
				{
					sender = faciltyToSender[Facility.PM];
					facilityFreightCode = "LEPVIC";
				}
			}
			else
			{
			}
			if (order.Status >= OrderStatusOptions.Submitted && order.FacilityAtSubmit.HasValue)
			{
				if (order.FacilityAtSubmit.Value == Facility.FG)
				{
					sender = faciltyToSender[Facility.FG];
					facilityFreightCode = "LEP";
				}
				else if ((order.FacilityAtSubmit.Value == Facility.PM))
				{
					sender = faciltyToSender[Facility.PM];
					facilityFreightCode = "LEPVIC";
				}
			}

			// if (facility != null)
			//     packages = order.PackDetail.Packages.Where(x => x.Facility == facility).ToList();
			// else

			List<Rate> ratesResult = new List<Rate>();

			//var ifreight = new lep.CompData.IiFreightChargeEnquiryServiceClient();

			System.Net.ServicePointManager.Expect100Continue = false;

			#region construct request

			var fcr = new FreightChargeEnquiryRequestType()
			{
				DateTimeStamp = DateTime.Now.ToString("o"),
				DocumentType = "FreightChargeEnquiry",
				DocumentID = order.Id.ToString(),
				Sender = facilityFreightCode,
				Receiver = ReceiverType.SMSQL,
				Authentication = new AuthenticationType()
				{
					APIUser = "LEP",
					APIKey = "Password",
				},
				FreightChargeEnquiry = new FreightChargeEnquiryType()
				{
					ConsignmentDate = DateTime.Now,
					PayParty = new Party()
					{
						Code = facilityFreightCode
					},
					Sender = sender,
					Receiver = new Party()
					{
						Suburb = order.DeliveryAddress.City,
						State = order.DeliveryAddress.State,
						PostCode = order.DeliveryAddress.Postcode,
						Country = order.DeliveryAddress.Country,

						Address1 = order.DeliveryAddress.Address1,
						Address2 = order.DeliveryAddress.Address2,
						Address3 = order.DeliveryAddress.Address3,

						// Code
						//ExtensionData
						//Name
					},
					CheapestChargeOption = CarrierCheapestChargeType.None,
					CustomerChargeCalculation = CustomerChargeCalculationType.Sender,
					ConnoteCategoryType = ConnoteCategoryType.Others,
					GoodsDetailLines = new List<GoodsDetailLine>(),
				}
			};

			var packages = order.PackDetail.GetPackages(null);
			if (packages == null)
				return null;

			//http://www.abctransport.com.au/cubic-calculator
			foreach (var p in packages)
			{
				var cubicM3 = (double)((p.Width / 1000d) * (p.Height / 1000d) * (p.Depth / 1000d));
				var cubicWeight = cubicM3 * 250;

				var deadWeight = (double)(p.Weight);
				var weight = Math.Max(deadWeight, cubicWeight);

				var l = new GoodsDetailLine()
				{
					ItemQuantity = 1,
					Weight = weight,
					Cubic = cubicM3,
					XCM = p.Width / 10d,
					YCM = p.Height / 10d,
					ZCM = p.Depth / 10d,
					CommCode = "",
				};
				fcr.FreightChargeEnquiry.GoodsDetailLines.Add(l);
			}

			var xxxxx = fcr.FreightChargeEnquiry.GoodsDetailLines;

			#endregion construct request

			Log.Debug(string.Format("REQUEST: {0}", JsonConvert.SerializeObject(fcr, Formatting.None)));
			try
			{
				var watch = Stopwatch.StartNew();
				//var result = ifreight.GetCarrierFreightCharge(fcr);

				FreightChargeEnquiryResponseType result = null;
				if (!string.IsNullOrEmpty(order.DeliveryAddress.Postcode))
				// or other reason for which  to bypass calling
				{
					result = TryGetRatesFromMultipleICompDataEndpoints(fcr);
				}

				if (result == null)
				{
					Log.Error("GOT NULL RESPONSE ");
					result = new FreightChargeEnquiryResponseType();
					result.Rates = new List<Rate>();
				}

				watch.Stop();
				Log.Information(string.Format("Time elapsed: {0}", watch.Elapsed));
				Log.Information(string.Format("RESPONSE: {0}", JsonConvert.SerializeObject(result, Formatting.None)));

				//if marginRate is negative, apply freight discount to least expensive - then subtract to all other rates returned.
				//if marginRate is positive then add margin to all rates returned.
				//if marginRate is 0 don't change rates at all, but still change the description for FastWays hack.
				if (result.Rates.Count > 0)
				{
					bool has0CustCharge = result.Rates.Any(r => r.CustomerCharge == 0);
					if (has0CustCharge)
					{
						Log.Error("\n\n\n\n0 rate from CompData");
						// Log.Error(JsonConvert.SerializeObject(fcr, Formatting.Indented));
						//  Log.Error(JsonConvert.SerializeObject(result, Formatting.Indented));
						foreach (var r in result.Rates)
						{
							if ((int)r.CustomerCharge == 0)
								r.CustomerCharge = r.Basic;
						}
					}

					// change the description for FastWays hack.
					result.Rates.ForEach(rate =>
					{
						if (rate.CarrierCode == "FSW" || rate.CarrierCode == "FSV")
						{
							if (rate.ServiceName == "LIME" || rate.ServiceName == "BLACK" || rate.ServiceName == "BLUE" || rate.ServiceName == "BROWN")
							{
								rate.ServiceName = rate.ServiceName + " next day";
							}
							else
							{
								rate.ServiceName = rate.ServiceName + " 1-3 days";
							}
						}
					});

					//if marginRate is negative, apply freight discount to least expensive - then subtract to all other rates returned.
					//if marginRate is positive then add margin to all rates returned.
					//if marginRate is 0 don't change rates at all
					if (order.Customer != null)
					{
						double marginRate = 0.0;
						try
						{
							marginRate = (double)GetCustomerFreightMarginFromCode(order.Customer.FreightPriceCode);
						}
						catch (Exception ex)
						{
							//Log.Error(ex.Message, ex);
						}
						var leastExpensiveRate1 = result.Rates.Min(x => x.CustomerCharge);
						var adjustmentPrice = (leastExpensiveRate1 * Math.Abs(marginRate)) / 100;
						adjustmentPrice = adjustmentPrice * (marginRate < 0 ? -1 : 1);
						result.Rates.ForEach(rate =>
						{
							rate.CustomerCharge = (double)(rate.CustomerCharge + adjustmentPrice);
						});
					}
				}

				ratesResult.AddRange(result.Rates);
			}
			catch (Exception ex)
			{
				//Log.Error(ex.StackTrace);
				var m = ex.InnerException;
				//throw ex;
			}

			ratesResult = ratesResult.OrderBy(x => (x.CustomerCharge)).ToList();

			bool includePickupFG = order.Jobs.All(j => JobApplication.IsFacilityAvailable(Facility.FG, j));
			bool includePickupPM = order.Jobs.All(j => JobApplication.IsFacilityAvailable(Facility.PM, j));
			// !order.Jobs.Any(job => job.IsDigital()); //make sure there are no digital or Business cards if Port Melbourne - as can't pickup!

			#region add pickup rate TODO get rid of pickup

			//if not same postcode
			if (includePickup)
			{
				var pickupFGCt = new CourierType(CourierType.CustomerPickupFG);
				var pickupPMCt = new CourierType(CourierType.CustomerPickupPM);

				if (includePickupFG)
				{
					Rate pickup = new Rate()
					{
						CarrierName = pickupFGCt.CourierName,
						ServiceName = pickupFGCt.ServiceName,
						ServiceCode = "",
						CustomerCharge = 0
					};
					ratesResult.Add(pickup);
				}

				if (includePickupPM)
				{
					Rate pickupPM = new Rate()
					{
						CarrierName = pickupPMCt.CourierName,
						ServiceName = pickupPMCt.ServiceName,
						ServiceCode = "",
						CustomerCharge = 0
					};
					ratesResult.Add(pickupPM);
				}
			}

			#endregion add pickup rate TODO get rid of pickup

			return ratesResult;
		}

		public void SetCustomerPreferredCourier(IOrder order, CourierType courier, decimal price)
		{
		}

		/// <summary>
		/// TODO : PULL Carriers and services from Compdata Supply master
		/// </summary>
		/// <param name="location"></param>
		/// <returns></returns>
		public IList<CourierType> GetAllCouriers(SiteLocation location)
		{
			var result = new List<CourierType>();

			if (location != SiteLocation.NZ)
			{
				result.Add(CourierType.FastWay);
				result.Add(CourierType.StarTrack);
				result.Add(CourierType.AusPost);
			}

			result.Add(CourierType.TNT);

			return result;
		}

		public Dictionary<CourierType, decimal> GetAvailableRates2(IOrder order, Facility? facility)
		{
			var result = GetAvailableRates(order, facility, true);
			var uniqueSet = new Dictionary<CourierType,
			 decimal>();

			foreach (var d in result)
			{
				var key = new CourierType()
				{
					CourierName = d.CarrierName,
					ServiceName = d.ServiceName
				};
				var total = (decimal)(d.CustomerCharge);

				if (uniqueSet.ContainsKey(key))
				{
					uniqueSet[key] = Math.Max(uniqueSet[key], total);
				}
				else
				{
					uniqueSet.Add(key, total);
				}
			}
			return uniqueSet;
		}

		//very temporary - should really reference freight.GetCustomerAdjustedFreightPrice.
		private decimal GetCustomerAdjustedFreightPrice(Int32 CustomerID, decimal Price)
		{
			string sql = @"Select dbo.GetFreightPriceForCustomer(:CustomerID,:Price)";
			IQuery query = Session.CreateSQLQuery(sql);
			query.SetInt32("CustomerID", CustomerID);
			query.SetDecimal("Price", Price);
			return query.UniqueResult<decimal>();
		}

		public decimal GetCustomerFreightMarginFromCode(String freightCode)
		{
			var sql = @"SELECT CASE
                             WHEN EXISTS(SELECT 1 from dbo.CustomerFreightPricing where Code = :freightCode)
                       THEN(SELECT ISNULL(Margin, 0) from dbo.CustomerFreightPricing where Code = :freightCode)
                             ELSE 0
                             END ";

			IQuery query = Session.CreateSQLQuery(sql).SetAnsiString("freightCode", freightCode);
			var margin = Convert.ToDecimal(query.UniqueResult());
			return margin;
			//return query.UniqueResult<float>(); // throws error: ?
		}

		public IConfigurationApplication ConfigurationApplication
		{
			set;
			get;
		}

		public IJobApplication JobApplication
		{
			set;
			get;
		}
	}
}
