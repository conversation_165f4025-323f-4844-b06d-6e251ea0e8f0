<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2"
				   namespace="lep.jobmonitor"
				   assembly="lep"
				   auto-import="true"
				   default-cascade="all">

	<class name="ISchedule" table="Schedule" discriminator-value="null">
    <cache usage="read-write" />
    <id name="Id" type="Int32" unsaved-value="0">
      <generator class="identity" />
    </id>
    <discriminator column="Id" type="Int32" insert="false" />
    <timestamp name="DateModified" column="DateModified" />
    <many-to-one name="Template" column="JobOptionId" class="lep.job.IJobTemplate" not-null="true" cascade="none" />
    <property name="Folding" type="YesNo" not-null="true" />
    <property name="Facility" column="Facility" type="lep.GenericEnum`1[lep.job.Facility], lep" not-null="true" />
    <property name="StatusInt" type="Int32" not-null="true" />
    <property name="Status" type="lep.GenericEnum`1[lep.job.JobStatusOptions], lep" not-null="true" />
    <property name="ScheduleType" type="lep.GenericEnum`1[lep.ProductionTiming], lep" not-null="true" />
    <property name="Amber" type="Decimal" not-null="true" />
    <property name="Red" type="Decimal" not-null="true" />
    <property name="DateCreated" column="DateCreated" type="lumen.hibernate.type.DateTimeType, lumen" not-null="false" update="false" />
    <subclass name="lep.jobmonitor.impl.Schedule, lep" proxy="ISchedule" discriminator-value="not null" />
	</class>

  <!--
  Drop table Schedule;
  Create table Schedule(
    Id            int IDENTITY(1,1) NOT NULL,
    JobOptionId   int not null,
    [StatusInt]    int not null,
    [Status]        varchar(255) not null default(''),
    [ScheduleType]        varchar(255) not null default(''),
    Amber         int not null default(0),
    Red           int not null default(0),
	  [DateCreated]  [datetime] NOT NULL default(GETDATE()),
	  [DateModified] [datetime] NULL,
  )
  -->
</hibernate-mapping>