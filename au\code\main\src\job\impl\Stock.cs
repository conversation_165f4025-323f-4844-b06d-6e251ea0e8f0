using System;

namespace lep.job.impl
{
	public class Stock : IStock
    {
#pragma warning disable CS0169 // The field 'Stock.weight' is never used
        private decimal weight;
#pragma warning restore CS0169 // The field 'Stock.weight' is never used

        public Stock()
        {
        }

        public Stock(int id, string name)
        {
            Id = id;
            Name = name;
        }

        public int Id { get; set; }

        public string Name { get; set; }

		public string SType { set; get; }

		public bool IsCover { get; set; }
		public DateTime DateCreated { get; set; }

        public DateTime DateModified { get; set; }

        public int GSM { get; set; }

        public bool FG_Production { get; set; }

        public bool PM_Production { get; set; }

        public decimal Thickness { get; set; }
    }
}
