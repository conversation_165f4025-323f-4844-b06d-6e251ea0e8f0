using lep.job.impl;

using Serilog;
using lumen.csv;
using System;
using System.Collections.Generic;
using System.Reflection;

namespace lep.job.csv
{
	public class QuantityOptionImportHandler : BaseHandler
	{
		// private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);
		private Dictionary<string, int> fieldMap = new Dictionary<string, int>();

		private IJobApplication jobApp;

		private int rowNumber = 0;

		private bool seenFirstRow;

		public IDictionary<int, IJobOptionSpecQuantity> Result { get; } = new Dictionary<int, IJobOptionSpecQuantity>();

		public IJobApplication JobApplication
		{
			set { jobApp = value; }
		}

		public override void StartDocument()
		{
			seenFirstRow = false;
		}

		public override void EndDocument()
		{
		}

		public override void RowData(string[] values)
		{
			if (!seenFirstRow)
			{
				seenFirstRow = true;
				fieldMap = new Dictionary<string, int>(values.Length);
				for (var i = 0; i < values.Length; i++)
				{
					fieldMap[values[i].ToLower()] = i;
				}
			}
			else
			{
				rowNumber++;

				try
				{
					var rid = Convert.ToInt32(values[fieldMap["rid"]]);
					IJobOptionSpecQuantity jobOptionSpecQuantity = new JobOptionSpecQuantity();
					jobOptionSpecQuantity.Minium = "-" != values[fieldMap["min"]]
						? Convert.ToInt32(values[fieldMap["min"]])
						: 0;
					jobOptionSpecQuantity.Step1 = "-" != values[fieldMap["step1"]]
						? Convert.ToInt32(values[fieldMap["step1"]])
						: 0;
					jobOptionSpecQuantity.Change = "-" != values[fieldMap["change"]]
						? Convert.ToInt32(values[fieldMap["change"]])
						: 0;
					jobOptionSpecQuantity.Step2 = "-" != values[fieldMap["step2"]]
						? Convert.ToInt32(values[fieldMap["step2"]])
						: 0;
					jobOptionSpecQuantity.Change2 = "-" != values[fieldMap["change2"]]
						? Convert.ToInt32(values[fieldMap["change2"]])
						: 0;

					if (jobOptionSpecQuantity.Step1 != 0)
						if ((jobOptionSpecQuantity.Change - jobOptionSpecQuantity.Minium) % jobOptionSpecQuantity.Step1 !=
							0)
						{
							Log.Error(string.Format(
								"Range {0} Error: change:{1} - minim:{2} not divisible by step1:{3}",
								rid, jobOptionSpecQuantity.Change, jobOptionSpecQuantity.Minium,
								jobOptionSpecQuantity.Step1));
						}

					if (jobOptionSpecQuantity.Step2 != 0)
						if ((jobOptionSpecQuantity.Change2 - jobOptionSpecQuantity.Change) % jobOptionSpecQuantity.Step2 !=
							0)
						{
							Log.Error(
								string.Format("Range {0} Error: change2: {1} - change: {2} not divisible by step2:{3}",
									rid, jobOptionSpecQuantity.Change2, jobOptionSpecQuantity.Change,
									jobOptionSpecQuantity.Step2));
						}

					Result.Add(rid, jobOptionSpecQuantity);
				}
				catch (CsvException)
				{
					throw;
				}
				catch (Exception ex)
				{
					//Wrap the exception in another that gives the line number
					Log.Error(string.Format("Invalid value in row {0} : {1}", rowNumber, ex.Message), ex);
				}
			}

			base.RowData(values);
		}
	}
}
