﻿using System;

namespace lep.job.impl
{
	[Serializable]
    public class BindingOptionLookup : IBindingOptionLookup
    {
        private IBindingOption bindingOptionId;
        private int id;
        private int jobOptionId;
        private int maxTextPP;
        private int minTextPP;
        private int paperSizeId;
        private PrintType printType;
        private int stockId;

        public BindingOptionLookup()
        {
        }

        #region BindingOptionLookup Members

        public virtual int Id
        {
            get { return id; }
            set { id = value; }
        }

        public virtual int JobOptionId
        {
            get { return jobOptionId; }
            set { jobOptionId = value; }
        }

        public virtual int StockId
        {
            get { return stockId; }
            set { stockId = value; }
        }

        public virtual int PaperSizeId
        {
            get { return paperSizeId; }
            set { paperSizeId = value; }
        }

        public virtual int MinTextPP
        {
            get { return minTextPP; }
            set { minTextPP = value; }
        }

        public virtual int MaxTextPP
        {
            get { return maxTextPP; }
            set { maxTextPP = value; }
        }

        public virtual PrintType PrintType
        {
            get { return printType; }
            set { printType = value; }
        }

        public virtual IBindingOption BindingOptionId
        {
            get { return bindingOptionId; }
            set { bindingOptionId = value; }
        }

        #endregion BindingOptionLookup Members
    }
}