using System;

namespace lep.job
{
	public interface IArtwork
    {
        int Id { get; set; }
        ArtworkTypeOptions Type { get; set; }
        String Supplied { get; set; }
        String Ready { get; set; }
        String Preview { get; set; }
        String Position { get; set; }
        IJob <PERSON> { get; set; }
        string SuppliedCheckSum { get; set; }
        string PreviewdCheckSum { get; set; }
        string ReadyCheckSum { get; set; }
        bool? AACPerformed { get; set; }
    }
}