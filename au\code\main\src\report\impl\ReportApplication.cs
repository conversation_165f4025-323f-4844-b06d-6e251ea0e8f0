using lep.security;

using Serilog;
using NHibernate;
using NHibernate.Transform;
using System.Collections;
using System.Reflection;

namespace lep.report.impl
{
	public class ReportApplication : BaseApplication
	{
		// private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		#region constructors

		public ReportApplication(ISession sf, ISecurityApplication _securityApp) : base(sf, _securityApp)
		{
		}

		#endregion constructors

		public IList DoReport()
		{
			var results = Session.GetNamedQuery("ManagementReport_GetData")
				.SetResultTransformer(Transformers.AliasToBean(typeof(Report)))
				.List();
			return results;
		}
	}
}
