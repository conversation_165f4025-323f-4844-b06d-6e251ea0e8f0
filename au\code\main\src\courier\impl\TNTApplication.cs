using System;
using System.Collections.Generic;
using System.Collections;
using System.Text;
using System.Text.RegularExpressions;
using System.IO;
using Spring.Objects.Factory;
using NHibernate.Criterion;
using NHibernate;
using System.Linq;

using lep.courier.csv;
using lep.configuration;
using lep.order;
using lep.freight;
using lep.freight.impl;

namespace lep.courier.impl
{

	public class TNTApplication: ICourierApplication
    {
		private static readonly Common.Logging.ILog log = Common.Logging.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

		public TNTApplication()
        {
		}

		public IList<string> ImportRate(Stream file, out int processCount)
		{
			throw new Exception( "not implemented" );
		}

		IList<string> ImportPostcode(Stream file, out int processCount)
		{
			throw new Exception( "not implemented" );
		}

		public int GetCourierRating()
		{
			return 0;
		}

		public void SaveCourierRating(int rating)
		{
			throw new Exception( "not implemented" );
		}


		public decimal GetCourierSurCharge()
		{
			return 0;
		}

		public void SaveCourierSurCharge(decimal surcharge)
		{
			throw new Exception( "not implemented" );
		}

		public bool CalculatePrice( IPackDetail package,string postcode,StringBuilder log,out decimal price )
		{
			price = 0;
			return true;
		}
	}
}
