namespace lep.job
{
	public enum JobPrintOptions
    {
        [Description("Unprinted")]
        Unprinted = 0,

        [Description("Printed")]
        Printed = 1,

        [Description("Black & White")]
        BW = 2,

        [Description("Black")]
        Black = 3,

        [Description("Reflex Blue")]
        ReflexBlue = 4,
    }

    public enum ColourSides
    {
        [Description("None")]
        None = 0,

        [Description("One Side")]
        OneSide = 1,

        [Description("Both sides")]
        BothSides = 2,
    }
}
