using System.IO;
using System.Text;

namespace lep.macro.impl
{
	public abstract class MacroFilter : TextWriterFilter
    {
        private const int STATE_DEFAULT = 0;
        private const int STATE_OPEN = 1;
        private const int STATE_CLOSE = 2;
        private const int STATE_NAME = 3;
        private const int STATE_LITERAL = 4;
        private StringBuilder macro;

        private int state;

        public MacroFilter(TextWriter writer) : base(writer)
        {
        }

        public override void Write(char c)
        {
            switch (state)
            {
                case STATE_LITERAL:
                    base.Write(c);
                    break;

                case STATE_DEFAULT:
                    if (c == '[')
                    {
                        state = STATE_OPEN;
                        return;
                    }
                    base.Write(c);
                    break;

                case STATE_OPEN:
                    if (c == '[')
                    {
                        state = STATE_NAME;
                        macro = new StringBuilder();
                        return;
                    }
                    base.Write('[');
                    base.Write(c);
                    state = STATE_DEFAULT;
                    break;

                case STATE_NAME:
                    if (c == ']')
                    {
                        state = STATE_CLOSE;
                        return;
                    }
                    macro.Append(c);
                    break;

                case STATE_CLOSE:
                    if (c == ']')
                    {
                        state = STATE_LITERAL;
                        Write(ExpandMacro(macro.ToString()));
                        state = STATE_DEFAULT;
                        return;
                    }
                    base.Write(']');
                    base.Write(c);
                    state = STATE_NAME;
                    break;
            }
        }

        protected abstract string ExpandMacro(string name);
    }
}