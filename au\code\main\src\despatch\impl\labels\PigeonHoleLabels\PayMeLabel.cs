using System.Drawing;
using System.Drawing.Printing;

namespace lep.despatch.impl.label
{
    public class PayMeLabel : BasePigeonHoleLabel
    {
        #region Constructors

        public PayMeLabel()
        {
            WatermarkStamp = "PAY ME";
            WatermarkStampColor = Color.Red;
        }

        #endregion Constructors

        #region Protected Methods

        protected override void OnPrintPage(PrintPageEventArgs e)
        {
            base.OnPrintPage(e);
        }

        protected override void PrintExtraThingsAfterGrid(Graphics g)
        {
        }

        #endregion Protected Methods
    }
}