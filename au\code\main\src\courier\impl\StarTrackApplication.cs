using System;
using System.Collections.Generic;
using System.Collections;
using System.Text;
using System.Text.RegularExpressions;
using System.IO;
using Spring.Objects.Factory;
using NHibernate.Criterion;
using NHibernate;
using System.Linq;

using lep.courier.csv;
using lep.configuration;
using lep.order;
using lep.freight;
using lep.freight.impl;
using lep.job;


/*
namespace lep.courier.impl
{

	/// <summary>
    /// 
    /// </summary>
	public class StarTrackApplication: BaseCourierApplication, ICourierApplication
    {
		private static readonly Common.Logging.ILog log = Common.Logging.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);


		public StarTrackApplication() : base(CourierType.StarTrack)
        {
		}

        public bool CalculatePrice(Facility facility, IList<IPackage> packages, string postcode, decimal totalWeight, bool hasSkid, StringBuilder log, out decimal price)
		{
			price = 0;
			log.AppendLine( "\n\nStar Track calculation" );
            return CalculatePrice(facility, packages, FindZone(postcode), GetCourierSurCharge(), totalWeight, hasSkid, log, out price);
		}

        private bool CalculatePrice(Facility facility, IList<IPackage> packages, string zone, decimal surcharge, decimal totalWeight, bool hasSkid, StringBuilder log, out decimal price)
		{
			
			price = 0;
			log.AppendFormat("zone code: {0}\n", zone);

            StarTrackRate rate = FindRate(facility, zone);
			if (rate == null) {
				log.AppendLine( "destination not exist" );
				return false;
			}

			if (hasSkid) {
				log.AppendLine( "    pack everything in skid" );
				ICarton skid = FreightApplication.GetCarton( CartonType.Skid );
                price = FindCharge(Convert.ToDecimal(skid.Height * skid.Width * skid.Depth) / 1000000000, totalWeight, rate, log);
			} else if (packages.All( p => p.Carton.CartonType == CartonType.BC )) {
				decimal smallMax = Convert.ToDecimal( ConfigurationApplication.GetValue( Configuration.StarTrackSmallWeight ) );
				decimal largeMax = Convert.ToDecimal( ConfigurationApplication.GetValue( Configuration.StarTrackLargeWeight ) );
				decimal smallPrice = Convert.ToDecimal( ConfigurationApplication.GetValue( Configuration.StarTrackSmallPrice ) );
				decimal largePrice = Convert.ToDecimal( ConfigurationApplication.GetValue( Configuration.StarTrackLargePrice ) );

				if (!PackInBag( packages,largeMax,smallMax,smallPrice,largePrice,log,out price )) {
					return false;
				}

			} else {
				decimal totalVolume = 0;
				foreach (Package pack in packages) {
					totalVolume += Convert.ToDecimal( pack.Carton.Height * pack.Carton.Width * pack.Carton.Depth ) / 1000000000;
				}
				price = FindCharge( totalVolume,totalWeight,rate,log );
			}
			log.AppendFormat( "total price (price x surcharge): {0} x {1} = ${2}\n",AuditDecimal(price),surcharge,AuditDecimal(Convert.ToDecimal( price * surcharge )) );
			price = price * surcharge;

			return true;
		}

		public decimal FindCharge( decimal volume, decimal weight, StarTrackRate rate, StringBuilder log )
		{
			decimal weightPrice = weight * rate.BreakRate + rate.BasicCharge;
			log.AppendFormat( "    price by weight: {0}KG x {1} + {2} = ${3}\n",AuditDecimal(weight),AuditDecimal(rate.BreakRate),AuditDecimal(rate.BasicCharge),AuditDecimal(weightPrice) );

			decimal volumePrice = volume * rate.CubicConvFactor * rate.BreakRate + rate.BasicCharge;
			log.AppendFormat( "    price by volume: {0} * {1} * {2} + {3} = ${4}\n",AuditDecimal(volume),AuditDecimal( rate.CubicConvFactor ),AuditDecimal( rate.BreakRate ),AuditDecimal( rate.BasicCharge ),AuditDecimal( volumePrice ) );

			log.AppendFormat( "    price per pack: max({0}, {1}) = ${2}\n\n",AuditDecimal(weightPrice),AuditDecimal(volumePrice),AuditDecimal(Math.Max( weightPrice,volumePrice )) );

			return Math.Max( weightPrice,volumePrice );
		}

		private StarTrackRate FindRate( Facility facility, string destination )
		{
			if (String.IsNullOrEmpty( destination )) {
				return null;
			}

			return Session.CreateCriteria( typeof( StarTrackRate ) )
				.Add( Expression.Eq( "AccountNumber",AccountNumber ) )
                .Add(Expression.Eq("OriginZone", facility == Facility.PM ? MELBOURNE_ORIGIN_CODE : FOREST_GLEN_ORIGIN_CODE))
				.Add( Expression.Eq( "ServiceCode",ServiceCode ) )
				.Add( Expression.Eq( "DestinationZone",destination ) )
				.SetMaxResults( 1 )
				.UniqueResult<StarTrackRate>();
		}

		private string FindZone( string postcode )
		{
			StarTrackPostcode data = Session.CreateCriteria( typeof( StarTrackPostcode ) )
				.Add( Expression.Eq( "Postcode",postcode ) )
				.SetMaxResults( 1 )
				.UniqueResult<StarTrackPostcode>();

			if (data != null) {
				if (!String.IsNullOrEmpty( data.DirectZone )) {
					return data.DirectZone;
				} else if (!String.IsNullOrEmpty( data.AltOnForwardZone )) {
					return data.AltOnForwardZone;
				} else if (!String.IsNullOrEmpty( data.OnForwardZone )) {
					return data.OnForwardZone;
				}
			}

			return String.Empty;
		}

		public string AccountNumber { get; set; }
		public string ServiceCode { get; set; }

	}
}


*/
