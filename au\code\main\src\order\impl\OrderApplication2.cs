using lep.address.impl;
using lep.configuration;
using lep.content;
using lep.courier;
using lep.despatch;
using lep.despatch.impl;
using lep.email;
using lep.extensionmethods;
using lep.freight;
using lep.job;
using lep.jobmonitor;
using lep.pricing;
using lep.promotion;
using lep.run;
using lep.security;
using lep.user;
using NHibernate;
using NHibernate.Criterion;
using Serilog;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Xml.Linq;
using System.Xml.XPath;
using lep.job.impl;
using static lep.job.Facility;
using static lep.job.JobCelloglazeOptions;
using static lep.job.JobStatusOptions;
using static lep.job.JobTypeOptions;
using static lep.OrderPaymentStatusOptions;
using static lep.OrderStatusOptions;
using static lep.PaymentTermsOptions;
using static NHibernate.Criterion.Restrictions;
using static NHibernate.Impl.CriteriaImpl;

namespace lep.order.impl
{
	/// <summary>
	/// </summary>
	public partial class OrderApplication : BaseApplication, IOrderApplication
	{

		public ICriteria OrderCriteria(ICustomerUser customer, List<OrderStatusOptions> statuslist, bool attention,
			bool cancel)
		{
			var criteria = Session.CreateCriteria(typeof(IOrder), "o").Add(Eq("Customer", customer));

			var disjunction = new Disjunction()
				.Add(Eq("ReadyArtworkApproval", JobApprovalOptions.NeedsApproval))
				.Add(Eq("SupplyArtworkApproval", JobApprovalOptions.Rejected))
				.Add(Eq("QuoteNeedApprove", true));

			var jobCriteria = DetachedCriteria.For(typeof(IJob), "j");
			//jobCriteria.Add(Restrictions.Eq("Enable", true));
			jobCriteria.Add(disjunction);
			jobCriteria.SetProjection(Projections.Property("Order"));
			jobCriteria.Add(EqProperty("Order.Id", "o.Id"));

			ICriterion attentionExp = And(Eq("Status", OrderStatusOptions.Open),
				Subqueries.Exists(jobCriteria));

			var criteriaOr = new Disjunction();
			if (attention) criteriaOr.Add(In("Status", statuslist));
			if (statuslist.Count > 0) criteriaOr.Add(In("Status", statuslist));

			//if (cancel) criteriaOr.Add(Restrictions.Eq("Enable", false));
			//else criteria.Add(Restrictions.Eq("Enable", true));

			criteria.Add(criteriaOr);
			return criteria;
		}

		public ICriteria OrderCriteriaCust(ICustomerUser customer, string orderOrJobNum, List<OrderStatusOptions> statuslist,
			List<IJobTemplate> types, bool? cancel = null, RunCelloglazeOptions? cello = null, IPaperSize size = null,
			IStock stock = null, bool? rejected = null, bool? requireApproval = null, bool? quoteRequired = null, bool isWhiteLabel = false,
			int? wlCustomerId = null, bool? isWlOrderPaidFor = null)
		{
			//var criteria = Session.CreateCriteria(typeof(IOrder), "o").Add(Restrictions.Eq("Customer", customer));

			var criteria = Session.CreateCriteria(typeof(IOrder), "o");

			//.CreateAlias("o.Jobs", "j")
			//.SetFetchMode("o.Jobs", FetchMode.Eager)
			//.SetFetchMode("j.Runs", FetchMode.Eager)
			//.SetFetchMode("j.Artworks", FetchMode.Eager)

			if (customer != null)
			{
				criteria.Add(Eq("Customer", customer));
			}

			if (wlCustomerId != null)
			{
				criteria.Add(Eq("WLCustomerId", wlCustomerId));
			}

			if (isWlOrderPaidFor != null)
			{
				criteria.Add(Eq("IsWLOrderPaidFor", isWlOrderPaidFor.Value));
			}

			if (string.IsNullOrEmpty(orderOrJobNum))
			{
				if (statuslist?.Count > 0)
				{
					criteria.Add(In("Status", statuslist));
				}

				//if (cancel.HasValue && cancel.Value == false) {
				//	criteria.Add(Restrictions.Eq("Enable", true));
				//}
			}

			var jobCriteria = DetachedCriteria.For(typeof(IJob), "j");
			jobCriteria.CreateAlias("j.Order", "jo");
			if (types?.Count > 0)
				jobCriteria.Add(In("j.Template", types));

			if (stock != null)
				jobCriteria.Add(Eq("j.Stock", stock));

			if (size != null)
				jobCriteria.Add(Eq("j.FinishedSize.PaperSize", size));

			if (rejected.HasValue && rejected.Value)
				jobCriteria.Add(Or(
					Eq("j.SupplyArtworkApproval", JobApprovalOptions.Rejected),
					Eq("j.ReadyArtworkApproval", JobApprovalOptions.Rejected)
				));

			if (requireApproval.HasValue && requireApproval.Value)
				jobCriteria.Add(
					Or(Eq("j.ReadyArtworkApproval", JobApprovalOptions.NeedsApproval),
						Eq("j.QuoteNeedApprove", true)));

			if (quoteRequired.HasValue && quoteRequired.Value) jobCriteria.Add(Eq("j.Price", string.Empty));

			if (isWhiteLabel)
			{
				jobCriteria.Add(Eq("j.IsWhiteLabel", true));
			}

			jobCriteria.SetProjection(Projections.Alias(Projections.Property("Order"), "o1"));
			jobCriteria.Add(EqProperty("Order.Id", "o.Id"));

			if (!string.IsNullOrEmpty(orderOrJobNum) && orderOrJobNum.Trim().Length > 2)
			{
				orderOrJobNum = orderOrJobNum.Trim();
				var d = new Disjunction();

				var orderOrJobNumInt = 0;
				int.TryParse(orderOrJobNum, out orderOrJobNumInt);

				if (orderOrJobNumInt != 0)
				{
					d.Add(Eq("j.Id", orderOrJobNumInt));
					d.Add(Eq("Order.Id", orderOrJobNumInt));
					d.Add(Like(Projections.Cast(NHibernateUtil.String, Projections.Property("j.Id")), orderOrJobNumInt.ToString(), MatchMode.Anywhere));
					d.Add(Like(Projections.Cast(NHibernateUtil.String, Projections.Property("Order.Id")), orderOrJobNumInt.ToString(), MatchMode.Anywhere));
				}

				d.Add(Like("j.Name", orderOrJobNum, MatchMode.Anywhere));
				d.Add(Like("jo.PurchaseOrder", orderOrJobNum, MatchMode.Anywhere));

				jobCriteria.Add(d);
			}

			criteria.Add(Subqueries.Exists(jobCriteria));
			criteria.Add(Restrictions.Eq("o.IsDeleted", false));


			return criteria;
		}

		//ox1
		public ICriteria OrderCriteria(string customer, string ordernumber, string jobnumber, string status, bool newOrder,
			bool onHold, bool urgent, bool corrected, bool prePay, bool open, bool awaitingPay, bool cancelled,
			bool requireApproval, bool quoteRequired, bool rejected, List<IJobTemplate> types, List<IJobTemplate> nonTypes,
			RunCelloglazeOptions? cello, IPaperSize size, IStock stock, bool isOrderWithDigitalJobs, bool isOrderWithOutworkJob,
			Facility? facility = null, bool isWhiteLabel = false, bool isPaidFor = false, bool isUnableToMeetPrice = false,
			bool hideOnHoldOrders = false, bool showOnlyOnHoldOrders = false)
		{
			var idsGiven = false;
			var jobIdGiven = false;
			if (rejected || isUnableToMeetPrice)
			{
				open = true;
				newOrder = false;
			}

			var criteria = Session.CreateCriteria(typeof(IOrder), "o");
			criteria.CreateAlias("o.Customer", "c");

			if (!string.IsNullOrEmpty(customer))
				criteria.Add(Disjunction().Add(Like("c.Name", customer, MatchMode.Start))
					//.Add(Like("c.Contact1.Phone", customer, MatchMode.Start))
					//.Add(Like("c.Contact1.Mobile", customer, MatchMode.Start))
					//.Add(Restrictions.Like("c.ContactsJsonStr", customer, MatchMode.Anywhere))
					.Add(Like("c.Username", customer, MatchMode.Start)));
			if (!string.IsNullOrEmpty(ordernumber))
			{
				ordernumber = ordernumber.Trim();
				var order = 0;
				int.TryParse(ordernumber, out order);
				criteria.Add(Eq("o.Id", order));

				//criteria.Add(
				//	Restrictions.Disjunction()
				//		.Add(Restrictions.Like(Projections.Cast(NHibernateUtil.String, Projections.Property("o.Id")), order.ToString(),
				//			MatchMode.Anywhere))
				//		.Add(Restrictions.Eq("o.Id", order))
				//);

				idsGiven = true;
			}

			//if (isPaidButNotSubmitted)
			//{
			//	criteria.Add(And(
			//					Eq("o.PaymentStatus", OrderPaymentStatusOptions.Paid),
			//					Eq("o.Status", OrderStatusOptions.Open)
			//				));
			//	status = "";
			//}

			if (!string.IsNullOrEmpty(status))
				criteria.Add(Eq("o.Status", Enum.Parse(typeof(OrderStatusOptions), status, true)));
			if (!idsGiven)
				if (urgent || !string.IsNullOrEmpty(jobnumber) || newOrder || corrected || types.Count > 0 ||
					nonTypes.Count > 0 || requireApproval || quoteRequired || cello.HasValue || size != null ||
					stock != null || isOrderWithDigitalJobs || isOrderWithOutworkJob || rejected || facility != null || isWhiteLabel ||
					isPaidFor || isUnableToMeetPrice)
				{
					var jobCriteria = DetachedCriteria.For(typeof(IJob), "j");

					if (!string.IsNullOrEmpty(jobnumber))
					{
						jobnumber = jobnumber.Trim();
						var job = 0;
						int.TryParse(jobnumber, out job);

						if (job != 0)
						{
							jobIdGiven = true;
							jobCriteria.Add(Eq("j.Id", job));
							//jobCriteria.Add(
							//	Restrictions.Disjunction()
							//		.Add(Restrictions.Like(Projections.Cast(NHibernateUtil.String, Projections.Property("j.Id")), job.ToString(),
							//			MatchMode.Anywhere))
							//		.Add(Restrictions.Eq("j.Id", job))
							//);
						}
					}

					if (!jobIdGiven)
					{
						if (facility != null) jobCriteria.Add(Eq("j.Facility", facility));

						if (urgent) jobCriteria.Add(Eq("j.Urgent", true));

						if (newOrder || corrected)
						{
							criteria.Add(Eq("o.Status", OrderStatusOptions.Submitted));
							if (newOrder != corrected)
								if (newOrder) jobCriteria.Add(Eq("j.HasRejectedBefore", false));
								else if (corrected) jobCriteria.Add(Eq("j.HasRejectedBefore", true));
						}

						if (rejected)
							jobCriteria.Add(Or(
								Eq("j.SupplyArtworkApproval", JobApprovalOptions.Rejected),
								Eq("j.ReadyArtworkApproval", JobApprovalOptions.Rejected)
							));

						if (types.Count > 0) jobCriteria.Add(In("j.Template", types));

						if (nonTypes.Count > 0) jobCriteria.Add(Not(In("j.Template", nonTypes)));

						if (requireApproval)
							jobCriteria.Add(
								Or(Eq("j.ReadyArtworkApproval", JobApprovalOptions.NeedsApproval),
									Eq("j.QuoteNeedApprove", true)));

						if (quoteRequired) jobCriteria.Add(Eq("j.Price", string.Empty));

						if (cello.HasValue)
						{
							var cellos = CelloUtils.ToJobCelloGlaze(cello.Value);
							jobCriteria.Add(Eq("j.FrontCelloglaze", cellos[0]));
							if (cellos[0] != Foil)
								jobCriteria.Add(Eq("j.BackCelloglaze", cellos[1]));
						}

						if (stock != null) jobCriteria.Add(Eq("j.Stock", stock));

						if (size != null) jobCriteria.Add(Eq("j.FinishedSize.PaperSize", size));

						//jobCriteria.Add(Restrictions.Eq("j.Enable", !cancelled));

						if (isOrderWithDigitalJobs) jobCriteria.Add(Eq("j.PrintType", PrintType.D));
						if (isOrderWithOutworkJob)
							jobCriteria.Add(Eq("j.InvolvesOutwork", true));

						if (isWhiteLabel)
							jobCriteria.Add(Eq("j.IsWhiteLabel", true));

						if (isUnableToMeetPrice)
						{
							jobCriteria.Add(Eq("j.Status", UnableToMeetPrice));
						}

					}
					jobCriteria.SetProjection(Projections.Alias(Projections.Property("Order"), "o1"));
					jobCriteria.Add(EqProperty("Order.Id", "o.Id"));
					criteria.Add(Subqueries.Exists(jobCriteria));
				}

			if (!idsGiven && !jobIdGiven)
			{
				/* todo iwh: add back later after performance inspection	*/
				if (!isUnableToMeetPrice && !quoteRequired)
					criteria.Add(Expression.Sql(@" not exists (select j.id from job j where j.orderid = {alias}.id and j.price = '')")); //and j.IsEnable = 'Y'

				//criteria.Add(Restrictions.Eq("o.Enable", !cancelled));

				if (onHold) criteria.Add(Eq("c.PaymentTerms", OnHold));

				if (prePay) criteria.Add(Eq("c.PaymentTerms", PrePay));

				// Apply On Hold filtering based on job.ProofStatus
				if (hideOnHoldOrders)
				{
					var jobCriteriaOnHold = DetachedCriteria.For(typeof(IJob), "joh");
					jobCriteriaOnHold.Add(Eq("joh.ProofStatus", JobProofStatus.OnHold));
					jobCriteriaOnHold.SetProjection(Projections.Property("Order"));
					jobCriteriaOnHold.Add(EqProperty("Order.Id", "o.Id"));
					criteria.Add(Subqueries.NotExists(jobCriteriaOnHold));
				}
				else if (showOnlyOnHoldOrders)
				{
					var jobCriteriaOnHold = DetachedCriteria.For(typeof(IJob), "joh");
					jobCriteriaOnHold.Add(Eq("joh.ProofStatus", JobProofStatus.OnHold));
					jobCriteriaOnHold.SetProjection(Projections.Property("Order"));
					jobCriteriaOnHold.Add(EqProperty("Order.Id", "o.Id"));
					criteria.Add(Subqueries.Exists(jobCriteriaOnHold));
				}

				if (awaitingPay)
					criteria.Add(Eq("o.PaymentStatus", AwaitingPayment));
				else if (isPaidFor)
					criteria.Add(Eq("o.PaymentStatus", OrderPaymentStatusOptions.Paid));

				if (!idsGiven)
					if (open) criteria.Add(Eq("o.Status", OrderStatusOptions.Open));
					else criteria.Add(Not(Eq("o.Status", OrderStatusOptions.Open)));

				/*
				if (String.IsNullOrEmpty( ordernumber ) && String.IsNullOrEmpty( jobnumber )) {
					criteria.Add( Expression.Ge( "DateModified",DateTime.Now.AddDays( -14 ) ) );
				}
				 */
			}
			/* todo iwh: add back later after performance inspection */
			if (string.IsNullOrEmpty(customer) && string.IsNullOrEmpty(ordernumber) && string.IsNullOrEmpty(jobnumber) && string.IsNullOrEmpty(status) &&
				!newOrder && !onHold && !urgent && !corrected && !open && !awaitingPay && !cancelled && !requireApproval &&
				!quoteRequired && types.Count == 0 && nonTypes.Count == 0 && !cello.HasValue)
			{
				var days = int.Parse(_configApp.GetValue(Configuration.ArchiveOrdersPeriod));
				criteria.Add(Ge("DateModified", DateTime.Now.Date.AddDays(-days)));
			}


			return criteria;
		}

		public IList<IOrder> FindEmptyOrder()
		{
			var jobCriteria = DetachedCriteria.For(typeof(IJob), "j");
			jobCriteria.SetProjection(Projections.Property("Order"));
			jobCriteria.Add(EqProperty("Order.Id", "o.Id"));

			return Session.CreateCriteria(typeof(IOrder), "o")
				.Add(Eq("o.Status", OrderStatusOptions.Open))
				.Add(Subqueries.NotExists(jobCriteria))
				.List<IOrder>();
		}

		public IList<IOrder> GetRequireAttentionOrders(ICustomerUser customer)
		{
			var disjunction = new Disjunction()
				.Add(Eq("ReadyArtworkApproval", JobApprovalOptions.NeedsApproval))
				.Add(Eq("SupplyArtworkApproval", JobApprovalOptions.Rejected))
				.Add(In("ArtworkStatus", new[] { ArtworkStatusOption.LATER }))
				.Add(Eq("QuoteNeedApprove", true));

			var jobCriteria = DetachedCriteria.For(typeof(IJob), "j");
			jobCriteria.Add(disjunction);
			//jobCriteria.Add(Restrictions.Eq("Enable", true));
			jobCriteria.SetProjection(Projections.Property("Order"));
			jobCriteria.Add(EqProperty("Order.Id", "o.Id"));

			return Session.CreateCriteria(typeof(IOrder), "o")
				.Add(Eq("Customer", customer))
				.Add(Eq("o.Status", OrderStatusOptions.Open))
				.Add(Subqueries.Exists(jobCriteria))
				.List<IOrder>();
		}





	}
}
