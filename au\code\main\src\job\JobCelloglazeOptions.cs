namespace lep.job
{
	public enum JobCelloglazeOptions
    {
        <PERSON>,
        <PERSON><PERSON>,
        <PERSON>,

        <PERSON>,
        Emboss,
        Foil,
        SpotUV,

        EmbossFoil,

        EmbossedGlossFront,
        EmbossedMattFront,

		SpotUVFrontMattFront,

        MattAntiScuff,
	}

    public enum Envelope
    {
        White,
        <PERSON>,
        Green
    }

    public enum EnvelopeType
    {
        [Description("Window/Peel & Seal")]
        WindowPeelNSeal,

        [Description("Plain Face/Peel & Seal")]
        PlainFacePeelNSeal,

        [Description("Window/Self Seal")]
        WindowSelfSeal,

        [Description("Plain Face/Self Seal")]
        PlainFaceSelfSeal
    }
}
