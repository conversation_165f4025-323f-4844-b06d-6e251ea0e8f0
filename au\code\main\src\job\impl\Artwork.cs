namespace lep.job.impl
{
	public class Artwork : IArtwork
    {
        public Artwork()
        {
        }

        public Artwork(string position, IJob job)
        {
            Position = position;
            Job = job;
        }

        #region IJobArtwork Members

        public virtual int Id { get; set; }
        public virtual ArtworkTypeOptions Type { get; set; }
        public virtual string Supplied { get; set; } = "";
        public virtual IJob Job { get; set; }
        public virtual string Ready { get; set; } = null;
        public virtual string Preview { get; set; } = null;
        public virtual string Position { get; set; }
        public virtual string SuppliedCheckSum { get; set; } = "";
        public virtual string PreviewdCheckSum { get; set; } = "";
        public virtual string ReadyCheckSum { get; set; } = "";
        public virtual bool? AACPerformed { get; set; }

        #endregion IJobArtwork Members
    }
}