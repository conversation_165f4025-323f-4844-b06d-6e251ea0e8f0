﻿using lep.job;
using System;
using System.Collections.Generic;

namespace lep.order
{
	public interface IOrderConNote
	{
		string ConNote { get; set; }
		DateTime DateCreated { get; set; }
		DateTime DateModified { get; set; }
		int Id { get; set; }
		int IsEmailGenerated { get; set; }
		int OrderId { get; set; }
		IOrder Order { get; set; }
		Facility DispatchFacility { get; set; }

		string CarrierName { get; set; }
		string CarrierService { get; set; }

		IList<string> TrackingLabels { get; set; }
	}
}
